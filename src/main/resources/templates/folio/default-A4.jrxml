<!-- Created with Jaspersoft Studio version 7.0.1.final using JasperReports Library version 7.0.1-573496633c2b4074e32f433154b543003f7d2498  -->
<jasperReport name="test" language="java" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="a476954b-0f9d-4a7a-9c87-50423fc8d5c8">
	<style name="NormalStyle" mode="Transparent"/>
	<style name="AlternateStyle" mode="Opaque" backcolor="#E6E6E6"/>
	<parameter name="resv" class="com.cw.entity.Reservation"/>
	<parameter name="hotelInfo" class="com.cw.pojo.entity.HotelInfoEntity"/>
	<parameter name="folio" class="com.cw.service.config.print.folio.FolioCalcFunc"/>
	<field name="acc" class="com.cw.entity.GuestAccounts"/>
	<pageHeader height="114">
		<element kind="textField" uuid="60af670c-59a9-4625-a97c-c591df5e7727" x="0" y="0" width="555" height="25" fontSize="15.0" blankWhenNull="true" bold="true" hTextAlign="Center" vTextAlign="Top">
			<expression><![CDATA[$P{hotelInfo}.getHotelName() + "客人账单"]]></expression>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="3fc26c87-62a9-411a-8a54-7fafa347930c" x="0" y="25" width="555" height="20" fontSize="8.0" blankWhenNull="true" bold="false" hTextAlign="Center" vTextAlign="Middle">
			<expression><![CDATA["地址: " + $P{hotelInfo}.getAddress() + " 电话: " + $P{hotelInfo}.getTelephone()]]></expression>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.5"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="51e0a382-03e0-496b-af9f-3dcc3710d4b4" x="0" y="45" width="80" height="17" fontSize="9.0" blankWhenNull="true" bold="false" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA["宾客姓名"]]></expression>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="1d5d767c-6df3-4fee-80f1-c964e1d5fdaa" x="80" y="45" width="200" height="17" fontSize="9.0" blankWhenNull="true" bold="false" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA[$P{resv}.getGuestName()]]></expression>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="0bfae8a7-01d4-41aa-a20b-0a9dfd612f26" x="280" y="45" width="75" height="17" fontSize="9.0" blankWhenNull="true" bold="false" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA["账单号"]]></expression>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="afff33e2-0d17-4742-a757-08f59681b6ea" x="354" y="45" width="200" height="17" fontSize="9.0" blankWhenNull="true" bold="false" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA[$P{resv}.getReservationNumber()]]></expression>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="ad0e5c14-2fc3-44d3-b4c1-c124da3e07ff" x="0" y="62" width="80" height="17" fontSize="9.0" blankWhenNull="true" bold="false" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA["房　　号"]]></expression>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="694b5fb2-f893-4821-b54c-133550db6f50" x="80" y="62" width="200" height="17" fontSize="9.0" blankWhenNull="true" bold="false" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA[$P{resv}.getRoomNumber()]]></expression>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="68ff7a69-4303-46b5-b1f1-ffa9ea2d10b5" x="280" y="62" width="75" height="17" fontSize="9.0" blankWhenNull="true" bold="false" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA["房　价"]]></expression>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="37a01b75-4e80-4477-b9ab-9b8d1e03aded" x="354" y="62" width="200" height="17" fontSize="9.0" blankWhenNull="true" bold="false" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA[$P{resv}.getPrice()]]></expression>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="29f4e67c-1ae8-49fe-94c4-491d6a20ab88" x="0" y="79" width="80" height="17" fontSize="9.0" blankWhenNull="true" bold="false" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA["来店时间"]]></expression>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="960c56e8-2896-419f-bc68-d4356a9f78af" x="80" y="79" width="200" height="17" fontSize="9.0" pattern="yyyy-MM-dd" blankWhenNull="true" bold="false" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA[$P{resv}.getArrivalDate()]]></expression>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="5054f42d-be6d-475a-a2be-ac607b8248f0" x="280" y="79" width="75" height="17" fontSize="9.0" blankWhenNull="true" bold="false" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA["人　数"]]></expression>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="7d49ca15-7aa3-4c56-bb81-b77d93b2b26b" x="354" y="79" width="200" height="17" fontSize="9.0" blankWhenNull="true" bold="false" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA[$P{resv}.getPersonTotal()]]></expression>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="7238a373-a3a1-447a-9301-045f903c7f8d" x="0" y="96" width="80" height="17" fontSize="9.0" blankWhenNull="true" bold="false" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA["离店时间"]]></expression>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="2e529940-a3c0-4fae-8315-9712c2a358c1" x="80" y="96" width="200" height="17" fontSize="9.0" pattern="yyyy-MM-dd" blankWhenNull="true" bold="false" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA[$P{resv}.getDepartureDate()]]></expression>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="c40ba717-119d-4906-8d0b-c21d3a8f13f9" x="280" y="96" width="75" height="17" fontSize="9.0" blankWhenNull="true" bold="false" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA["操作员"]]></expression>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="55b8ff7d-7b52-4af2-9099-e703b2b3e977" x="354" y="96" width="200" height="17" fontSize="9.0" blankWhenNull="true" bold="false" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA[$P{folio}.getOptUser()]]></expression>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
	</pageHeader>
	<columnHeader height="25">
		<element kind="textField" uuid="c10e17f7-c299-4c8f-ba8f-a3f5c1c8ffd7" x="0" y="0" width="80" height="25" fontSize="11.0" linkType="None" linkTarget="Self" blankWhenNull="true" bold="true" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA["日期Date"]]></expression>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<box padding="0">
				<topPen lineWidth="0.5"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.5"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="bbb026a2-ca35-4120-893d-a3ab68c4d579" x="80" y="0" width="275" height="25" fontSize="11.0" linkType="None" linkTarget="Self" blankWhenNull="true" bold="true" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA["摘要Remark"]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<box padding="0">
				<topPen lineWidth="0.5"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.5"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="e5dd61f0-0a2d-46fd-aae1-adbf70a06471" x="355" y="0" width="100" height="25" fontSize="11.0" linkType="None" linkTarget="Self" blankWhenNull="true" bold="true" hTextAlign="Right" vTextAlign="Middle">
			<expression><![CDATA["消费charge"]]></expression>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<box padding="0">
				<topPen lineWidth="0.5"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.5"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="8c6f339d-9a24-4b12-bd94-f69a1ed7b1fc" x="455" y="0" width="100" height="25" fontSize="11.0" linkType="None" linkTarget="Self" blankWhenNull="true" bold="true" hTextAlign="Right" vTextAlign="Middle">
			<expression><![CDATA["付款Payment"]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<box padding="0">
				<topPen lineWidth="0.5"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.5"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</columnHeader>
	<detail>
		<band height="20">
			<element kind="textField" uuid="60c14820-5546-4dfb-b0b3-960449985ac7" x="0" y="0" width="80" height="20" fontSize="11.0" blankWhenNull="true" bold="false" hTextAlign="Left" vTextAlign="Middle">
				<expression><![CDATA[$F{acc}.getBusiness_date()]]></expression>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box padding="0">
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="textField" uuid="98ba12f6-c1d7-4b39-a5fb-04c31bbbfe47" x="80" y="0" width="275" height="20" fontSize="11.0" blankWhenNull="true" bold="false" hTextAlign="Left" vTextAlign="Middle">
				<expression><![CDATA[$F{acc}.getDescription() + " " + (null == $F{acc}.getRemark() ? "" : $F{acc}.getRemark())]]></expression>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box padding="0">
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="textField" uuid="8d187697-aa34-4647-b123-a809126474da" x="355" y="0" width="100" height="20" fontSize="11.0" blankWhenNull="true" bold="false" hTextAlign="Right" vTextAlign="Middle">
				<expression><![CDATA[$F{acc}.getPrice().multiply($F{acc}.getQuantity())]]></expression>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box padding="0">
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="textField" uuid="8f302ff1-72b8-4276-8c09-2d7f8d57d8e6" x="455" y="0" width="100" height="20" fontSize="11.0" blankWhenNull="true" bold="false" hTextAlign="Right" vTextAlign="Middle">
				<expression><![CDATA[$F{acc}.getCredit()]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box padding="0">
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
	</detail>
	<pageFooter height="15">
		<element kind="textField" uuid="1f2e238a-f49c-4dc7-9a3b-f3e92213817c" x="0" y="0" width="555" height="15" fontSize="9.0" blankWhenNull="true" bold="false" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA["打印时间："+new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:dd").format(new java.util.Date())]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="1c4be378-68f5-4ad9-b02a-edf8cb54c790" x="0" y="0" width="555" height="15" fontSize="8.0" evaluationTime="Master" blankWhenNull="true" bold="false" hTextAlign="Right" vTextAlign="Middle">
			<expression><![CDATA[$V{MASTER_CURRENT_PAGE} + " / " + $V{MASTER_TOTAL_PAGES}]]></expression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<box padding="0">
				<topPen lineWidth="0.5"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageFooter>
	<lastPageFooter height="35">
		<element kind="textField" uuid="3384ad78-86d3-4177-9968-018afaa33a28" x="0" y="20" width="555" height="15" fontSize="9.0" blankWhenNull="true" bold="false" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA["打印时间："+new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:dd").format(new java.util.Date())]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="8fa44b66-09b9-4ec0-8e3e-2eb51d93ad98" x="0" y="20" width="555" height="15" fontSize="8.0" evaluationTime="Master" blankWhenNull="true" bold="false" hTextAlign="Right" vTextAlign="Middle">
			<expression><![CDATA[$V{MASTER_CURRENT_PAGE} + " / " + $V{MASTER_TOTAL_PAGES}]]></expression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<box padding="0">
				<topPen lineWidth="0.5"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="54d39fdb-be31-4244-914c-2351fff80c9a" x="0" y="0" width="355" height="20" fontSize="9.0" blankWhenNull="true" bold="true" hTextAlign="Right" vTextAlign="Middle">
			<expression><![CDATA["签名:"]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</lastPageFooter>
	<summary height="40">
		<element kind="frame" uuid="6f51c44a-8372-4ec1-bc80-7f05b2520f5a" x="0" y="0" width="555" height="20">
			<element kind="textField" uuid="0391715f-23a4-43c0-932b-5664641d5f48" x="0" y="0" width="355" height="20" fontSize="11.0" linkTarget="Self" blankWhenNull="true" bold="true" hTextAlign="Right" vTextAlign="Middle">
				<expression><![CDATA["总额"]]></expression>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box padding="0">
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="textField" uuid="425453cc-e653-460c-b1ef-c8fde7815825" x="355" y="0" width="100" height="20" fontSize="11.0" linkTarget="Self" blankWhenNull="true" bold="true" hTextAlign="Right" vTextAlign="Middle">
				<expression><![CDATA[$P{folio}.getTotalDebit()]]></expression>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box padding="0">
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="textField" uuid="b5978d11-f96c-4a66-abbd-f32715e1fe59" x="455" y="0" width="100" height="20" fontSize="11.0" linkTarget="Self" blankWhenNull="true" bold="true" hTextAlign="Right" vTextAlign="Middle">
				<expression><![CDATA[$P{folio}.getTotalCredit()]]></expression>
				<box padding="0">
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
			</element>
			<box>
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="frame" uuid="73516d2f-ffbd-4392-b474-dadf8c60f21a" x="0" y="20" width="555" height="20">
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<element kind="textField" uuid="384d5256-ca02-400f-93a0-770bc89fb09f" x="0" y="0" width="355" height="20" fontSize="11.0" linkTarget="Self" blankWhenNull="true" bold="true" hTextAlign="Right" vTextAlign="Middle">
				<expression><![CDATA["余额"]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box padding="0">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="textField" uuid="044aa171-bf55-42cf-b4b8-07f6ecf2486f" x="354" y="0" width="200" height="20" fontSize="11.0" linkTarget="Self" blankWhenNull="true" bold="true" hTextAlign="Right" vTextAlign="Middle">
				<expression><![CDATA[$P{folio}.getTotalBalance()]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box padding="0">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
			</element>
			<box>
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</summary>
</jasperReport>

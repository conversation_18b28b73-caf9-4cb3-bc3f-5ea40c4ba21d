package com.cw.inject.rights;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cw.arithmetic.SysFunLibTool;
import com.cw.cache.GlobalCache;
import com.cw.cache.RedisTool;
import com.cw.config.Cwconfig;
import com.cw.config.exception.CustomException;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.service.context.GlobalContext;
import com.cw.service.context.OtaGlobalContext;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RateIntervalUnit;
import org.redisson.api.RateType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/1/2 09:20
 **/
@Aspect
@Component
@Order(1)
@Slf4j
public class SignInjecter {

    private static Set<String> checkMethods;

    static {
        checkMethods = Sets.newHashSet();
        checkMethods.addAll(Arrays.asList("/create-order", "/cancel-order", "/query-order", "/pay-order", "/refund-order",
                "/buildings", "/room-types", "/room-rates"));
    }

    @Autowired
    private Cwconfig cwconfig;
    private Logger errlog = LoggerFactory.getLogger("errlog");

    @Around("execution(* com.cw.controller.ota.Pms_OtaController.*(..))")  //拦截所有用户操作接口
    public Object around(ProceedingJoinPoint pjp) throws Throwable {

        if (cwconfig.getChecksign()) {
            String sign = GlobalContext.getOtaHeaderValue(OtaGlobalContext.OtaHeader.SIGN);
            String timestamp = GlobalContext.getOtaHeaderValue(OtaGlobalContext.OtaHeader.STAMP);
            String appid = GlobalContext.getOtaHeaderValue(OtaGlobalContext.OtaHeader.APPID);
            String nonce = GlobalContext.getOtaHeaderValue(OtaGlobalContext.OtaHeader.NONCE);
            // 校验签名合法性
            boolean isValid = validateSignature(sign, timestamp, appid, nonce);
            if (!isValid) {
                throw new CustomException(ResultJson.failure(ResultCode.FORBIDDEN).msg("签名无效或请求频率过高.拒绝访问"));
            }

            Signature signature = pjp.getSignature();

            // 确保签名是MethodSignature类型
            if (signature instanceof MethodSignature) {
                MethodSignature methodSignature = (MethodSignature) signature;

                String methodName = methodSignature.getName();

                if (checkMethods.contains(methodName)) {
                    RRateLimiter rateLimiter = RedisTool.getRedissonClient().getRateLimiter(appid);
                    rateLimiter.trySetRate(RateType.OVERALL, 6, 1, RateIntervalUnit.MINUTES);
                    rateLimiter.expire(3, TimeUnit.MINUTES);
                    boolean acquire = rateLimiter.tryAcquire();
                    if (!acquire) {
                        errlog.error("请求过快:已经拦截 {} -{}", appid, GlobalContext.getRequestIp());
                        throw new CustomException(ResultJson.failure(ResultCode.FORBIDDEN).msg("请求频率过高.拒绝访问."));
                    }
                }
            }


        }
        Object result = pjp.proceed();
        return result;
    }

    private boolean validateSignature(String signature, String timestamp, String appid, String nonce) {
        // 验证是否需要检查签名
        if (!cwconfig.getChecksign()) {
            return true;
        }
        if (signature.equals("chikanpwd")) {
            return true;
        }
        // 验证签名是否为空
        if (ObjectUtil.hasNull(signature, timestamp, appid, nonce)) {
            return false;
        }
        // 将时间戳转换为长整型
        Long postStamp = NumberUtil.parseLong(timestamp);
        // 统一转成用UTC 标准时间来做校验
        ZonedDateTime utcNow = ZonedDateTime.now(ZoneOffset.UTC);
        Long systamp = utcNow.toInstant().toEpochMilli();

        // 验证时间戳间隔 3分钟内
        if (postStamp < systamp - 1000 * 60 * 3) {
            return false;
        }

        String checkbody = timestamp + appid + nonce;
        String localSign = SysFunLibTool.encodeAesContent(checkbody);
        if (!localSign.equals(signature)) {
            return false;
        }

        // 签名验证合法
        return true;
    }


}

package com.cw.inject.subscribe;

import com.cw.core.CorePrice;
import com.cw.pojo.dto.pms.req.rate.UpdateRateDetailBatchReq;
import com.cw.service.mq.notifyer.PmsMsgNotifyer;
import com.cw.utils.CalculateDate;
import com.cw.utils.ProdType;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 房价变化拦截器
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2022/1/4 02:12
 **/
@Aspect
@Component
public class RateInjecter {

    @Pointcut(value = "execution(*  com.cw.service.config.rate.impl.RoomRateDetailServiceImpl.updateRoomRateDetailBatch(..))")
    public void updRoomPricePointCut() {

    }

    //  String roomType, Date startDate, Date endDate,String rateCode, String projectId, BigDecimal price, List<Integer> week
    @AfterReturning("updRoomPricePointCut()" +
            "&&args(hotelId,req)")
    public void updRoomRatedetBatch(String hotelId, UpdateRateDetailBatchReq req) {
        PmsMsgNotifyer notifyer = PmsMsgNotifyer.getInstance();
        for (UpdateRateDetailBatchReq.RateDetailInfo rateDetailInfo : req.getRateDetails()) {
            notifyer.notify_RoomRate_ByMq(hotelId, ProdType.ROOM.val(), req.getRateCode(),
                    req.getStartDate(), req.getEndDate(), rateDetailInfo.getRoomType());
        }
    }


}

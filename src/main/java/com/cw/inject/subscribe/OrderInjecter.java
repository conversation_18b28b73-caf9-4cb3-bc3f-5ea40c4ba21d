package com.cw.inject.subscribe;

import com.cw.core.func.order.StdOrderData;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2022/1/4 02:12
 **/
@Aspect
@Component
@Order(99)
@Slf4j
public class OrderInjecter {



}

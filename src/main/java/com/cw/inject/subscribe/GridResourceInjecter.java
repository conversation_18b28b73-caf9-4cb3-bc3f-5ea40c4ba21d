package com.cw.inject.subscribe;

import cn.hutool.core.util.StrUtil;
import com.cw.arithmetic.sku.TSkuUpd;
import com.cw.core.CoreAvl;
import com.cw.service.mq.notifyer.PmsMsgNotifyer;
import com.cw.utils.CalculateDate;
import com.cw.utils.ProdType;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 房量变化拦截器
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2022/1/4 02:12
 **/
@Aspect
@Component
public class GridResourceInjecter {
    @Autowired
    CoreAvl coreAvl;


    @Pointcut(value = "execution(* com.cw.core.CoreAvl.updateResourceAvailability(..))")
    public void updRoomResourcePointCut() {

    }

    //  String roomType, Date startDate, Date endDate, String projectId, Integer avl, List<Integer> week
    @AfterReturning("updRoomResourcePointCut()&&args(updList,hotelId,prodType)")
    public void updRoomCache(List<TSkuUpd> updList, String hotelId, String prodType) {
        //订单的日常更新 根据算法类算出的库存更新结果来通知更新
        for (TSkuUpd tSkuUpd : updList) {
            PmsMsgNotifyer.getInstance().notify_Grid_ByMq(hotelId, StrUtil.EMPTY, StrUtil.EMPTY, ProdType.ROOM.val(),
                    CalculateDate.returnDate_ZeroTime(tSkuUpd.getStartdate()), CalculateDate.returnDate_ZeroTime(tSkuUpd.getEnddate()),
                    tSkuUpd.getSkuid());
        }

    }


}

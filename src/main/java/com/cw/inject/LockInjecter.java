package com.cw.inject;

import cn.hutool.core.collection.CollectionUtil;
import com.cw.config.exception.BizException;
import com.cw.utils.annotion.LockInterface;
import com.cw.utils.annotion.LockOp;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/4/25
 **/
@Aspect
@Component
@Order(3)
@Slf4j
public class LockInjecter {

    @Autowired
    private RedissonClient redissonClient;

    private static final long timeout = 3;
    private static final long waitTime = 10;
    private static final TimeUnit timeUnit = TimeUnit.SECONDS;

    // 类中带有 @LockOp 注解的方法
    @Pointcut("@annotation(com.cw.utils.annotion.LockOp)")
    public void lockCut() {
    }

    @Around("lockCut()")
    public Object controlSpeed(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        LockOp lockOp = signature.getMethod().getAnnotation(LockOp.class);
        if (lockOp == null) {
            // 若未获取到注解，记录日志并直接执行目标方法
            LoggerFactory.getLogger(LockInjecter.class).warn("未获取到 @LockOp 注解，直接执行目标方法");
            return joinPoint.proceed();
        }

        // 默认是第一个参数
        LockInterface lockInterface = (LockInterface) joinPoint.getArgs()[0];

        String lockKey = lockInterface.getUniqueKey();
        if (StringUtil.isNotEmpty(lockKey)) {
            RLock lock = null;
            boolean isLocked = false;
            try {
                lock = redissonClient.getLock(lockKey);
                isLocked = lock.tryLock(timeout, waitTime, timeUnit);
                if (!isLocked) {
                    LoggerFactory.getLogger(LockInjecter.class).warn("获取锁失败，重复请求，锁键: {}", lockKey);
                    throw new BizException("操作重复请求，请稍后再试");
                }

            } catch (Throwable throwable) {
                if (isLocked && lock != null) {
                    // 发生异常时主动释放锁，避免影响后续正常请求
                    lock.unlock();
                }
                LoggerFactory.getLogger(LockInjecter.class).error("获取锁异常，锁键: {}", lockKey, throwable);
                throw throwable;
            }
            return joinPoint.proceed();
        } else {
            List<String> uniqueKeys = lockInterface.getUniqueKeys();
            if (CollectionUtil.isNotEmpty(uniqueKeys)) {
                RLock lock = null;
                boolean isLocked = false;
                try {
                    lock = redissonClient.getLock(lockKey);
                    isLocked = lock.tryLock(timeout, waitTime, timeUnit);
                    if (!isLocked) {
                        LoggerFactory.getLogger(LockInjecter.class).warn("获取锁失败，重复请求，锁键: {}", lockKey);
                        throw new BizException("操作重复请求，请稍后再试");
                    }

                } catch (Throwable throwable) {
                    if (isLocked && lock != null) {
                        // 发生异常时主动释放锁，避免影响后续正常请求
                        lock.unlock();
                    }
                    LoggerFactory.getLogger(LockInjecter.class).error("获取锁异常，锁键: {}", lockKey, throwable);
                    throw throwable;
                }
                return joinPoint.proceed();
            }
        }
        return null;
    }
}


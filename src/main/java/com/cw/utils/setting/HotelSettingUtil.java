package com.cw.utils.setting;

import com.cw.utils.SystemUtil;

/**
 * <AUTHOR>
 * @date 2025/6/23
 **/
public class HotelSettingUtil {

    public enum HotelSetting {
        // 功能开关
        SALER("订单信息-销售", GroupType.FUNCTIONSWITCH.getDesc(), "在录入订单信息时，允许选择销售", ParamType.BOOL, true),//保洁员
        //        CHECKOUTTIME("订单信息-管家", GroupType.FUNCTIONSWITCH.getDesc(), "在录入订单信息时，允许选择管家", ParamType.BOOL, false),
        ORDER_INFO("订单信息-渠道订单号", GroupType.FUNCTIONSWITCH.getDesc(), "在录入订单信息时，允许手动输入渠道订单号。关闭本功能，不影响直连订单自带的渠道订单号", ParamType.BOOL, true),
        ORDER_BOX("订单盒子", GroupType.FUNCTIONSWITCH.getDesc(), "订单盒子中的订单不实际排房，不占库存，可以将冲突订单、刷单、保留单等暂时放到订单盒子中。订单盒子中的订单不参与统计", ParamType.BOOL, false),
        FORBID_CHECKIN_IN_DIRTY_ROOM("脏房不允许办理入住", GroupType.FUNCTIONSWITCH.getDesc(), "订单盒子中的订单不实际排房，不占库存，可以将冲突订单、刷单、保留单等暂时放到订单盒子中。订单盒子中的订单不参与统计", ParamType.BOOL, false),
        CHECKOUT_PENDING_PROHIBIT_CHECKIN("预离房不允许办理入住", GroupType.FUNCTIONSWITCH.getDesc(), "该功能开启后，在日历房态表中的预离房间必须办理退房后才可以办理入住", ParamType.BOOL, false),
        STAYOVER_DAILY_DIRTY("连住房自动置为脏房", GroupType.FUNCTIONSWITCH.getDesc(), "每日24:00系统自动将连住房置为脏房。连住房最后一天默认不置脏，仍由“办理退房”控制", ParamType.BOOL, false),
        ROOM_STATUS_DISPLAY_UNIFORM("房态显示统一", GroupType.FUNCTIONSWITCH.getDesc(), "该功能开启后，房态(日历房态和单日房态)相关显示设置将以此处设置的规则为准", ParamType.BOOL, false),

        // 住宿默认规则
        CHICK_IN_TIME("默认入住时间", GroupType.STAYRULE.getDesc(), "在录入预订订单时，系统默认填写的入住时间", ParamType.DATETIME, "14:00"),//保洁员
        CHECK_OUT_TIME("默认退房时间", GroupType.STAYRULE.getDesc(), "在录入订单时，系统默认填写的退房时间", ParamType.DATETIME, "12:00"),
        NIGHT_COUNT_EXCLUDE("钟点房统计间夜数", GroupType.STAYRULE.getDesc(), "设置钟点房豁在准ヶ阻计间夜时的数量", ParamType.INT, "0"),
        Full_day_check_in_and_late_check_out("全日入住延迟退房时间", GroupType.STAYRULE.getDesc(), "正常入住、自用房、免费房退房时间超过设置的允许时间时，会被系统判做延迟退房。您也可关闭此功能", ParamType.DURATION, "20"),
        Late_check_out_time_for_hourly_rooms("钟点房延迟退房时间", GroupType.STAYRULE.getDesc(), "钟点房退房时间超过设置的允许时间时，会被系统判做延迟退房。您也可关闭此功能", ParamType.DURATION, "20"),
        Advance_reservation_form_room_key_card("预订单提前制房卡", GroupType.STAYRULE.getDesc(), "预订单提前制房卡", ParamType.DURATION, "0"),
        CLOSE_HOUSE("关房参与经营指标统计", GroupType.STAYRULE.getDesc(), "维修房、停用房、链接关房的房间，是否正常作为总房数统计入住率、平均客房收益，如无需统计请关闭", ParamType.BOOL, false),
        Revenue_statistics_for_self_use_housing_participation("自用房参与营收统计", GroupType.STAYRULE.getDesc(), "入住类型为自用房的房间，是否正常统计平均房价、入住率、间夜量等营业指标，如无需统计请关闭", ParamType.BOOL, false),
        CHECKOUTTIME("免费房参与营收统计", GroupType.STAYRULE.getDesc(), "入住类型为免费房的房间，是否正常统计平均房价，入住率，间夜量等营业指标，如无需统计请关闭", ParamType.BOOL, false),

        // 直连订单规则
//        CHECKOUTTIME("入住自动变脏房", GroupType.ORDERRULES.getDesc(), "宾客完成入住登记（在系统中确认入住状态变更）后，客房管理系统依据预设规则，自动将该客房的状态标记为 “脏房”", ParamType.BOOL, true),
//        CHICKINTIME("直连订单自动排房", GroupType.ORDERRULES.getDesc(), "通过直连渠道（如与 OTA 平台直连、官网预订系统直连等，订单数据可实时、自动传输到酒店 PMS 系统 ）获取预订订单后，系统依据预先设定的规则和策略，无需人工干预", ParamType.BOOL, false),//保洁员
////        CHICKINTIME("多天订单交叉排房", GroupType.STAYRULE.getDesc(), "入住时长大于1天的直连订单，可能存在系统中没有连续空房的情况，开启后，没有连续空房时，将多天拆成多个房间。若不开启，则不排房", ParamType.BOOL, false),//保洁员
////        CHICKINTIME("直连订单自动收款设置", GroupType.STAYRULE.getDesc(), "直连订单落单并确认(订单需转为已预订，不包含待确认/拒单/关闭的订单)后，自动添加收银记录",  ParamType.DATETIME, "14:00"),//保洁员
//
//
//        CHECKOUTTIME("套餐优惠", GroupType.PREFERENTIALPRICE.getDesc(), "绑定 “房 + 餐”“房 + 门票” 等产品组合价", ParamType.BOOL, true),
//        CHECKOUTTIME("套餐优惠", GroupType.PREFERENTIALPRICE.getDesc(), "绑定 “房 + 餐”“房 + 门票” 等产品组合价", ParamType.BOOL, true),

        ;

        private String name;
        private String group;// 组
        private String desc;
        private Enum type;
        private Object defaultValue;// 默认值

        HotelSetting(String name, String group, String desc, Enum type, Object defaultValue) {
            this.name = name;
            this.group = group;
            this.desc = desc;
            this.type = type;
            this.defaultValue = defaultValue;
        }

        public String getName() {
            return name;
        }

        public String getGroup() {
            return group;
        }

        public String getDesc() {
            return desc;
        }

        public Enum getType() {
            return type;
        }

        public Object getDefaultValue() {
            return defaultValue;
        }
    }

    public enum GroupType {
        FUNCTIONSWITCH("function_switch", "功能开关"),
        STAYRULE("stay_rule", "入住规则"),
        ORDERRULES("order_rule", "订单规则"),
        PREFERENTIALPRICE("preferential_price", "价格优惠");

        private String name;
        private String desc;

        GroupType(String name, String desc) {
            this.name = name;
            this.desc = desc;
        }

        public String getName() {
            return name;
        }

        public String getDesc() {
            return desc;
        }
    }

    public enum ParamType {
        STRING, BOOL, INT, FLOAT, DATETIME, DURATION;
    }
}

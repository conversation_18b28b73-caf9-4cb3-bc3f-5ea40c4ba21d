package com.cw.config.mq;

import com.cw.service.mq.MqNameUtils;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.FanoutExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Describe 消息队列配置
 * <AUTHOR> <PERSON>
 * @Create on 2024-03-26
 */
@Configuration
public class ExchangeQueueConfig {
    String appName = "dspms";

    @Value("${random.value}")
    private String randomSignal;


    @Bean(name = "pmsExchange")
    public DirectExchange pmsExchange() {
        return new DirectExchange(MqNameUtils.DirectExchange.PMS.toString(), true, false);
    }

    @Bean(name = "groupExchange")
    public DirectExchange groupExchange() {
        return new DirectExchange(MqNameUtils.DirectExchange.GROUP.toString(), true, false);
    }

    @Bean(name = "fanoutConfigExchange")
    public FanoutExchange fanoutConfigExchange() {  //新增.全局数据配置更新通知路由
        return new FanoutExchange(MqNameUtils.FanoutExchange.CONFIG.toString(), true, false);
    }

    @Bean(name = "fanoutScheduleExchange")
    public FanoutExchange scheduleConfigExchange() {  //计划任务 夜审定时任务
        return new FanoutExchange(MqNameUtils.FanoutExchange.SCHEDULE.toString(), true, false);
    }

    @Bean(name = "fanoutNaExchange")
    public FanoutExchange fanoutNaExchange() {  //夜审步骤
        return new FanoutExchange(MqNameUtils.FanoutExchange.NA.toString(), true, false);
    }

    @Bean(name = "delayExchange")
    public DirectExchange delayExchange() { //缓冲交换机,绑定缓冲队列.缓冲队列的消息会定义超时进入死信队列.死信队列才是真正的处理消息
        return new DirectExchange(MqNameUtils.DirectExchange.DELAY.toString(), true, false);
    }

    @Bean
    public RabbitAdmin rabbitAdmin(ConnectionFactory connectionFactory) {
        RabbitAdmin admin = new RabbitAdmin(connectionFactory);
        return admin;
    }


    @Bean
    public Queue configQueue() {
        String signal = MqNameUtils.getAppSignalId(MqNameUtils.ModuleNotifyQueueName.CONFIG, appName, randomSignal);
        //没有消费者订阅会自动销毁.    不影响断线重连   就算断网一样可以继续消费队列
        return new Queue(signal, true, true, true);
    }

    @Bean
    public Binding configBing(Queue configQueue) {//绑定到配置广播路由
        return new Binding(configQueue.getName(),
                Binding.DestinationType.QUEUE, MqNameUtils.FanoutExchange.CONFIG.name(),
                configQueue.getName(), null);
    }


    @Bean
    public Queue configNaQueue() {
        String signal = MqNameUtils.getAppSignalId(MqNameUtils.ModuleNotifyQueueName.NA, appName, randomSignal);
        //没有消费者订阅会自动销毁.    不影响断线重连   就算断网一样可以继续消费队列
        return new Queue(signal, true, true, true);
    }

    @Bean
    public Binding naBing(Queue configNaQueue) {
        return new Binding(configNaQueue.getName(),
                Binding.DestinationType.QUEUE, MqNameUtils.FanoutExchange.NA.name(),
                configNaQueue.getName(), null);
    }

    @Bean
    public Queue scheduleQueue() {
        String signal = MqNameUtils.getAppSignalId(MqNameUtils.ModuleNotifyQueueName.SCHEDULE, appName, randomSignal);
        return new Queue(signal, true, true, true);
    }

    @Bean
    public Binding scheduleBing(Queue scheduleQueue) {//绑定到配置广播路由
        return new Binding(scheduleQueue.getName(),
                Binding.DestinationType.QUEUE, MqNameUtils.FanoutExchange.SCHEDULE.name(),
                scheduleQueue.getName(), null);
    }
}

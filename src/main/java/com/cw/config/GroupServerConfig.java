package com.cw.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * OTA处理服务配置
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/09/30 16:17
 **/
@Component
@Data
@RefreshScope
@Slf4j
@ConfigurationProperties(prefix = "groupserver", ignoreInvalidFields = true)
public class GroupServerConfig {
    String mqurl = "http://mq.zj.com:8080/";
    String crmurl = "";
    String xcurl = "";
    String appid = "";
    String appkey = "";
    String sectionid = "";

    @PostConstruct
    public void init() {
        LoggerFactory.getLogger(this.getClass()).info("OTA 消息推送服务 地址 init success:{}", mqurl);
    }


}

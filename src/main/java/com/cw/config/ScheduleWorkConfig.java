package com.cw.config;

import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.SchedulerFactory;
import org.quartz.impl.StdSchedulerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2018/7/22 10:40
 **/
@Configuration
@EnableScheduling
public class ScheduleWorkConfig {
    public static final String quartzSchedulerName = "quartzSchedule";

    @Bean(name = quartzSchedulerName)
    public Scheduler getScheduler() { //
        SchedulerFactory sf = new StdSchedulerFactory();
        Scheduler scheduler = null;
        try {
            scheduler = sf.getScheduler();
        } catch (SchedulerException e) {
            e.printStackTrace();
        }
        return scheduler;
    }
}

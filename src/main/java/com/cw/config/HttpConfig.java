package com.cw.config;

import cn.dev33.satoken.interceptor.SaRouteInterceptor;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import com.cw.config.satoken.MchStpUtil;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.ByteArrayHttpMessageConverter;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 跨域访问配置文件
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2024-03-13 16:46
 **/
@Configuration
public class HttpConfig implements WebMvcConfigurer {
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")
                .allowedMethods("POST", "GET", "PUT", "OPTIONS", "DELETE", "HEAD")
                .maxAge(3600)
                .exposedHeaders("access-control-allow-headers",
                        "access-control-allow-methods",
                        "access-control-allow-origin",
                        "access-control-max-age")
                .allowedHeaders("*")
                .allowCredentials(true);

    }


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(getDefinedRouterInterceptor()).addPathPatterns("/**");
    }

    /**
     * 要做登陆拦截校验的路径
     *
     * @return
     */
    private SaRouteInterceptor getDefinedRouterInterceptor() {
        return new SaRouteInterceptor((req, res, handler) -> {

            SaRouter.match(Arrays.asList("/pmsapi/**", "/app-api/**"),
                    Arrays.asList("/api/sysconf/loadconf", "/pmsapi/user/login"), StpUtil::checkLogin);

            SaRouter.match(Arrays.asList("/pmsmchapi/**"),
                    Arrays.asList("/pmsmchapi/user/**"), MchStpUtil::checkLogin);

        });
    }


    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {//处理下日期标准格式
        JSON.DEFFAULT_DATE_FORMAT = "yyyy-MM-dd";
        FastJsonHttpMessageConverter fastJsonHttpMessageConverter = new FastJsonHttpMessageConverter();
        FastJsonConfig fastJsonConfig = new FastJsonConfig();
        fastJsonConfig.setDateFormat("yyyy-MM-dd");
        fastJsonConfig.setSerializerFeatures(SerializerFeature.WriteDateUseDateFormat);
        //3处理中文乱码问题
        List<MediaType> fastMediaTypes = new ArrayList<>();
        fastMediaTypes.add(MediaType.APPLICATION_JSON);
        //4.在convert中添加配置信息.
        fastJsonHttpMessageConverter.setSupportedMediaTypes(fastMediaTypes);
        fastJsonHttpMessageConverter.setFastJsonConfig(fastJsonConfig);

        StringHttpMessageConverter stringHttpMessageConverter = null;
        for(HttpMessageConverter converter: converters){
            if(converter instanceof StringHttpMessageConverter){
                stringHttpMessageConverter = (StringHttpMessageConverter)converter;
                stringHttpMessageConverter.setSupportedMediaTypes(Arrays.asList(MediaType.TEXT_PLAIN, MediaType.APPLICATION_JSON, MediaType.ALL));
                ((StringHttpMessageConverter) converter).setDefaultCharset(Charset.forName("UTF-8"));
            }
        }
        if(stringHttpMessageConverter==null){
            stringHttpMessageConverter = new StringHttpMessageConverter(Charset.forName("UTF-8"));
            stringHttpMessageConverter.setSupportedMediaTypes(Arrays.asList(MediaType.TEXT_PLAIN, MediaType.APPLICATION_JSON, MediaType.ALL));
            converters.add(1, stringHttpMessageConverter);
        }

        converters.add(new ByteArrayHttpMessageConverter());
        converters.add(1, fastJsonHttpMessageConverter);

      /*  for (HttpMessageConverter<?> converter : converters) {
            System.out.println("消息转换器:" + converter.getClass().getName());
            if(converter instanceof StringHttpMessageConverter){
                System.out.println(converter.getClass().getName() + "   字符集:" + ((StringHttpMessageConverter) converter).getDefaultCharset());
            }
        }*/


    }


    /**
     * @param converters
     */
    @Override  //响应统一 UTF-8
    public void extendMessageConverters(List<HttpMessageConverter<?>> converters) {
        for (HttpMessageConverter<?> converter : converters) {
            if (converter instanceof StringHttpMessageConverter) {
                //覆盖默认设置
                ((StringHttpMessageConverter) converter).setDefaultCharset(Charset.forName("UTF-8"));
            }
        }
    }




}

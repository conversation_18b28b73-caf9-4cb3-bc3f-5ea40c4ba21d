package com.cw.config.exception;

/**
 * @Classname BizException
 * @Description 业务异常
 * @Date 2024-04-01 22:23
 * <AUTHOR> sancho.shen
 */
public class BizException extends RuntimeException {
    private String code;
    // 无参构造函数
    public BizException() {
        super();
    }

    // 带有一个错误消息的构造函数
    public BizException(String code, String message) {
        super(message);
        this.code = code;
    }

    // 带有一个错误消息的构造函数
    public BizException(String message) {
        this("", message);
    }

    // 带有错误消息和原因的构造函数
    public BizException(String code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    public BizException(String message, Throwable cause) {
        this("", message, cause);
    }

    // 带有原因的构造函数
    public BizException(Throwable cause) {
        super(cause);
    }

    public String getCode() {
        return code;
    }
}

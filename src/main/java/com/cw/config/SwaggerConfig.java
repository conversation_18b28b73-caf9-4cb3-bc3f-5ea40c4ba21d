package com.cw.config;

import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.RequestHandler;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.builders.RequestParameterBuilder;
import springfox.documentation.oas.annotations.EnableOpenApi;
import springfox.documentation.schema.ScalarType;
import springfox.documentation.service.*;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.service.contexts.SecurityContext;
import springfox.documentation.spring.web.plugins.Docket;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/8/23 01:52
 **/
@Configuration
@EnableOpenApi  //开启 swagger3 可以不写
@EnableKnife4j //开启 knife4j 可以不写
public class SwaggerConfig {

//    private final OpenApiExtensionResolver openApiExtensionResolver;
//
//    @Autowired
//    public SwaggerConfig(OpenApiExtensionResolver openApiExtensionResolver) {
//        this.openApiExtensionResolver = openApiExtensionResolver;
//    }

    @Value("${knife4j.enable:false}")
    Boolean enableApi;


    @Bean
    public Docket createRestApi() {
        // Swagger 2 使用的是：DocumentationType.SWAGGER_2
        // Swagger 3 使用的是：DocumentationType.OAS_30


        Predicate<RequestHandler> selector1 =
                RequestHandlerSelectors.basePackage("com.cw.controller.app");
        Predicate<RequestHandler> selector2 =
                RequestHandlerSelectors.basePackage("com.cw.controller.pms");
        Predicate<RequestHandler> selector3 =
                RequestHandlerSelectors.basePackage("com.cw.controller.open");

        Predicate<RequestHandler> selector4 =
                RequestHandlerSelectors.basePackage("com.cw.controller.mch");
        String groupName = "1.0";


        return new Docket(DocumentationType.OAS_30)
                // 定义是否开启swagger，false为关闭，可以通过变量控制
                .enable(enableApi)
                // 将api的元信息设置为包含在json ResourceListing响应中。
                .apiInfo(new ApiInfoBuilder()
                        .title("PMS MALL API")
                        .description("PMS平台服务管理api")
                        .version("1.0.0")
                        .build())
                // 分组名称
                .groupName(groupName)
//                .extensions(openApiExtensionResolver.buildExtensions(groupName))
                // 选择哪些接口作为swagger的doc发布
                .select()
                .apis(selector1.or(selector2).or(selector3).or(selector4))
                .paths(PathSelectors.any())
                .build()
                .globalRequestParameters(globalRequestParameters())
                .securitySchemes(security())
                .securityContexts(securityContexts());
    }

    /**
     * 授权信息
     *
     * @return
     */
    private List<SecurityScheme> security() {
        ApiKey apiKey = new ApiKey("Authorization", "Authorization", "header");
        return Collections.singletonList(apiKey);
    }

    private List<RequestParameter> globalRequestParameters() {
        //RequestParameterBuilder agentHeaderBuilder = new RequestParameterBuilder().
        //        in(ParameterType.HEADER).name("Agent").required(false).
        //        query(param -> param.defaultValue(AgentType.PC.name()).model(model -> model.scalarModel(ScalarType.STRING)));
        //RequestParameter agentheader = agentHeaderBuilder.build();
        //
        //RequestParameterBuilder appidHeaderBuilder = new RequestParameterBuilder().
        //        in(ParameterType.HEADER).name("Appid").required(false).
        //        query(param -> param.defaultValue("wx477b242e9aea4815").model(model -> model.scalarModel(ScalarType.STRING)));
        //RequestParameter appidheader = appidHeaderBuilder.build();

        RequestParameterBuilder langHeaderBuilder = new RequestParameterBuilder().
                in(ParameterType.HEADER).name("Language").required(false).
                query(param -> param.defaultValue(Locale.CHINESE.getLanguage()).model(model -> model.scalarModel(ScalarType.STRING)));
        RequestParameter langheader = langHeaderBuilder.build();

        return Arrays.asList(langheader);
    }

    /**
     * 授权全局应用
     *
     * @return
     */
    private List<SecurityContext> securityContexts() {
        return Collections.singletonList(
                SecurityContext.builder()
                        .securityReferences(Collections.singletonList(new SecurityReference("Authorization",
                                new AuthorizationScope[]{new AuthorizationScope("global", "")})))
                        .build()
        );
    }



}

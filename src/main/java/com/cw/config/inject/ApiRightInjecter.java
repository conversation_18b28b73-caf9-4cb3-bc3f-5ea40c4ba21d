package com.cw.config.inject;

import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * Created by flyhigh on 2017/6/9.
 */
@Aspect
@Component
@Order(2)
public class ApiRightInjecter {



    ////切点:拦截加了加锁注解的方法
    //@Pointcut("within(com.cw.controller.pms..*)&&@annotation(com.cw.utils.rights.RequireOpRight)")
    ////后台设置权限校验
    //public void apiRightCut() {
    //
    //}
    //
    ///**
    // *  around拦截要返回拦截方法的返回值.否则会出现没结果返回的情况
    // * @param joinPoint
    // * @param requireOpRight
    // * @return
    // * @throws Throwable
    // */
    //@Around("apiRightCut()&&@annotation(requireOpRight)")
    //public Object checkRight(ProceedingJoinPoint joinPoint, RequireOpRight requireOpRight) throws Throwable {//        logger.info("当前方法要求的权限:"+ requireOpRight.opRight()+SpringUtil.getCurrentUser().getUserid());
    //    OpRoleRightCache opRoleRightCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.OP_ROLE_RIGHT);
    //    OpUserCache userCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.USER);
    //    OpUser opUser = userCache.getRecord(GlobalContext.getCurrentHotelId(), GlobalContext.getCurrentUserId());
    //    boolean lHasRight = false;
    //    if (opUser != null) {
    //        lHasRight = opRoleRightCache.hasRight(opUser.getHotelid(), opUser.getRoleid(), requireOpRight.opRight());
    //    }
    //    if (!lHasRight) {
    //        //返回权限不足的名字
    //        int opRight = requireOpRight.opRight();
    //        List<RightCode> opRights = OpRightCodes.getAllOpRights();
    //        for (RightCode right : opRights) {
    //            if (Integer.parseInt(right.getCode()) == opRight) {
    //                return ResultJson.failure(ResultCode.FORBIDDEN).msg("没有权限[" + right.getCodename() + "]代码:" + right.getCode());
    //            }
    //        }
    //
    //    }
    //    Object object = null;
    //    try {
    //        object = joinPoint.proceed();
    //    } catch (Throwable throwable) {
    //        throw throwable;
    //    }
    //    return object;
    //}


}

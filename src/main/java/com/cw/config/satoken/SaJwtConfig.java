package com.cw.config.satoken;

import cn.dev33.satoken.action.SaTokenActionDefaultImpl;
import com.cw.utils.JwtUtils;
import com.cw.utils.LoginJwtForm;
import org.springframework.stereotype.Component;

/**
 * 生成多少
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/9/7 01:21
 **/
@Component
public class SaJwtConfig extends SaTokenActionDefaultImpl {

    @Override
    public String createToken(Object loginId, String loginType) {
        return JwtUtils.generateToken((LoginJwtForm) loginId);
    }
}

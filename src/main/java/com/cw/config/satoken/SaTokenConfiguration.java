package com.cw.config.satoken;

import cn.dev33.satoken.config.SaTokenConfig;
import com.cw.utils.JwtUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.concurrent.TimeUnit;

/**
 * 权限JWT 配置文件
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/9/3 11:10
 **/
@Configuration
public class SaTokenConfiguration {

    @Bean
    @Primary
    public SaTokenConfig getSaTokenConfigPrimary() {
        SaTokenConfig config = new SaTokenConfig();
        config.setTokenName(JwtUtils.token_header);             // token名称 (同时也是cookie名称)
        config.setActivityTimeout(TimeUnit.MINUTES.toSeconds(120));    // token临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
        config.setIsConcurrent(true);               // 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
        config.setIsShare(false);                    // 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
        config.setTokenStyle("uuid");               // token风格
        config.setIsLog(false);                     // 是否输出操作日志
        config.setIsReadBody(false);
        config.setIsReadCookie(false);
        config.setAutoRenew(true);  //token

        return config;
    }

    /* *//**
     * 重写 Sa-Token 框架内部算法策略
     *//*
    @Autowired
    public void rewriteSaStrategy(String arg0) {
        // 重写 Token 生成策略

    }*/

}

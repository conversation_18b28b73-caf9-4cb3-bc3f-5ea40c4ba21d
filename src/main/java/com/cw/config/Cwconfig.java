package com.cw.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/12/3 17:23
 **/
@Component
@Data
@ConfigurationProperties(prefix = "cwconfig", ignoreInvalidFields = false)
public class Cwconfig {
    boolean debugmode = false; //调试模式.默认false 生产环境不会配这个

    Boolean checksign = false;  //开启接口校验模式
    Boolean ticketmode = true;
    String domain = ""; // 回调通知地址 https://wxapp.citybaytech.com  #环境域名 对应生产环境.测试环境的地址前缀
    String consolepwd = "CWKJ666";  //控制端登陆密码.默认密码 bc加密
    boolean enwxplatfrom = false; //集群工作模式.默认单景区独立托管模式. true: 第三方平台代理模式
    String wxplappid = ""; //第三方平台appid
    String wxplsecret = ""; //第三方平台appsecret
    String wxpltoken = ""; //第三方平台token
    String wxplaeskey = "";//第三方平台aeskey
    String consoleurl = "http://127.0.0.1:9600"; //控制端地址 用于授权回调


}

package com.cw.core.init;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2022/9/5 17:43
 **/
public class InitDataFactory {

//    String initUserSql="INSERT INTO `op_user`(`nickname`, `pwd`, `userid`, `userkey`, `username`, `roleid`, `positcode`, `channel`, `profileid`, `subchannel`, `relationid`, `market`, `reports`, `projectid`, `ostatus`, `station`, `tel`) VALUES ('', '$2a$10$.p0JT1VhLvFjbWwEIkdrsuw09QnHpoitwnEVngS1qGt5svMiCU9Ee', 'admin', '', 'admin', 'supervisor', '', '', '', '', '', '', '', '001', b'1', '', '');INSERT INTO `gztraining`.`op_user`(`sqlid`, `nickname`, `pwd`, `userid`, `userkey`, `username`, `roleid`, `positcode`, `channel`, `profileid`, `subchannel`, `relationid`, `market`, `reports`, `projectid`, `ostatus`, `station`, `tel`) " +
//            "VALUES ( '', '$2a$10$.p0JT1VhLvFjbWwEIkdrsuw09QnHpoitwnEVngS1qGt5svMiCU9Ee', 'admin', '', 'admin', 'supervisor', '', '', '', '', '', '', '', '{}', b'1', '', '');";


    public static List<String> getInitSqls(String hotelId) {

        String initPageMenus = "";

        List<String> sqls = FileUtil.readLines("init/newproject.sql", CharsetUtil.CHARSET_UTF_8);
        List<String> finals = sqls.stream().filter(r -> StrUtil.isNotEmpty(r) && !r.startsWith("#")).map(r -> StrUtil.format(r, hotelId)).collect(Collectors.toList());

        return finals;
    }

    public static List<Object> getNewProjectEntitys(String hotelId) {
        NewInitRecords initRecords = new NewInitRecords(hotelId);

        return initRecords.getNewEntitys();
    }

    private static class NewInitRecords {
        private String hotelId;

        public NewInitRecords(String hotelId) {
            this.hotelId = hotelId;
        }

        public List<Object> getNewEntitys() {
            List<Object> oss = Lists.newArrayList();
         /*   oss.add(getNewSysconf());
            oss.add(getNewWebconf());
            oss.addAll(getDefaultRule());//生成几条默认的产品规则
            oss.addAll(getNightAudit());
            oss.addAll(getDefaultVendor());
            //生成几个vendorconfig*/

            return oss;
        }


    }


}

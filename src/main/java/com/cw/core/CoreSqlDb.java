package com.cw.core;

import com.cw.mapper.common.DaoLocal;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.Column;
import javax.persistence.Table;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.*;
import javax.sql.DataSource;

/**
 * 以后修复字段类型,长度什么的.写在这里
 * 还没测试过.先放着.别搞
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2025/1/23 22:21
 **/
@Slf4j
@Service
public class CoreSqlDb {


    @Autowired
    private DaoLocal<?> daoLocal;


    /**
     * 检查并修复表结构
     *
     * @param entityClass 实体类
     * @param fixAll      是否修复所有问题(包括类型不匹配等警告)
     * @return 检查结果
     */
    public TableCheckResult checkAndFixTableStructure(Class<?> entityClass, boolean fixAll) {
        TableCheckResult result = new TableCheckResult();

        try {
            Table tableAnn = entityClass.getAnnotation(Table.class);
            if (tableAnn == null) {
                result.addError("Entity has no @Table annotation: " + entityClass.getName());
                return result;
            }

            String tableName = tableAnn.name();
            result.setTableName(tableName);

            // 获取实体和数据库的字段定义
            Map<String, ColumnInfo> entityColumns = getEntityColumns(entityClass);
            Map<String, ColumnInfo> tableColumns = getTableColumns(tableName);

            // 检查字段差异
            checkColumnDifferences(entityColumns, tableColumns, result);

            // 修复长度不匹配的字段
            if (!result.getLengthMismatches().isEmpty()) {
                fixColumnLengths(tableName, result.getLengthMismatches());
                result.addFixed("Fixed " + result.getLengthMismatches().size() + " column length mismatches");
            }

            // 如果需要修复所有问题
            if (fixAll) {
                fixTypeMismatches(tableName, result.getTypeMismatches());
                fixMissingColumns(tableName, result.getMissingInTable());
                dropExtraColumns(tableName, result.getExtraInTable());
            }

        } catch (Exception e) {
            result.addError("Failed to check table structure: " + e.getMessage());
            log.error("Table structure check failed", e);
        }

        return result;
    }

    private Map<String, ColumnInfo> getEntityColumns(Class<?> entityClass) {
        Map<String, ColumnInfo> columns = new HashMap<>();

        for (Field field : entityClass.getDeclaredFields()) {
            Column column = field.getAnnotation(Column.class);
            if (column != null) {
                String columnName = column.name().replace("[", "").replace("]", "");
                ColumnInfo info = new ColumnInfo();
                info.setName(columnName);
                info.setLength(column.length());
                info.setNullable(column.nullable());
                info.setColumnDefinition(column.columnDefinition());
                info.setJavaType(field.getType());
                columns.put(columnName.toLowerCase(), info);
            }
        }

        return columns;
    }

    private Map<String, ColumnInfo> getTableColumns(String tableName) throws Exception {
        Map<String, ColumnInfo> columns = new HashMap<>();

        try (Connection conn = daoLocal.getDataSource().getConnection()) {
            DatabaseMetaData metaData = conn.getMetaData();
            String catalog = conn.getCatalog();
            String schema = conn.getSchema();

            try (ResultSet rs = metaData.getColumns(catalog, schema, tableName, null)) {
                while (rs.next()) {
                    ColumnInfo info = new ColumnInfo();
                    info.setName(rs.getString("COLUMN_NAME"));
                    info.setSqlType(rs.getString("TYPE_NAME"));
                    info.setLength(rs.getInt("COLUMN_SIZE"));
                    info.setPrecision(rs.getInt("DECIMAL_DIGITS"));
                    info.setNullable("YES".equalsIgnoreCase(rs.getString("IS_NULLABLE")));
                    info.setComment(rs.getString("REMARKS"));

                    // 解析完整的列定义
                    String columnDef = rs.getString("COLUMN_DEF");
                    if (columnDef != null) {
                        info.setColumnDefinition(columnDef);
                        info.parseColumnDefinition();
                    }

                    columns.put(info.getName().toLowerCase(), info);
                }
            }
        }

        return columns;
    }

    private void checkColumnDifferences(
            Map<String, ColumnInfo> entityColumns,
            Map<String, ColumnInfo> tableColumns,
            TableCheckResult result) {

        // 检查长度不匹配
        for (Map.Entry<String, ColumnInfo> entry : entityColumns.entrySet()) {
            String columnName = entry.getKey();
            ColumnInfo entityCol = entry.getValue();
            ColumnInfo tableCol = tableColumns.get(columnName);

            if (tableCol == null) {
                result.addMissingInTable(columnName);
                continue;
            }

            // 检查长度不匹配
            if (entityCol.getLength() != tableCol.getLength()) {
                result.addLengthMismatch(columnName, tableCol.getLength(), entityCol.getLength());
            }

            // 检查类型不匹配
            if (!isCompatibleTypes(entityCol.getJavaType(), tableCol.getSqlType())) {
                result.addTypeMismatch(columnName, tableCol.getSqlType(), entityCol.getJavaType());
            }
        }

        // 检查数据库中多余的字段
        for (String columnName : tableColumns.keySet()) {
            if (!entityColumns.containsKey(columnName)) {
                result.addExtraInTable(columnName);
            }
        }
    }

    private void fixColumnLengths(String tableName, List<LengthMismatch> mismatches) {
        for (LengthMismatch mismatch : mismatches) {
            String sql = String.format(
                    "ALTER TABLE %s MODIFY COLUMN %s %s(%d)",
                    tableName,
                    mismatch.getColumnName(),
                    getColumnType(mismatch.getColumnName()),
                    mismatch.getExpectedLength()
            );

            try {
                executeSQL(sql);
                log.info("Fixed column length: {} in table {}", mismatch.getColumnName(), tableName);
            } catch (Exception e) {
                log.error("Failed to fix column length: " + mismatch.getColumnName(), e);
            }
        }
    }

    /**
     * 检查Java类型和SQL类型是否兼容
     */
    private boolean isCompatibleTypes(Class<?> javaType, String sqlType) {
        sqlType = sqlType.toLowerCase();

        if (String.class.equals(javaType)) {
            return sqlType.contains("char") || sqlType.contains("text");
        } else if (Integer.class.equals(javaType) || int.class.equals(javaType)) {
            return sqlType.contains("int");
        } else if (Long.class.equals(javaType) || long.class.equals(javaType)) {
            return sqlType.contains("bigint");
        } else if (Boolean.class.equals(javaType) || boolean.class.equals(javaType)) {
            return sqlType.contains("bit") || sqlType.contains("boolean");
        } else if (java.util.Date.class.equals(javaType)) {
            return sqlType.contains("datetime") || sqlType.contains("timestamp");
        } else if (java.math.BigDecimal.class.equals(javaType)) {
            return sqlType.contains("decimal") || sqlType.contains("numeric");
        }

        return false;
    }

    /**
     * 获取列的SQL类型
     */
    private String getColumnType(String columnName) {
        try (Connection conn = daoLocal.getDataSource().getConnection()) {
            DatabaseMetaData metaData = conn.getMetaData();
            String catalog = conn.getCatalog();
            String schema = conn.getSchema();

            try (ResultSet rs = metaData.getColumns(catalog, schema, null, columnName)) {
                if (rs.next()) {
                    return rs.getString("TYPE_NAME");
                }
            }
        } catch (Exception e) {
            log.error("Failed to get column type for: " + columnName, e);
        }
        return "varchar"; // 默认类型
    }

    /**
     * 执行SQL语句
     */
    private void executeSQL(String sql) {
        try (Connection conn = daoLocal.getDataSource().getConnection();
             Statement stmt = conn.createStatement()) {
            log.info("Executing SQL: {}", sql);
            stmt.execute(sql);
        } catch (Exception e) {
            log.error("Failed to execute SQL: " + sql, e);
            throw new RuntimeException("Failed to execute SQL", e);
        }
    }

    /**
     * 修复类型不匹配的列
     */
    private void fixTypeMismatches(String tableName, List<TypeMismatch> mismatches) {
        for (TypeMismatch mismatch : mismatches) {
            log.warn("Type mismatch found for column {}: SQL type {} vs Java type {}",
                    mismatch.getColumnName(), mismatch.getSqlType(), mismatch.getJavaType());
            // 暂时只记录警告，不执行修复
        }
    }

    /**
     * 修复缺失的列
     */
    private void fixMissingColumns(String tableName, List<String> missingColumns) {
        for (String columnName : missingColumns) {
            log.warn("Column missing in table {}: {}", tableName, columnName);
            // 暂时只记录警告，不执行修复
        }
    }

    /**
     * 删除多余的列
     */
    private void dropExtraColumns(String tableName, List<String> extraColumns) {
        for (String columnName : extraColumns) {
            log.warn("Extra column found in table {}: {}", tableName, columnName);
            // 暂时只记录警告，不执行修复
        }
    }

    /**
     * 批量检查和修复多个实体类
     */
    public List<TableCheckResult> batchCheckAndFix(List<Class<?>> entityClasses, boolean fixAll) {
        List<TableCheckResult> results = new ArrayList<>();
        for (Class<?> entityClass : entityClasses) {
            results.add(checkAndFixTableStructure(entityClass, fixAll));
        }
        return results;
    }
}

@Data
class TableCheckResult {
    private String tableName;
    private List<String> errors = new ArrayList<>();
    private List<String> fixed = new ArrayList<>();
    private List<String> warnings = new ArrayList<>();
    private List<LengthMismatch> lengthMismatches = new ArrayList<>();
    private List<TypeMismatch> typeMismatches = new ArrayList<>();
    private List<String> missingInTable = new ArrayList<>();
    private List<String> extraInTable = new ArrayList<>();

    public void addError(String error) {
        errors.add(error);
    }

    public void addFixed(String message) {
        fixed.add(message);
    }

    public void addWarning(String warning) {
        warnings.add(warning);
    }

    public void addLengthMismatch(String columnName, long currentLength, long expectedLength) {
        lengthMismatches.add(new LengthMismatch(columnName, currentLength, expectedLength));
    }

    public void addTypeMismatch(String columnName, String sqlType, Class<?> javaType) {
        typeMismatches.add(new TypeMismatch(columnName, sqlType, javaType));
    }

    public void addMissingInTable(String columnName) {
        missingInTable.add(columnName);
    }

    public void addExtraInTable(String columnName) {
        extraInTable.add(columnName);
    }
}

@Data
class LengthMismatch {
    private String columnName;
    private long currentLength;
    private long expectedLength;

    public LengthMismatch(String columnName, long currentLength, long expectedLength) {
        this.columnName = columnName;
        this.currentLength = currentLength;
        this.expectedLength = expectedLength;
    }
}

@Data
class TypeMismatch {
    private String columnName;
    private String sqlType;
    private Class<?> javaType;

    public TypeMismatch(String columnName, String sqlType, Class<?> javaType) {
        this.columnName = columnName;
        this.sqlType = sqlType;
        this.javaType = javaType;
    }
}

@Data
class ColumnInfo {
    private String name;                // 字段名
    private long length;                // 字段长度
    private boolean nullable;           // 是否可为空
    private String columnDefinition;    // 字段定义
    private Class<?> javaType;         // Java类型
    private String sqlType;            // SQL类型
    private int precision;             // 精度(用于数字类型)
    private int scale;                 // 小数位数
    private String comment;            // 字段注释

    // 解析columnDefinition获取类型信息
    public void parseColumnDefinition() {
        if (columnDefinition != null && !columnDefinition.isEmpty()) {
            // 例如: "varchar(64) default '' comment '代码'"
            String def = columnDefinition.toLowerCase();

            // 提取类型和长度
            int typeStart = def.indexOf(" ");
            if (typeStart > 0) {
                String typeInfo = def.substring(0, typeStart);
                if (typeInfo.contains("(")) {
                    sqlType = typeInfo.substring(0, typeInfo.indexOf("("));
                    String lengthStr = typeInfo.substring(typeInfo.indexOf("(") + 1, typeInfo.indexOf(")"));
                    if (lengthStr.contains(",")) {
                        String[] parts = lengthStr.split(",");
                        precision = Integer.parseInt(parts[0].trim());
                        scale = Integer.parseInt(parts[1].trim());
                    } else {
                        length = Long.parseLong(lengthStr);
                    }
                } else {
                    sqlType = typeInfo;
                }
            }

            // 提取注释
            int commentIndex = def.indexOf("comment");
            if (commentIndex > 0) {
                comment = def.substring(commentIndex + 7).trim();
                comment = comment.substring(1, comment.length() - 1); // 去掉引号
            }
        }
    }

    // 生成完整的字段定义SQL
    public String generateColumnDefinition() {
        StringBuilder sql = new StringBuilder();

        // 添加基本类型和长度
        sql.append(sqlType);
        if (length > 0) {
            sql.append("(").append(length).append(")");
        } else if (precision > 0) {
            sql.append("(").append(precision);
            if (scale > 0) {
                sql.append(",").append(scale);
            }
            sql.append(")");
        }

        // 添加是否可空
        sql.append(nullable ? " NULL" : " NOT NULL");

        // 添加默认值(如果有)
        if (columnDefinition != null && columnDefinition.contains("default")) {
            int defaultStart = columnDefinition.indexOf("default");
            int defaultEnd = columnDefinition.indexOf("comment");
            if (defaultEnd < 0) defaultEnd = columnDefinition.length();
            String defaultValue = columnDefinition.substring(defaultStart, defaultEnd);
            sql.append(" ").append(defaultValue);
        }

        // 添加注释
        if (comment != null && !comment.isEmpty()) {
            sql.append(" COMMENT '").append(comment).append("'");
        }

        return sql.toString();
    }
}

package com.cw.core.func.order.req;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.cw.entity.Colrs;
import com.cw.entity.Profile;
import com.cw.entity.Reservation;
import com.cw.pms.request.CwColRsReq;
import com.cw.pojo.common.core.StdOrderRequest;
import com.cw.service.context.OtaGlobalContext;
import com.cw.utils.enums.StatusTypeUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2025/3/3 00:43
 **/
public class OtaColRsUpd implements StdOrderRequest {

    CwColRsReq req;

    public OtaColRsUpd(CwColRsReq req) {
        this.req = req;
    }

    @Override
    public Colrs getColrs() {
        Colrs colrs = new Colrs();
        colrs.setOtano(req.getOtaOrderId());
        colrs.setCrsno(req.getNetworkId());
        colrs.setChannel(req.getChannelCode());
        colrs.setCreateTime(DateUtil.date());
        colrs.setTelephone(req.getPhone());
        return colrs;
    }

    @Override
    public List<Reservation> getRoomRss() {
        List<Reservation> reservations = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(req.getRooms())) {
            for (CwColRsReq.Room room : req.getRooms()) {
                Reservation record = new Reservation();
                record.setGuestName(req.getBooker());
                record.setBlock(StrUtil.isBlank(req.getBlockCode()) ? "" : req.getBlockCode());
                record.setArrivalDate(room.getStayDateRange().getStartDate());
                record.setDepartureDate(room.getStayDateRange().getEndDate());
                record.setRateCode(room.getRateCode());
                record.setRemark(room.getCommon());
                record.setChannel(room.getChannel());
                record.setRooms(room.getNumberOfRooms());
                record.setChannel(room.getChannel());  //TODO 没有加转换表.暂时先直接转
                record.setRoomType(room.getRoomType());
                record.setCreateTime(DateUtil.date());
                record.setTelephone(req.getPhone());
                record.setCreateBy("SYS");
                record.setCrsno(req.getNetworkId());
                record.setOtano(req.getOtaOrderId());
                //TODO 中台过来的是否做固定价格呢？ 还需要安装OTA价格的日历价格进行价格固定

                reservations.add(record);
            }
        }
        return reservations;
    }

    @Override
    public Profile getMainProfile() {
        return null;
    }

    @Override
    public String getHotelId() {
        return OtaGlobalContext.getCurrentHotelId();
    }
}

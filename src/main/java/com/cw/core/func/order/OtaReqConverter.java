package com.cw.core.func.order;

import com.cw.pms.request.CwColPayReq;
import com.cw.pms.request.CwOrderArPayReq;
import com.cw.pms.request.CwOrderConsumptionReq;
import com.cw.pojo.dto.pms.req.cashier.CashierAccountReq;
import com.cw.pojo.dto.pms.req.cashier.CashierPostReq;

import java.math.BigDecimal;

/**
 * 将一些OTA 接口请求转成本地的参数形式
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2025/2/25 16:40
 **/
public class OtaReqConverter {

    public static CashierPostReq convertArPayReq(CwOrderArPayReq req) {
        CashierPostReq cashierPostReq = new CashierPostReq()
                .setRemark(req.getRemark()).setAr_no(req.getAr_no()).setReservationNumber(req.getItfNo());

        cashierPostReq.getAccounts().add(new CashierAccountReq()
                .setDepartmentCode(req.getDeptCode())// 账项码必须为消费码，看看在哪控制好
                .setPrice(req.getAmount().abs()));  //金额取正
        return cashierPostReq;
    }

    public static CashierPostReq convertConsumptionReq(CwOrderConsumptionReq req) {
        CashierPostReq cashierPostReq = new CashierPostReq()
                .setRemark(req.getRemark()).setReservationNumber(req.getRoomReservationNo());
        cashierPostReq.getAccounts().add(new CashierAccountReq()
                .setDepartmentCode(req.getDeptCode())
                .setPrice(BigDecimal.valueOf(req.getAmount().doubleValue())));


        return cashierPostReq;
    }


}

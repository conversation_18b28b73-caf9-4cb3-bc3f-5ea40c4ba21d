package com.cw.core.func.order;

import com.cw.core.SeqNoService;
import com.cw.entity.Colrs;
import com.cw.entity.Reservation;
import com.cw.pojo.common.core.StdOrderRequest;
import com.cw.service.context.GlobalContext;
import com.cw.utils.SystemUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 将购买请求转换成订单实体.并写入订单号.付款单号
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/10/13 11:56
 **/
@Service
public class OrderReqTransFactory {

    private final SeqNoService seqNoService;

    @Autowired
    public OrderReqTransFactory(SeqNoService seqNoService) {
        this.seqNoService = seqNoService;
    }


    public StdOrderData parseOrderForm(StdOrderRequest req) {
        StdOrderData stdOrderData = new StdOrderData();

        stdOrderData.setColrs(req.getColrs());
        stdOrderData.setRooms(req.getRoomRss());
        //订单内容以后需要拓展..就在这里加

        return stdOrderData;
    }


    /**
     * 初始化订单字段.写入项目 ID, 创建时间,订单号.根据购买产品将产品类型写入订单
     *
     * @param data
     */
    public void writeMakerAndRegno(StdOrderData data) {
        String hotelId = data.getHotelId();
        String userid = GlobalContext.getCurrentUserId();
        LocalDateTime now = LocalDateTime.now();
        Date nowdate = new Date();
        String relationNumber = "";

        if (data.getColrs() != null) {
            Colrs colrs = data.getColrs();
            colrs.setHotelId(hotelId);
            colrs.setBookingid(seqNoService.getSequenceID(SystemUtil.SequenceKey.ORDERID));
            relationNumber = colrs.getBookingid();
        }

        List<Long> dates = new ArrayList<>();
        for (Reservation rs : data.getRooms()) {
            rs.setCreateTime(nowdate);
            rs.setHotelId(hotelId);
            rs.setReservationNumber(seqNoService.getSequenceID(SystemUtil.SequenceKey.ORDERID));
            dates.add(rs.getArrivalDate().getTime());
            dates.add(rs.getDepartureDate().getTime());
            rs.setRelationNumber(relationNumber);   //将订单号关联到一起
        }

        Collections.sort(dates);
        if (!dates.isEmpty() && data.getColrs() != null) {
            Colrs colrs = data.getColrs();

            Date startdate = new Date(dates.get(0));
            Date enddate = new Date(dates.get(dates.size() - 1));
            colrs.setArrivalDate(startdate);
            colrs.setDepartureDate(enddate);
        }


    }


/*    public void writeHotelId(String hotelId, StdOrderData orderData) {
        //orderData.getBookingRs().setProjectid(hotelId);
        for (Reservation room : orderData.getRooms()) {
            room.setHotelId(hotelId);
        }
    }*/


}

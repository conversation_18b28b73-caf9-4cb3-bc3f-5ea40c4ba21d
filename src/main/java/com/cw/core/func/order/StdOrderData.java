package com.cw.core.func.order;

import com.cw.entity.Colrs;
import com.cw.entity.Profile;
import com.cw.entity.Reservation;
import com.cw.entity.RoomsDaily;
import com.cw.utils.SystemUtil;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Multimap;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/6/17 15:34
 **/
@Data
public class StdOrderData {
    String hotelId;

    Colrs colrs;

    List<Reservation> rooms = Lists.newArrayList();

    Multimap<String, RoomsDaily> roomsDailyMap = ArrayListMultimap.create();//存放每个订单号的每日房价信息

    Multimap<String, Profile> profilesMap = ArrayListMultimap.create();//存放每个订单号的档案信息

    //后面又代金券之类的抵扣也放在这

    BigDecimal actualAmount;  //实际支付金额
    BigDecimal extrafee = BigDecimal.ZERO;//额外费用,通常是运费
    BigDecimal totalAmount = BigDecimal.ZERO;//订单总金额
    BigDecimal discAmount = BigDecimal.ZERO;//折扣金额

    public String getReSource() {
        return SystemUtil.DEF_RESOURCEID;
    }


}

package com.cw.core;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.cw.arithmetic.PojoUtils;
import com.cw.cache.CustomData;
import com.cw.entity.Room;
import com.cw.exception.DefinedException;
import com.cw.mapper.ReservationMapper;
import com.cw.mapper.RoomMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.dto.common.req.PageReq;
import com.cw.pojo.dto.pms.req.room.QueryRegisterRoomReq;
import com.cw.pojo.dto.pms.req.room.RoomSearchReq;
import com.cw.pojo.dto.pms.res.room.RoomListRes;
import com.cw.pojo.dto.pms.res.room.RoomRes;
import com.cw.service.context.GlobalContext;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.StatusTypeUtils;
import com.cw.utils.jpa.DynamicSpecificationBuilder;
import com.cw.utils.jpa.Operator;
import com.cw.utils.jpa.SearchCriteria;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/5/6
 **/
@Service
public class CoreAssign {

    @Autowired
    private RoomMapper roomMapper;

    @Autowired
    DaoLocal<?> daoLocal;

    public RoomListRes listRegisterRooms(QueryRegisterRoomReq queryRegisterRoomReq) {
        //如果前端不传，默认使用当天的日期
        String today = DateUtil.now();
        //入住日期
        String arrivalDateStr = queryRegisterRoomReq.getArrivalDate();
        DateTime arrivalDate = DateUtil.parse(StrUtil.isNotEmpty(arrivalDateStr) ? arrivalDateStr : today, "yyyy-MM-dd");
        //特性
        String characteristic = queryRegisterRoomReq.getCharacteristic();
        //房态
        String roomStatus = queryRegisterRoomReq.getRoomStatus();
        //查询起始房间
        String roomNoStart = queryRegisterRoomReq.getRoomNoStart();
        //查询结束房间
        String roomNoEnd = queryRegisterRoomReq.getRoomNoEnd();
        //房型
        String roomType = queryRegisterRoomReq.getRoomType();
        //分页数据
        PageReq.PageData pages = queryRegisterRoomReq.getPages();
        // 房间号
        String roomNumber = queryRegisterRoomReq.getRoomNo();
        String hotelId = GlobalContext.getCurrentHotelId();

        RoomListRes roomListRes = null;
        //查询的房间范围
        List<String> roomList = getRoomRange(roomNoStart, roomNoEnd);
        //如果入住日期是当天的则查询当天的房间表即可
        DateTime todayDate = DateUtil.parse(today, "yyyy-MM-dd");
        if (DateUtil.compare(todayDate, arrivalDate) == 0) {
            //房态
            List<String> roomStatusList;
            //如果roomStatus为空，则默认查询非占用的干净房
            if (StrUtil.isNotEmpty(roomStatus)) {
                roomStatusList = Arrays.asList(roomStatus.split(","));
            } else {
                roomStatusList = Lists.newArrayList();
                roomStatusList.add(StatusTypeUtils.RoomStatus.CLEAN);
            }
            roomListRes = getRegisterRoomRes(queryRegisterRoomReq, hotelId, roomType, characteristic, roomList, roomStatusList, 0, roomNumber);
        } else {
            //所有符合的房间
            roomListRes = getRegisterRoomRes(queryRegisterRoomReq, null, null, hotelId, roomType, roomNumber); //根据占用的预订表排查一遍
            List<RoomRes> records = roomListRes.getRecords();
//            etRegisterRoomRes(QueryRegisterRoomReq req,
//                    String roomNumber,
//                    String reservationNumber,
//                    Date arrivalDate,
//                    Date departureDate,
//                    String hotelId,
//                    String roomType
//                    List < RoomRes > records = roomListRes.getRecords();
            //此处筛选出来的未来房间，都是空闲的干净房
            //for (RoomRes roomRes : records) {
            //    roomRes.setRoomStatus(StatusTypeUtils.RoomStatus.CLEAN);
            //    roomRes.setLocc(StatusTypeUtils.RoomLocc.UN_OCCUPY);
            //}
            //如果房间范围是空的，则直接返回
            //if (ObjectUtil.isEmpty(roomList)) {
            //    return roomListRes;
            //}

            if (!roomList.isEmpty()) {
                //根据房间范围筛选符合条件的房间列表，后续还要过滤
                List<Room> roomsRecords = getRegisterRoomRes(hotelId, roomType, characteristic, roomList, null, null);
                List<RoomRes> roomResult = Lists.newArrayList();
                for (RoomRes roomRes : records) {
                    String roomNo = roomRes.getRoomNo();
                    for (Room res : roomsRecords) {
                        //根据房间范围过滤所有不符合的房间
                        if (roomNo.equals(res.getRoomNo())) {
                            roomResult.add(roomRes);
                            break;
                        }
                    }
                }
                roomListRes = paginateResults(roomResult, queryRegisterRoomReq.getPages());
            }

        }

        if (CollectionUtil.isNotEmpty(roomListRes.getRecords())) {
            for (RoomRes node : roomListRes.getRecords()) {
                //床型房间特性描述
                if (StringUtils.isNotBlank(node.getCharacteristic())) {
                    String[] descs = node.getCharacteristic().split(",");
                    String rowdesc = "";
                    for (String desc : descs) {
                        String descName = CustomData.getDesc(hotelId, desc, SystemUtil.CustomDataKey.characteristic);
                        rowdesc += rowdesc.isEmpty() ? descName : "," + descName;
                    }
                    node.setCharacteristic(rowdesc);
                }

            }
        }

        return roomListRes;
    }

    /**
     * 获取排除预订在用的房间
     * @param req
     * @param roomNumber
     * @param reservationNumber
     * @param hotelId
     * @param roomType
     * @return
     */
    public RoomListRes getRegisterRoomRes(QueryRegisterRoomReq req,
                                          String roomNumber,
                                          String reservationNumber,
                                          String hotelId,
                                          String roomType,
                                          String roomNo
    ) {
        String rsfilter = "SELECT res.roomNumber FROM Reservation res WHERE res.hotelId= :hotelId  "
                + (StringUtils.isNotBlank(roomNumber) ? " AND res.roomNumber = :roomNumber" : "")
                + (StringUtils.isNotBlank(reservationNumber) ? " res.reservationNumber = :reservationNumber" : "")
                + "AND res.reservationStatus IN (0, 1) "
                + "AND ((res.arrivalDate < :arrivalDate AND res.departureDate > :departureDate) "
                + "     OR (res.arrivalDate > :arrivalDate AND res.arrivalDate < :arrivalDate))";
        StringBuilder jpql = new StringBuilder(" FROM Room r ");
        jpql.append("  WHERE r.hotelId = :hotelId")
                .append("   AND r.roomType =  :roomType ")
                .append((StringUtils.isNotBlank(roomNumber) ?"   AND r.roomNo =  :roomNo " : ""))
                .append("   AND r.roomNo NOT IN (")
                .append(rsfilter + " )");

        Map<String, Object> queryMap = Maps.newHashMap();
        queryMap.put("hotelId", hotelId);
        if (StringUtils.isNotBlank(roomNumber)) {
            queryMap.put("roomNumber", roomNumber);
        }

        if (StringUtils.isNotBlank(reservationNumber)) {
            queryMap.put("reservationNumber", reservationNumber);
        }
        queryMap.put("arrivalDate", DateUtil.parse(req.getArrivalDate(), "yyyy-MM-dd"));
        queryMap.put("roomType", roomType);
        if (StringUtils.isNotBlank(roomNumber) ) {
            queryMap.put("roomNo", roomNo);
        }
        queryMap.put("departureDate",DateUtil.parse(req.getDepartureDate(), "yyyy-MM-dd"));

        Page<Room> roomPage = daoLocal.getListPage(jpql.toString(), queryMap, req.getPages().getPageable());
        RoomListRes roomListRes = new RoomListRes();
        roomListRes.fillPageData(roomPage);

        Map<String, List<RoomRes>> rmtMaps = Maps.newHashMap();
        List<RoomRes> ls = roomListRes.getRecords();
        for (RoomRes room : ls) {
            rmtMaps.computeIfAbsent(room.getRoomType(), k -> new ArrayList<>()).add(room);
        }
        roomListRes.setRmtGroupInfos(new ArrayList<>());
        for (Map.Entry<String, List<RoomRes>> stringListEntry : rmtMaps.entrySet()) {
            RoomListRes.RmtGroupInfo rmtGroupInfo = new RoomListRes.RmtGroupInfo();
            rmtGroupInfo.setRoomType(stringListEntry.getKey());
            rmtGroupInfo.setDescription(CustomData.getDesc(hotelId, rmtGroupInfo.getRoomType(), SystemUtil.CustomDataKey.roomtype));
            List<RoomRes> groupnodes = Lists.newArrayList();
            for (RoomRes value : stringListEntry.getValue()) {
                groupnodes.add(PojoUtils.cloneEntity(value));
            }
            rmtGroupInfo.setRooms(groupnodes);
            roomListRes.getRmtGroupInfos().add(rmtGroupInfo);
        }
        return roomListRes;
    }

    /**
     *  获取指定房间
     * @param req
     * @param hotelId
     * @param roomType
     * @param characteristic
     * @param roomsList
     * @param roomStatusList
     * @param locc
     * @return
     */
    public RoomListRes getRegisterRoomRes(QueryRegisterRoomReq req, String hotelId
            , String roomType, String characteristic, List<String> roomsList, List<String> roomStatusList, Integer locc, String roomNo
    ) {

        String jpql = "  FROM Room r "
                + "        WHERE r.hotelId = :hotelId"
                + "        AND r.locc = :locc"
                + "        AND r.roomType = :roomType"
                + "        AND r.roomStatus IN :roomStatus"
                +  (StringUtils.isNotEmpty(roomNo) ? "        AND r.roomNo = :roomNo" : "")
                + (StringUtils.isNotBlank(characteristic) ? " AND r.characteristic = :characteristic" : "")
                + (CollectionUtil.isNotEmpty(roomsList) ? " AND r.roomNo between :roomStart AND :roomEnd" : "");

        Map<String, Object> queryMap = Maps.newHashMap();
        queryMap.put("hotelId", hotelId);
        queryMap.put("roomType", roomType);
        queryMap.put("roomStatus", roomStatusList);
        queryMap.put("locc", locc);
        if (StringUtils.isNotEmpty(roomNo)) {
            queryMap.put("roomNo", roomNo);
        }
        if (StringUtils.isNotBlank(characteristic) ) {
            queryMap.put("characteristic", characteristic);
        }
        if (CollectionUtil.isNotEmpty(roomsList)) {
            queryMap.put("roomStart", roomsList.get(0));
            if (roomsList.size() > 1) {
                queryMap.put("roomEnd", roomsList.get(roomsList.size() - 1));
            } else {
                queryMap.put("roomEnd", "");
            }
        }

        Page<Room> roomPage = daoLocal.getListPage(jpql, queryMap, req.getPages().getPageable());
        RoomListRes roomListRes = new RoomListRes();
        roomListRes.fillPageData(roomPage);

//        if (req.isNeedRmtGroup()) {  //批量分房时.需要进行分组
        Map<String, List<RoomRes>> rmtMaps = Maps.newHashMap();
        List<RoomRes> ls = roomListRes.getRecords();
        for (RoomRes room : ls) {
            rmtMaps.computeIfAbsent(room.getRoomType(), k -> new ArrayList<>()).add(room);
        }
        roomListRes.setRmtGroupInfos(new ArrayList<>());
        for (Map.Entry<String, List<RoomRes>> stringListEntry : rmtMaps.entrySet()) {
            RoomListRes.RmtGroupInfo rmtGroupInfo = new RoomListRes.RmtGroupInfo();
            rmtGroupInfo.setRoomType(stringListEntry.getKey());
            rmtGroupInfo.setDescription(CustomData.getDesc(hotelId, rmtGroupInfo.getRoomType(), SystemUtil.CustomDataKey.roomtype));
            List<RoomRes> groupnodes = Lists.newArrayList();
            for (RoomRes value : stringListEntry.getValue()) {
                groupnodes.add(PojoUtils.cloneEntity(value));
            }
            rmtGroupInfo.setRooms(groupnodes);
            roomListRes.getRmtGroupInfos().add(rmtGroupInfo);
        }
//        }
        return roomListRes;
    }

    private List<Room> getRegisterRoomRes(String hotelId
            , String roomType, String characteristic, List<String> roomsList, List<String> roomStatusList, Integer locc
    ) {
        String jpql = "  FROM Room r "
                + "        WHERE r.hotelId = :hotelId"
                + ( locc != null ? "  AND r.locc = :locc " : "")
                + "        AND r.roomType = :roomType"
                + (CollectionUtil.isNotEmpty(roomStatusList) ? "  AND r.roomStatus NOT IN  :roomStatus" : "")
                + (StringUtils.isNotBlank(characteristic) ? " AND r.characteristic = :characteristic" : "")
                + (CollectionUtil.isNotEmpty(roomsList) ? " AND r.roomNo between :roomStart AND :roomEnd" : "");

        Map<String, Object> queryMap = Maps.newHashMap();
        queryMap.put("hotelId", hotelId);
        queryMap.put("roomType", roomType);
        if (CollectionUtil.isNotEmpty(roomStatusList)) {
            queryMap.put("roomStatus", roomStatusList);
        }
        queryMap.put("locc", locc);
        if (StringUtils.isNotEmpty(characteristic)) {
            queryMap.put("characteristic", characteristic);
        }
        if (CollectionUtil.isNotEmpty(roomsList)) {
            queryMap.put("roomStart", roomsList.get(0));
            if (roomsList.size() > 1) {
                queryMap.put("roomEnd", roomsList.get(roomsList.size() - 1));
            } else {
                queryMap.put("roomEnd", "");
            }
        }
        List<Room> roomList = daoLocal.getList(jpql, queryMap);

        return roomList;
    }

    private ArrayList<String> getRoomRange(String roomNoStart, String roomNoEnd) {
        //房间范围
        ArrayList<String> roomNoList = Lists.newArrayList();
        if (StrUtil.isNotEmpty(roomNoStart)) {
            roomNoList.add(roomNoStart);
        }
        if (StrUtil.isNotEmpty(roomNoEnd)) {
            roomNoList.add(roomNoEnd);
        }
        return roomNoList;
    }

    private RoomListRes paginateResults(List<RoomRes> roomResList, PageReq.PageData pages) {
        //查询当前页数
        int currentPage = pages.getCurrentpage();
        //查询的每页大小
        int pageSize = pages.getPagesize();
        //符合条件的总数
        int total = roomResList.size();
        //根据前端请求的分页数据，截取相应数据返回给前端
        int start = (currentPage - 1) * pageSize;
        int end = Math.min(start + pageSize, total);
        // 计算总页数
        int totalPages = (total + pageSize - 1) / pageSize;
        // 确保当前页码不超过总页数
        currentPage = Math.min(currentPage, totalPages);

        // 构建分页响应对象
        RoomListRes roomListRes = new RoomListRes();
        roomListRes.setTotal(total);
        roomListRes.setPagesize(pageSize);
        roomListRes.setCurrentpage(currentPage);
        roomListRes.setTotalpage(totalPages);
        roomListRes.setRecords(roomResList.subList(start, end));
        return roomListRes;
    }
}

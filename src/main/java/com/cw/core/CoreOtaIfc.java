package com.cw.core;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.cw.cache.CustomData;
import com.cw.config.exception.BizException;
import com.cw.core.model.OtaCheckinResult;
import com.cw.core.model.OtaGuestInfo;
import com.cw.entity.Reservation;
import com.cw.exception.DefinedException;
import com.cw.mapper.common.DaoLocal;
import com.cw.pms.model.RoomOrderInfoNode;
import com.cw.pms.request.CwCheckInReq;
import com.cw.pms.request.CwQueryCheckinOrderReq;
import com.cw.pms.response.CwCheckInRes;
import com.cw.pms.response.CwQueryCheckinOrderRes;
import com.cw.pojo.dto.pms.req.reservation.Accompany;
import com.cw.service.context.OtaGlobalContext;
import com.cw.utils.CalculateDate;
import com.cw.utils.SystemUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * OTA核心接口服务类
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2025/7/17 09:18
 **/
@Slf4j
@Service
public class CoreOtaIfc {

    @Autowired
    private CoreRs coreRs;

    @Autowired
    private DaoLocal<?> daoLocal;

    /**
     * OTA入住办理
     *
     * @param req 入住请求参数
     * @return 入住响应结果
     */
    public CwCheckInRes checkin(CwCheckInReq req) {
        String hotelId = OtaGlobalContext.getCurrentHotelId();
        String userId = OtaGlobalContext.getCurrentUserId();

        try {
            // 1. 转换请求参数
            List<OtaGuestInfo> guestInfoList = convertToOtaGuestInfo(req.getGuests());

            // 2. 调用CoreRs的OTA入住方法
            OtaCheckinResult checkinResult = coreRs.otaCheckin(hotelId, req.getRoomOrderIds(),
                    guestInfoList, req.getContactPhone(), userId);

            // 3. 构建响应
            CwCheckInRes response = new CwCheckInRes();
            CwCheckInRes.BizModel bizModel = new CwCheckInRes.BizModel();

            bizModel.setRoomCount(checkinResult.getRoomCount());
            bizModel.setContactPhone(checkinResult.getContactPhone());

            // 转换房间信息
            List<CwCheckInRes.RoomInfo> roomInfoList = checkinResult.getRooms().stream()
                    .map(this::convertToRoomInfo)
                    .collect(Collectors.toList());
            bizModel.setRooms(roomInfoList);

            // 转换入住人员信息
            List<CwCheckInRes.GuestInfo> guestInfoResponseList = checkinResult.getGuests().stream()
                    .map(this::convertToGuestInfo)
                    .collect(Collectors.toList());
            bizModel.setGuests(guestInfoResponseList);

            response.setData(bizModel);
            return response;

        } catch (DefinedException e) {
            e.printStackTrace();
            throw new BizException(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            throw new BizException("入住办理失败：" + e.getMessage());
        }
    }

    /**
     * 查询入住订单信息
     *
     * @param req 查询入住订单请求参数
     * @return 查询入住订单响应结果
     */
    public CwQueryCheckinOrderRes queryCheckinOrder(CwQueryCheckinOrderReq req) {
        String hotelId = OtaGlobalContext.getCurrentHotelId();

        CwQueryCheckinOrderRes res = new CwQueryCheckinOrderRes();
        CwQueryCheckinOrderRes.BizModel bizModel = new CwQueryCheckinOrderRes.BizModel();

        Date sysDate = CalculateDate.getSystemDate();

        try {
            // 查询符合条件的预订信息
            List<Reservation> reservations = queryReservationsByConditions(hotelId, req);

            if (CollectionUtil.isNotEmpty(reservations)) {
                // 转换为RoomOrderInfoNode列表
                List<RoomOrderInfoNode> roomOrderNodes = convertToRoomOrderInfoNodes(reservations);
                bizModel.setRooms(roomOrderNodes);

                // 判断是否可以入住（预订状态为0-预订的订单可以入住）
                boolean canCheckin = true;

                for (Reservation rs : reservations) {
                    if (rs.getReservationStatus() != 0 || !CalculateDate.isEqual(rs.getArrivalDate(), CalculateDate.getSystemDate()) || StrUtil.isBlank(rs.getRoomNumber())) {
                        canCheckin = false;
                        break;
                    }
                }
                bizModel.setLcanCheckin(canCheckin);
            } else {
                bizModel.setLcanCheckin(false);
            }

        } catch (Exception e) {
            // 记录错误日志，但不抛出异常，返回空结果
            log.error("查询入住订单信息失败: {}", e.getMessage(), e);
            bizModel.setLcanCheckin(false);
        }

        res.setData(bizModel);
        return res;
    }

    /**
     * 转换CwCheckInReq.CheckInGuest为OtaGuestInfo
     *
     * @param guests 入住人员信息列表
     * @return OtaGuestInfo列表
     */
    private List<OtaGuestInfo> convertToOtaGuestInfo(List<CwCheckInReq.CheckInGuest> guests) {
        if (CollectionUtil.isEmpty(guests)) {
            return new ArrayList<>();
        }

        List<OtaGuestInfo> guestInfoList = new ArrayList<>();
        for (int i = 0; i < guests.size(); i++) {
            CwCheckInReq.CheckInGuest guest = guests.get(i);
            OtaGuestInfo guestInfo = new OtaGuestInfo();

            guestInfo.setGuestName(guest.getGuestName());
            guestInfo.setIdNumber(guest.getIdNumber());
            guestInfo.setIdPhoto(guest.getIdPhoto());

            // 设置主入住人（第一个默认为主入住人）
            if (i == 0) {
                guestInfo.setMain(true);
            }

            guestInfoList.add(guestInfo);
        }

        return guestInfoList;
    }

    /**
     * 转换OtaCheckinResult.OtaRoomInfo为CwCheckInRes.RoomInfo
     *
     * @param otaRoomInfo OTA房间信息
     * @return 响应房间信息
     */
    private CwCheckInRes.RoomInfo convertToRoomInfo(OtaCheckinResult.OtaRoomInfo otaRoomInfo) {
        CwCheckInRes.RoomInfo roomInfo = new CwCheckInRes.RoomInfo();
        roomInfo.setRoomNo(otaRoomInfo.getRoomNo());
        roomInfo.setRoomPassword(otaRoomInfo.getRoomPassword());
        roomInfo.setRoomTypeDesc(otaRoomInfo.getRoomTypeDesc());
        return roomInfo;
    }

    /**
     * 转换OtaCheckinResult.OtaGuestResult为CwCheckInRes.GuestInfo
     *
     * @param otaGuestResult OTA入住人员结果
     * @return 响应入住人员信息
     */
    private CwCheckInRes.GuestInfo convertToGuestInfo(OtaCheckinResult.OtaGuestResult otaGuestResult) {
        CwCheckInRes.GuestInfo guestInfo = new CwCheckInRes.GuestInfo();
        guestInfo.setGuestName(otaGuestResult.getGuestName());
        guestInfo.setIdNumber(otaGuestResult.getIdNumber());
        guestInfo.setCheckInStatus(otaGuestResult.getCheckInStatus());
        return guestInfo;
    }

    /**
     * 根据查询条件查询预订信息
     *
     * @param hotelId 酒店ID
     * @param req     查询请求参数
     * @return 预订信息列表
     */
    private List<Reservation> queryReservationsByConditions(String hotelId, CwQueryCheckinOrderReq req) {
        StringBuilder jpql = new StringBuilder("from Reservation where hotelId = :hotelId");
        Map<String, Object> params = new HashMap<>();
        params.put("hotelId", hotelId);

        // 添加查询条件
        if (StrUtil.isNotBlank(req.getOtaorderId())) {
            jpql.append(" and (otano = :otano or reservationNumber = :otano)");
            params.put("otano", req.getOtaorderId());
        }

        //if (StrUtil.isNotBlank(req.getGuestName())) {
        //    jpql.append(" and guestName like :guestName");
        //    params.put("guestName", "%" + req.getGuestName() + "%");
        //}
        //
        //if (StrUtil.isNotBlank(req.getPhoneNumber())) {
        //    jpql.append(" and telephone = :telephone");
        //    params.put("telephone", req.getPhoneNumber());
        //}

        // 只查询预订状态为预订(0)和在住(1)的订单
        jpql.append(" and reservationStatus in (0, 1)");

        // 按创建时间倒序排列
        jpql.append(" order by createTime desc");

        // 使用getList方法查询，然后手动限制结果数量
        List<Reservation> allResults = daoLocal.getList(jpql.toString(), params);


        return allResults != null ? allResults : new ArrayList<>();
    }

    /**
     * 将预订信息转换为RoomOrderInfoNode列表
     *
     * @param reservations 预订信息列表
     * @return RoomOrderInfoNode列表
     */
    private List<RoomOrderInfoNode> convertToRoomOrderInfoNodes(List<Reservation> reservations) {
        return reservations.stream().map(this::convertToRoomOrderInfoNode).collect(Collectors.toList());
    }

    /**
     * 将单个预订信息转换为RoomOrderInfoNode
     *
     * @param reservation 预订信息
     * @return RoomOrderInfoNode
     */
    private RoomOrderInfoNode convertToRoomOrderInfoNode(Reservation reservation) {
        RoomOrderInfoNode node = new RoomOrderInfoNode();

        // 基本信息
        node.setRoomOrderId(reservation.getReservationNumber());
        node.setOtano(reservation.getOtano());
        node.setGuestName(reservation.getGuestName());
        node.setPhoneNumber(reservation.getTelephone());
        node.setRoomNo(reservation.getRoomNumber());
        node.setRooms(reservation.getRooms());
        node.setOrderAmount(reservation.getTotalPrice());
        node.setTotalprice(reservation.getTotalPrice() != null ? reservation.getTotalPrice().toString() : "0.00");

        // 日期信息
        if (reservation.getArrivalDate() != null) {
            node.setArrDate(CalculateDate.dateToString(reservation.getArrivalDate()));
        }
        if (reservation.getDepartureDate() != null) {
            node.setDeptDate(CalculateDate.dateToString(reservation.getDepartureDate()));
        }

        // 订单状态转换
        node.setOrderStatus(convertReservationStatus(reservation.getReservationStatus()));

        // 渠道信息
        node.setChannel(CustomData.getDesc(reservation.getHotelId(), reservation.getChannel(), SystemUtil.CustomDataKey.channel));

        node.setDlpwd(reservation.getDlsecret());//reservation.getDlsecret()


        // 房型描述 - 这里可能需要根据实际情况查询房型信息
        node.setRoomTypeDesc(CustomData.getDesc(reservation.getHotelId(), reservation.getRoomType(), SystemUtil.CustomDataKey.roomtype));

        // 处理客人姓名列表（包括主客人和陪同人员）
        List<String> guestNames = new ArrayList<>();
        guestNames.add(reservation.getGuestName());

        // 解析陪同人员信息
        if (StrUtil.isNotBlank(reservation.getAccompany())) {
            try {
                List<Accompany> accompanyList = JSON.parseArray(reservation.getAccompany(), Accompany.class);
                if (CollectionUtil.isNotEmpty(accompanyList)) {
                    guestNames.addAll(accompanyList.stream()
                            .map(Accompany::getGuestName)
                            .filter(StrUtil::isNotBlank)
                            .collect(Collectors.toList()));
                }
            } catch (Exception e) {
            }
        }

        return node;
    }

    /**
     * 转换预订状态为字符串描述
     *
     * @param reservationStatus 预订状态
     * @return 状态描述
     */
    private String convertReservationStatus(Integer reservationStatus) {
        if (reservationStatus == null) {
            return "未知";
        }

        switch (reservationStatus) {
            case 0:
                return "预订";
            case 1:
                return "在住";
            case 2:
                return "离店";
            case 3:
                return "应到未到";
            case 4:
                return "应离未离";
            case -1:
                return "取消";
            default:
                return "未知";
        }
    }
}

package com.cw.core.platform.wechat.wxpay;

import lombok.Data;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/11/8 14:38
 **/
@Data
public class WxNotifyResult {

    String code = "SUCCESS";
    String message = "";

    public WxNotifyResult(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public static WxNotifyResult ok() {
        return new WxNotifyResult("SUCCESS", "SUCCESS");
    }

    public static WxNotifyResult fail(String message) {
        return new WxNotifyResult("FAIL", message);
    }
}

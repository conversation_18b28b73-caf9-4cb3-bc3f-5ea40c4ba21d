package com.cw.core.vendor.msg;

import com.cw.arithmetic.pay.PayAttachInfo;
import com.cw.arithmetic.pay.RefundAttachInfo;
import com.cw.core.vendor.pay.PayVendorHandler;
import com.cw.entity.Vendorconfig;
import com.cw.outsys.pay.StdPayParams;
import com.cw.outsys.pay.StdRefundParams;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.google.common.collect.Maps;
import org.redisson.api.RedissonClient;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 所有 vendor 的景区 client 工具访问实例
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/10/25 16:58
 **/
public abstract class BaseOutMsgVendor implements MsgVendorHandler {

    protected Map<String, Vendorconfig> configMap = Maps.newConcurrentMap();

    @Override
    public void initClient() {//后面改成延迟初始化
    }

    protected Vendorconfig getClient(String projectid) {
        if (!configMap.containsKey(projectid)) {
            throw new RuntimeException("没有找到" + projectid + "对应的 client");
        } else {
            return configMap.get(projectid);
        }
    }

    private void initConfig(Vendorconfig vendorconfig) {
        configMap.put(vendorconfig.getHotelId(), vendorconfig);
        initConfigEvent(vendorconfig);
    }

    @Override
    public void refreshClientConfig(Vendorconfig vendorconfig) {
        configMap.put(vendorconfig.getHotelId(), vendorconfig);
        updateConfigEvent(vendorconfig);
        LoggerFactory.getLogger(this.getClass()).info("{} 刷新{}支付客户端完成", vendorconfig.getHotelId(), vendorconfig.getVtype());
    }

    public void removeClient(String projectid) {
        removeConfigEvent(projectid);
        configMap.remove(projectid);

    }

    /**
     * 配置初始化事件,给子类根据需要重载
     */
    protected void initConfigEvent(Vendorconfig vendorconfig) {
    }


    /**
     * 配置更新事件,给子类根据需要重载
     */
    protected void updateConfigEvent(Vendorconfig vendorconfig) {

    }

    /**
     * 配置删除事件,给子类根据需要重载
     */
    protected void removeConfigEvent(String projectId) {

    }

    protected RedissonClient getRedisssonClient() {
        return SpringUtil.getBean(RedissonClient.class);
    }


    protected String getProjectIdByAppid(String appid) {
        return SystemUtil.CONSOLEHOTELID;
    }

}

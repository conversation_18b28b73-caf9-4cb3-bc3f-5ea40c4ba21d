package com.cw.core.vendor.msg;

import com.cw.entity.Vendorconfig;
import com.cw.exception.DefinedException;
import com.cw.outsys.msg.StdPushRoomClMsg;
import com.cw.outsys.pay.StdPayParams;
import com.cw.outsys.pay.StdPayQueryParams;
import com.cw.outsys.pay.StdRefundParams;
import com.cw.pojo.dto.app.res.AppQrScanPayRes;
import com.cw.pojo.dto.app.res.AppQueryPayRes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 声明厂商处理能力的接口
 * 实现类根据实例处理能力去调用相应的方法做实现
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2020/5/14 15:32
 **/
public interface MsgVendorHandler {

    /**
     * 启动时初始化所有处理器
     */
    void initClient();

    /**
     * 刷新vendor 内的处理器
     *
     * @param vendorconfig
     */
    void refreshClientConfig(Vendorconfig vendorconfig);


    /**
     * 客房打扫消息推送. 标准打扫消息推送到企微上
     *
     * @param clMsg
     * @param <T>
     * @return
     */
    default <T> T sendWxCpmsg(StdPushRoomClMsg clMsg) throws DefinedException {
        return null;
    }


}

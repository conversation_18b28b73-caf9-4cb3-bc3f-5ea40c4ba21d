package com.cw.core.vendor.msg.wxcp;

import com.cw.cache.RedisTool;
import com.cw.core.vendor.msg.BaseOutMsgVendor;
import com.cw.entity.Vendorconfig;
import com.cw.exception.DefinedException;
import com.cw.outsys.msg.StdPushRoomClMsg;
import com.cw.pojo.dto.common.res.Common_response;
import com.google.common.collect.Maps;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.bean.WxCpOauth2UserInfo;
import me.chanjar.weixin.cp.config.WxCpConfigStorage;
import me.chanjar.weixin.cp.config.impl.WxCpRedissonConfigImpl;
import me.chanjar.weixin.cp.bean.WxCpUser;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.stereotype.Service;

import java.rmi.Remote;
import java.util.Map;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2025/5/19 01:25
 **/
@Service
public class WxCpVendor extends BaseOutMsgVendor {

    private static Map<String, WxCpService> cpServices = Maps.newConcurrentMap();

    @Override
    public Common_response sendWxCpmsg(StdPushRoomClMsg clMsg) throws DefinedException {
        WxCpService wxCpService = cpServices.get(clMsg.getProjectId());
        if (wxCpService == null) {
            throw new DefinedException("企业微信服务未初始化，酒店ID: " + clMsg.getProjectId());
        }

        WxCpMessage message = WxCpMessage.TEXT()
                .agentId(wxCpService.getWxCpConfigStorage().getAgentId()) // 设置agentId
                .toUser(clMsg.getUserId()) // Assuming targetId is the WxCp userId
                .content(clMsg.getRoom())
                .build();

        try {
            wxCpService.getMessageService().send(message);
            return new Common_response();
        } catch (WxErrorException e) {
            // Log error e.printStackTrace(); or use a logger
            throw new DefinedException("发送企业微信消息失败: " + e.getMessage());
        }
    }

    @Override
    protected void initConfigEvent(Vendorconfig vendorconfig) {
        WxCpService wxCpService = new WxCpServiceImpl();
        WxCpConfigStorage wxCpConfigStorage = generateWxCpConfigStorage(vendorconfig);
        wxCpService.setWxCpConfigStorage(wxCpConfigStorage);

        cpServices.put(vendorconfig.getHotelId(), wxCpService);
    }

    @Override
    protected void updateConfigEvent(Vendorconfig vendorconfig) {

        if (cpServices.containsKey(vendorconfig.getHotelId())) {
            WxCpService wxCpService = new WxCpServiceImpl();
            WxCpConfigStorage wxCpConfigStorage = generateWxCpConfigStorage(vendorconfig);
            wxCpService.setWxCpConfigStorage(wxCpConfigStorage);
            cpServices.put(vendorconfig.getHotelId(), wxCpService);
        } else {
            WxCpService wxCpService = cpServices.get(vendorconfig.getHotelId());
            wxCpService.setWxCpConfigStorage(generateWxCpConfigStorage(vendorconfig));
        }
    }

    private WxCpConfigStorage generateWxCpConfigStorage(Vendorconfig vendorconfig) {
        WxCpConfigStorage wxCpConfigStorage = new WxCpRedissonConfigImpl(RedisTool.getRedissonClient());
        //wxCpConfigStorage.set(vendorconfig.getAppid());
        //wxCpConfigStorage.setAgentId(vendorconfig.getMchid());
        //wxCpConfigStorage.setAgentSecret(vendorconfig.getAppsecrect());
        //wxCpConfigStorage.setToken(vendorconfig.getUserpwd());
        //wxCpConfigStorage.setAesKey(vendorconfig.getMchkey());
        return wxCpConfigStorage;
    }

    /**
     * 构建企业微信静默授权链接
     *
     * @param hotelId     酒店ID
     * @param redirectUri 授权后重定向的回调链接地址
     * @param state       重定向后会带上state参数，开发者可以填写a-zA-Z0-9的参数值，最多128字节
     * @return 授权链接
     * @throws DefinedException 如果服务未初始化
     */
    public String buildSilentAuthUrl(String hotelId, String redirectUri, String state) throws DefinedException {
        WxCpService wxCpService = cpServices.get(hotelId);
        if (wxCpService == null) {
            throw new DefinedException("企业微信服务未初始化，酒店ID: " + hotelId);
        }
        return wxCpService.getOauth2Service().buildAuthorizationUrl(redirectUri, WxConsts.OAuth2Scope.SNSAPI_BASE, state);
    }

    /**
     * 根据授权回调获取的code获取用户企业微信ID
     *
     * @param hotelId 酒店ID
     * @param code    通过成员授权获取到的code，最大为512字节。每次成员授权带上的code将不一样，code只能使用一次，5分钟未被使用自动过期。
     * @return 用户企业微信ID (userId)
     * @throws DefinedException 如果服务未初始化或获取用户信息失败
     */
    public String getUserIdByCode(String hotelId, String code) throws DefinedException {
        WxCpService wxCpService = cpServices.get(hotelId);
        if (wxCpService == null) {
            throw new DefinedException("企业微信服务未初始化，酒店ID: " + hotelId);
        }
        try {
            WxCpOauth2UserInfo wxCpUser = wxCpService.getOauth2Service().getUserInfo(code);
            if (wxCpUser != null && wxCpUser.getUserId() != null) {
                return wxCpUser.getUserId();
            }
            throw new DefinedException("无法获取企业微信用户信息或UserId为空");
        } catch (WxErrorException e) {
            // Log error e.printStackTrace(); or use a logger
            throw new DefinedException("通过code获取企业微信用户ID失败: " + e.getMessage());
        }
    }

}

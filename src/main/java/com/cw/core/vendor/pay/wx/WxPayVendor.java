package com.cw.core.vendor.pay.wx;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.format.DateParser;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.net.Ipv4Util;
import cn.hutool.core.net.NetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.alibaba.fastjson.JSON;
import com.cw.arithmetic.pay.PayAttachInfo;
import com.cw.arithmetic.pay.RefundAttachInfo;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.VendorConfigCache;
import com.cw.config.exception.CustomException;
import com.cw.core.handler.VendorAdapter;
import com.cw.core.platform.wechat.wxpay.WxNotifyResult;
import com.cw.core.vendor.pay.BaseOutPayVendor;
import com.cw.entity.Vendorconfig;
import com.cw.exception.DefinedException;
import com.cw.outsys.pay.StdPayParams;
import com.cw.outsys.pay.StdPayQueryParams;
import com.cw.outsys.pay.StdRefundParams;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.app.res.AppQrScanPayRes;
import com.cw.pojo.dto.app.res.AppQrcodePayRes;
import com.cw.pojo.dto.app.res.AppQueryPayRes;
import com.cw.pojo.dto.app.res.AppWxJsApiPayRes;
import com.cw.utils.CalculateDate;
import com.cw.utils.CwUtils;
import com.cw.utils.RedisKey;
import com.cw.utils.datetime.DateStyle;
import com.cw.utils.enums.GlobalDataType;
import com.cw.utils.enums.VendorType;
import com.cw.utils.enums.pay.OnlinePayMethod;
import com.cw.utils.enums.pay.PayProcessStatus;
import com.cw.utils.pay.PayUtil;
import com.cw.utils.pay.wx.WechatPayHttpHeaders;
import com.cw.utils.pay.wx.WxPayUtil;
import com.github.binarywang.wxpay.bean.notify.SignatureHeader;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyV3Result;
import com.github.binarywang.wxpay.bean.notify.WxPayRefundNotifyV3Result;
import com.github.binarywang.wxpay.bean.request.*;
import com.github.binarywang.wxpay.bean.result.*;
import com.github.binarywang.wxpay.bean.result.enums.TradeTypeEnum;
import com.github.binarywang.wxpay.config.WxPayConfig;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.github.binarywang.wxpay.service.impl.WxPayServiceImpl;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMapCache;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.math.BigDecimal;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/10/21 15:29
 **/
@Slf4j
@VendorAdapter(vendorType = VendorType.WX_PAY)
public class WxPayVendor extends BaseOutPayVendor {

    private static Map<String, WxPayService> payServices = Maps.newConcurrentMap(); //存放每个 projectid 的微信支付处理实例对象


    @Override
    public AppWxJsApiPayRes wxJsapiPay(StdPayParams stdPayParams) throws DefinedException {
        WxPayService wxPayService = getWxPayService(stdPayParams.getProjectId());
        VendorConfigCache configCache = GlobalCache.getDataStructure().getCache(GlobalDataType.VENDORCONFIG);
        Vendorconfig appVendorConfig = null;
        if (stdPayParams.getPaymode() == OnlinePayMethod.WX_JSAPI) {
            appVendorConfig = configCache.getRecord(stdPayParams.getProjectId(), VendorType.WX_APP.name());
        } else {
            appVendorConfig = configCache.getRecord(stdPayParams.getProjectId(), VendorType.WX_MP.name());
        }
        if (appVendorConfig == null) {//获取配置
            throw new DefinedException("支付配置异常", ResultCode.PAYFAIL.code());
        }

        WxPayUnifiedOrderV3Request v3PayRequest = new WxPayUnifiedOrderV3Request();
        v3PayRequest.setAppid(appVendorConfig.getAppid());//APPid
        v3PayRequest.setMchid(wxPayService.getConfig().getMchId());//直连商户号
        WxPayUnifiedOrderV3Request.Amount amount = new WxPayUnifiedOrderV3Request.Amount();
        amount.setTotal(PayUtil.Yuan2Fen(stdPayParams.getTotalPay().doubleValue()));//支付金额.对应分
        v3PayRequest.setAmount(amount);
        WxPayUnifiedOrderV3Request.Payer payer = new WxPayUnifiedOrderV3Request.Payer();
        payer.setOpenid(stdPayParams.getPayerId());  //支付用户
        v3PayRequest.setPayer(payer);

        PayAttachInfo payAttachInfo = generatePayAttach(stdPayParams);
        RMapCache<String, PayAttachInfo> attachInfoRMapCache = getRedisssonClient().getMapCache(RedisKey.PAY_ATTACHINFO);
        String rskey = payAttachInfo.getOutTradeNo();
        attachInfoRMapCache.put(rskey, payAttachInfo, 1, TimeUnit.DAYS);
        String time = DateUtil.format(DateTime.of(stdPayParams.getExpireTime()), WxPayUtil.WXDATETIMEZONEFORMAT);  // getPayExpireTime(booking_rsList, projectid);
        v3PayRequest.setTimeExpire(time);
        v3PayRequest.setDescription(StrUtil.sub(stdPayParams.getOrderDesc(), 0, 36));//购买支付的商品描述 微信最多128位
        v3PayRequest.setOutTradeNo(stdPayParams.getOutTradeNo());
        v3PayRequest.setNotifyUrl(stdPayParams.getNotifyDomain() + "/pay/notify/jsapi/order/" + wxPayService.getConfig().getAppId()); //支付成功回调地址
        log.info("{}发起支付请求:{}", stdPayParams.getPaymode() == OnlinePayMethod.WX_JSAPI ? "小程序" : "公众号网页", JSON.toJSONString(v3PayRequest));
        WxPayUnifiedOrderV3Result.JsapiResult jsapiResult = null;
        AppWxJsApiPayRes res = new AppWxJsApiPayRes();
        try {
            jsapiResult = wxPayService.createOrderV3(TradeTypeEnum.JSAPI, v3PayRequest);
        } catch (WxPayException e) {
            e.printStackTrace();
            log.error("{}  请求支付错误  流水号:{}", stdPayParams.getOrderids().size() == 1 ? "单订单" : "购物车提交", stdPayParams.getOutTradeNo());
            throw new DefinedException(e.getMessage(), ResultCode.PAYFAIL.code());
        }
        if (jsapiResult != null) {
            log.info("支付获取到微信返回:{}", jsapiResult);
            BeanUtil.copyProperties(jsapiResult, res);
        }
        return res;
    }

    @Override
    public AppQrScanPayRes scanPay(StdPayParams stdPayParams) throws DefinedException {
    	return wxScanPay(stdPayParams);
    }
    
    @Override
    public AppQrScanPayRes wxScanPay(StdPayParams stdPayParams) throws DefinedException {
        //收银员使用扫码设备读取微信用户付款码以后，二维码或条码信息会传送至商户收银台，由商户收银台或者商户后台调用该接口发起支付。
        //提醒1：提交支付请求后微信会同步返回支付结果。当返回结果为“系统错误”时，商户系统等待5秒后调用【查询订单API】，查询支付实际交易结果；当返回结果为“USERPAYING”时，商户系统可设置间隔时间(建议10秒)重新查询支付结果，直到支付成功或超时(建议45秒)；
        //提醒2：在调用查询接口返回后，如果交易状况不明晰，请调用【撤销订单API】，此时如果交易失败则关闭订单，该单不能再支付成功；如果交易成功，则将扣款退回到用户账户。当撤销无返回或错误时，请再次调用。注意：请勿扣款后立即调用【撤销订单API】,建议至少15秒后再调用。撤销订单API需要双向证书。


        WxPayService wxPayService = getWxPayService(stdPayParams.getProjectId());

        WxPayMicropayRequest micropayRequest = new WxPayMicropayRequest();
        micropayRequest.setAppid(wxPayService.getConfig().getAppId());//服务号APPID  TODO 更换为系统服务商的appid
        micropayRequest.setMchId(wxPayService.getConfig().getMchId());//wxPayService.getConfig().getMchId()

        micropayRequest.setTotalFee(PayUtil.Yuan2Fen(stdPayParams.getTotalPay().doubleValue()));

        PayAttachInfo payAttachInfo = generatePayAttach(stdPayParams);
        RMapCache<String, PayAttachInfo> attachInfoRMapCache = getRedisssonClient().getMapCache(RedisKey.PAY_ATTACHINFO);
        String rskey = payAttachInfo.getOutTradeNo();
        attachInfoRMapCache.put(rskey, payAttachInfo, 1, TimeUnit.DAYS);
        String time = DateUtil.format(DateTime.of(stdPayParams.getExpireTime()), DateStyle.YYYYMMDDHHMMSS.getValue());  // getPayExpireTime(booking_rsList, projectid);
        micropayRequest.setTimeExpire(time);

        micropayRequest.setSpbillCreateIp(NetUtil.getLocalhostStr());
        ;
        micropayRequest.setBody(StrUtil.sub(stdPayParams.getOrderDesc(), 0, 36));//购买支付的商品描述 微信最多128位
        micropayRequest.setOutTradeNo(stdPayParams.getOutTradeNo());     //需要根据付款码生成一个幂等的ID
        micropayRequest.setAuthCode(stdPayParams.getQrAuthCode());       //识别到的用户手机二维码



        WxPayMicropayResult result = null;
        try {
            result = wxPayService.micropay(micropayRequest);

		} catch (WxPayException e) {
			AppQrScanPayRes res = new AppQrScanPayRes();
			if (StrUtil.equals(e.getErrCode(), "USERPAYING")) {
				res.setScan_status_code(PayProcessStatus.PAY_WAIT);
			} else {
				res.setScan_status_code(PayProcessStatus.PAY_FAIL);
			}
			res.setAlert_reason("请重新出示微信付款码 " + e.getErrCodeDes());
			return res;
		}

        log.info("微信付款码支付返回:{}  ", result);

        Integer standardPayStatus = PayProcessStatus.getWxPayStatus(result.getReturnCode(), result.getResultCode(), result.getTradeType(), result.getErrCode());

        AppQrScanPayRes res = new AppQrScanPayRes();

        res.setScan_status_code(standardPayStatus);
        res.setAlert_reason(result.getErrCodeDes());

        //TODO 线上支付成功.延迟一定时间后.刷新客人账单页面

        return res;
    }

    @Override
    public AppQrcodePayRes wxNativeQrcodePay(StdPayParams stdPayParams) throws DefinedException {
        WxPayService wxPayService = getWxPayService(stdPayParams.getProjectId());

        WxPayUnifiedOrderV3Request v3PayRequest = new WxPayUnifiedOrderV3Request();
        v3PayRequest.setAppid(wxPayService.getConfig().getAppId());//APPid
        v3PayRequest.setMchid(wxPayService.getConfig().getMchId());//直连商户号
        WxPayUnifiedOrderV3Request.Amount amount = new WxPayUnifiedOrderV3Request.Amount();
        amount.setTotal(PayUtil.Yuan2Fen(stdPayParams.getTotalPay().doubleValue()));//支付金额.对应分
        v3PayRequest.setAmount(amount);

        PayAttachInfo payAttachInfo = generatePayAttach(stdPayParams);
        payAttachInfo.setLnotify(true);
        RMapCache<String, PayAttachInfo> attachInfoRMapCache = getRedisssonClient().getMapCache(RedisKey.PAY_ATTACHINFO);
        String rskey = payAttachInfo.getOutTradeNo();
        attachInfoRMapCache.put(rskey, payAttachInfo, 1, TimeUnit.DAYS);

//        v3PayRequest.setAttach(JSON.toJSONString(payAttachInfo));//自定义附加数据.会在查询以及通知回调中返回 可以返回要关联支付的订单号
        v3PayRequest.setDescription(StrUtil.sub(stdPayParams.getOrderDesc(), 0, 36));//购买支付的商品描述
        v3PayRequest.setOutTradeNo(stdPayParams.getOutTradeNo());
        v3PayRequest.setNotifyUrl(stdPayParams.getNotifyDomain() + "/pay/notify/jsapi/order/" + wxPayService.getConfig().getAppId()); //支付成功回调地址

        AppQrcodePayRes res = new AppQrcodePayRes();
        try {
            String s = wxPayService.createOrderV3(TradeTypeEnum.NATIVE, v3PayRequest);

            QrConfig config = new QrConfig(280, 280);
            config.setRatio(5);
            String base64qr = QrCodeUtil.generateAsBase64(s, config,
                    ImgUtil.IMAGE_TYPE_PNG);
            res.setQrcode(base64qr);
//            String base64qr = QrCodeUtil.generateAsBase64(s, config, ImgUtil.IMAGE_TYPE_PNG);
//            res.setQrcode(base64qr);
        } catch (WxPayException e) {
            e.printStackTrace();
            throw new CustomException(ResultJson.failure(ResultCode.SERVER_ERROR).msg("微信PC端扫码支付发起异常"));
        }
        return res;
    }


    @Override
    public WxNotifyResult payCallBack(String appid, String body, HttpServletRequest request) {
        WxPayService wxPayService = payServices.get(getProjectIdByAppid(appid));
        SignatureHeader signatureHeader = new SignatureHeader();
        signatureHeader.setSignature(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_SIGNATURE));
        signatureHeader.setNonce(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_NONCE));
        signatureHeader.setTimeStamp(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_TIMESTAMP));
        signatureHeader.setSerial(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_SERIAL));
        try {//解密 确认是否为支付成功
            WxPayNotifyV3Result v3Result = wxPayService.parseOrderNotifyV3Result(body, signatureHeader);
            //getCorePay().createPrepayWithNotify(OnlinePayType.WX, v3Result.getResult().getOutTradeNo(), v3Result.getResult().getTransactionId());

        } catch (WxPayException e) {
            log.error(appid + "支付验签失败" + e.getMessage());
//            e.printStackTrace();
            return WxNotifyResult.fail("支付验签失败");
        }
        return WxNotifyResult.ok();
    }

    @Override
    public void refundPay(StdRefundParams stdRefundParams) throws DefinedException {
        WxPayService wxPayService = getWxPayService(stdRefundParams.getAttachProjectId());
//        		payServices.get(stdRefundParams.getAttachProjectId());

        WxPayRefundV3Request refundV3Request = new WxPayRefundV3Request();
        WxPayRefundV3Request.Amount amount = new WxPayRefundV3Request.Amount();
        BigDecimal total = stdRefundParams.getOrgAmount(); //prepay.getOrgamount();  发起支付/购物车合并支付时的总金额
        BigDecimal refund = stdRefundParams.getRefund();// cancelData.getRefund();

        amount.setTotal(PayUtil.Yuan2Fen(total.doubleValue()));//原始支付总金额
        amount.setRefund(PayUtil.Yuan2Fen(refund.doubleValue()));//退款金额
        amount.setCurrency(PayUtil.DEFAULT_CURRENCY);
        refundV3Request.setAmount(amount);

        log.info("即将对订单做退款 原支付金额 {} 现在退款 {}", amount.getTotal(), amount.getRefund());


        refundV3Request.setTransactionId(stdRefundParams.getTransactionId());//原支付单号
        refundV3Request.setOutTradeNo(stdRefundParams.getOutTradeNo());//原商户订单号
        refundV3Request.setOutRefundNo(stdRefundParams.getOutRefundNo());//生成一个本地退款流水号
		if (StrUtil.isNotBlank(stdRefundParams.getNotifyDomain())) {
			refundV3Request.setNotifyUrl(stdRefundParams.getNotifyDomain() + "/pay/notify/jsapi/refund/" + wxPayService.getConfig().getAppId());// TODO 通知地址改下前缀
		}
        RefundAttachInfo refundAttachInfo = generateRefundAttachInfo(stdRefundParams);
        RMapCache<String, RefundAttachInfo> attachInfoRMapCache = getRedisssonClient().getMapCache(RedisKey.REFUND_ATTACHINFO);
        String rskey = stdRefundParams.getOutRefundNo();//退款信息 用单个订单号作为 KEY
        attachInfoRMapCache.put(rskey, refundAttachInfo, 1, TimeUnit.DAYS);

        WxPayRefundV3Result v3Result;
        try {
            v3Result = wxPayService.refundV3(refundV3Request);
        } catch (WxPayException e) {
            e.printStackTrace();
            log.error("发起微信退款失败");
            throw new CustomException(ResultJson.failure(ResultCode.BAD_REQUEST).msg("发起微信退款失败:" + e.getErrCodeDes()));
        }

    }


    @Override
    public WxNotifyResult payAnonymousCallBack(String appid, String body, HttpServletRequest request) {
        WxPayService wxPayService = payServices.get(getProjectIdByAppid(appid));
        SignatureHeader signatureHeader = new SignatureHeader();
        signatureHeader.setSignature(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_SIGNATURE));
        signatureHeader.setNonce(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_NONCE));
        signatureHeader.setTimeStamp(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_TIMESTAMP));
        signatureHeader.setSerial(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_SERIAL));
        try {//解密 确认是否为支付成功
            WxPayNotifyV3Result v3Result = wxPayService.parseOrderNotifyV3Result(body, signatureHeader);
            //getCorePay().createPassByOrderWithNotify(OnlinePayType.WX, v3Result.getResult().getOutTradeNo(), v3Result.getResult().getTransactionId());


        } catch (WxPayException e) {
            log.error(appid + "支付验签失败" + e.getMessage());
//            e.printStackTrace();
            return WxNotifyResult.fail("支付验签失败");
        }
        return WxNotifyResult.ok();
    }


    @Override
    public AppQueryPayRes queryPay(StdPayQueryParams stdPayQueryParams) throws DefinedException {
        WxPayService wxPayService = payServices.get(stdPayQueryParams.getProjectId());
        AppQueryPayRes res = new AppQueryPayRes();
        if (wxPayService == null) {
            return res;
        }
        if (stdPayQueryParams.isLscanPay()) {
            WxPayOrderQueryRequest queryRequest = new WxPayOrderQueryRequest();
            queryRequest.setAppid(wxPayService.getConfig().getAppId());//服务号APPID  TODO 更换为系统
            queryRequest.setOutTradeNo(stdPayQueryParams.getOutTradeNo());
            queryRequest.setMchId(wxPayService.getConfig().getMchId());//直连商户号

            WxPayOrderQueryResult result = null;
            try {
                result = wxPayService.queryOrder(queryRequest);
                String state = result.getTradeState();
                res.setPayStatus(state.equals(CwUtils.SUCCESSSTR) ? 1 : 0);
                res.setTransId(result.getTransactionId());
                //res.setTotalAmount(PayUtil.Fen2Yuan(result.getTotalFee()));
                res.setOutTradeNo(stdPayQueryParams.getOutTradeNo());
                res.setPaytime(result.getTimeEnd()); //TODO 还没按统一格式返回.这个客人也看不到
				if (null != result.getTotalFee()) {
					res.setTotalAmount(PayUtil.Fen2Yuan(result.getTotalFee()));
				}
				Integer scanPayStatus = PayProcessStatus.getWxPayStatus(result.getReturnCode(), result.getResultCode(), result.getTradeType(),
						StrUtil.firstNonBlank(result.getTradeState(), result.getErrCode()));
				res.setScan_status_code(scanPayStatus);
				res.setAlert_reason(result.getErrCodeDes());
                return res;
            } catch (WxPayException e) {
                return res;
            }
        } else {
            WxPayOrderQueryV3Request queryV3Request = new WxPayOrderQueryV3Request();
            queryV3Request.setOutTradeNo(stdPayQueryParams.getOutTradeNo());
            queryV3Request.setMchid(wxPayService.getConfig().getMchId());//直连商户号

            try {
                WxPayOrderQueryV3Result v3Result = wxPayService.queryOrderV3(queryV3Request);
                String state = v3Result.getTradeState();
                res.setPayStatus(state.equals(CwUtils.SUCCESSSTR) ? 1 : 0);
                res.setTransId(v3Result.getTransactionId());
                res.setOutTradeNo(stdPayQueryParams.getOutTradeNo());
                res.setPaytime(CalculateDate.dateFormat(CalculateDate.stringToDate(v3Result.getSuccessTime(), DateStyle.YYYY_MM_DD_T_HH_MM_SS),
                        DateStyle.YYYY_MM_DD_HH_MM_SS.getValue()));
                return res;
            } catch (WxPayException e) {
                return res;
            }
        }


    }

    @Override
    public WxNotifyResult refundAnonymousCallBack(String appid, String body, HttpServletRequest request) {
        WxPayService wxPayService = payServices.get(getProjectIdByAppid(appid));
        SignatureHeader signatureHeader = new SignatureHeader();
        signatureHeader.setSignature(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_SIGNATURE));
        signatureHeader.setNonce(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_NONCE));
        signatureHeader.setTimeStamp(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_TIMESTAMP));
        signatureHeader.setSerial(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_SERIAL));
        try {
            WxPayRefundNotifyV3Result v3Result = wxPayService.parseRefundNotifyV3Result(body, signatureHeader);
            //getCorePay().writePassByRefundInfoWithNotify(OnlinePayType.WX, v3Result.getResult().getOutRefundNo());
        } catch (WxPayException e) {
            log.error(e.getMessage());
            return WxNotifyResult.fail("验签失败");
        }
        return WxNotifyResult.ok();
    }

    @Override
    public WxNotifyResult refundCallBack(String appid, String body, HttpServletRequest request) {
        WxPayService wxPayService = payServices.get(getProjectIdByAppid(appid));
        SignatureHeader signatureHeader = new SignatureHeader();
        signatureHeader.setSignature(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_SIGNATURE));
        signatureHeader.setNonce(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_NONCE));
        signatureHeader.setTimeStamp(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_TIMESTAMP));
        signatureHeader.setSerial(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_SERIAL));
        try {
            WxPayRefundNotifyV3Result v3Result = wxPayService.parseRefundNotifyV3Result(body, signatureHeader);
            //getCorePay().writeRefundInfoWithNotify(OnlinePayType.WX, v3Result.getResult().getOutRefundNo());
        } catch (WxPayException e) {
            log.error(e.getMessage());
            return WxNotifyResult.fail("验签失败");
        }
        return WxNotifyResult.ok();
    }


    private WxPayService getWxPayService(String projectId) throws DefinedException {
        if (!payServices.containsKey(projectId)) {
            VendorConfigCache configCache = GlobalCache.getDataStructure().getCache(GlobalDataType.VENDORCONFIG);
            Vendorconfig appVendorConfig = configCache.getVendorConfig(projectId, VendorType.WX_PAY);
            if (appVendorConfig == null) {//获取配置
                throw new DefinedException("微信支付配置异常", ResultCode.PAYFAIL.code());
            }
            WxPayService wxPayService = new WxPayServiceImpl();
            wxPayService.setConfig(generateNewPayConfig(appVendorConfig));
            payServices.put(projectId, wxPayService);
        }
        return payServices.get(projectId);
    }


    @Override
    protected void initConfigEvent(Vendorconfig vendorconfig) {
        WxPayService wxPayService = new WxPayServiceImpl();
        wxPayService.setConfig(generateNewPayConfig(vendorconfig));
        payServices.put(vendorconfig.getHotelId(), wxPayService);
    }

    @Override
    protected void updateConfigEvent(Vendorconfig vendorconfig) {
        if (!payServices.containsKey(vendorconfig.getHotelId())) {
            WxPayService wxPayService = new WxPayServiceImpl();
            wxPayService.setConfig(generateNewPayConfig(vendorconfig));
            payServices.put(vendorconfig.getHotelId(), wxPayService);
        } else {
            WxPayService wxPayService = payServices.get(vendorconfig.getAppid());
            wxPayService.setConfig(generateNewPayConfig(vendorconfig));
        }
    }

    @Override
    protected void removeConfigEvent(String hotelId) {
        if (payServices.containsKey(hotelId)) {
            payServices.remove(hotelId);
        }
    }

    private WxPayConfig generateNewPayConfig(Vendorconfig payVendorConfig) {
        WxPayConfig payConfig = new WxPayConfig();
        payConfig.setAppId(payVendorConfig.getAppid());
        payConfig.setMchId(payVendorConfig.getMchid());
        payConfig.setMchKey(payVendorConfig.getUserpwd());//商户key 目前统一用v3 密钥就可以了 这个用处不大
        payConfig.setPrivateKeyPath(getPrivateKeyPath(payConfig.getAppId(), payVendorConfig.getPrivatekey()));//放回一个临时文件地址
        payConfig.setPrivateCertPath(getPrivateCertPath(payConfig.getAppId(), payVendorConfig.getCertkey()));//返回一个临时文件地址
        payConfig.setApiV3Key(payVendorConfig.getWxpayv3key()); //目前统一用v3 密钥就可以了
        return payConfig;
    }

    private String getPrivateKeyPath(String appid, String content) {
        String tmpDirsLocation = System.getProperty("java.io.tmpdir");
        File file = new File(StrUtil.format(tmpDirsLocation + "/{}key.pem", appid));
        file.deleteOnExit();
        FileUtil.touch(file);
        FileUtil.writeBytes(content.getBytes(), file);
        return file.getPath();
    }

    private String getPrivateCertPath(String appid, String content) {
        String tmpDirsLocation = System.getProperty("java.io.tmpdir");
        File file = new File(StrUtil.format(tmpDirsLocation + "/{}cert.pem", appid));
        file.deleteOnExit();
        FileUtil.touch(file);
        FileUtil.writeBytes(content.getBytes(), file);
        return file.getPath();
    }

}

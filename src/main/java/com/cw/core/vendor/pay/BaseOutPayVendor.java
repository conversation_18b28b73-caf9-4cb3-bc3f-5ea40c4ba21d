package com.cw.core.vendor.pay;

import cn.hutool.core.annotation.AnnotationUtil;
import com.cw.arithmetic.pay.PayAttachInfo;
import com.cw.arithmetic.pay.RefundAttachInfo;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.VendorConfigCache;
import com.cw.core.handler.VendorAdapter;
import com.cw.entity.Vendorconfig;
import com.cw.outsys.pay.PassOrderPayParams;
import com.cw.outsys.pay.StdPayParams;
import com.cw.outsys.pay.StdRefundParams;
import com.cw.utils.CalculateDate;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.datetime.DateStyle;
import com.cw.utils.enums.GlobalDataType;
import com.google.common.collect.Maps;
import org.redisson.api.RedissonClient;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 所有 vendor 的景区 client 工具访问实例
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/10/25 16:58
 **/
public abstract class BaseOutPayVendor implements PayVendorHandler {

    protected Map<String, Vendorconfig> configMap = Maps.newConcurrentMap();

    @Override
    public void initClient() {//后面改成延迟初始化
        /*VendorAdapter adapter = AnnotationUtil.getAnnotation(this.getClass(), VendorAdapter.class);
        if (adapter != null) {
            VendorConfigCache configCache = GlobalCache.getDataStructure().getCache(GlobalDataType.VENDORCONFIG);
            List<Vendorconfig> configs = configCache.getVendorTypeConfig(adapter.vendorType());
            if (configs.size() > 0) {
                for (Vendorconfig vendorconfig : configCache.getVendorTypeConfig(adapter.vendorType())) {
                    initConfig(vendorconfig);
                    LoggerFactory.getLogger(this.getClass()).info("{} {}初始化支付客户端完成", vendorconfig.getHotelId(), vendorconfig.getVtype());
                }
            }
        }*/
    }

    protected Vendorconfig getClient(String projectid) {
        if (!configMap.containsKey(projectid)) {
            throw new RuntimeException("没有找到" + projectid + "对应的 client");
        } else {
            return configMap.get(projectid);
        }
    }

    private void initConfig(Vendorconfig vendorconfig) {
        configMap.put(vendorconfig.getHotelId(), vendorconfig);
        initConfigEvent(vendorconfig);
    }

    @Override
    public void refreshClientConfig(Vendorconfig vendorconfig) {
        configMap.put(vendorconfig.getHotelId(), vendorconfig);
        updateConfigEvent(vendorconfig);
        LoggerFactory.getLogger(this.getClass()).info("{} 刷新{}支付客户端完成", vendorconfig.getHotelId(), vendorconfig.getVtype());
    }

    public void removeClient(String projectid) {
        removeConfigEvent(projectid);
        configMap.remove(projectid);

    }

    /**
     * 配置初始化事件,给子类根据需要重载
     */
    protected void initConfigEvent(Vendorconfig vendorconfig) {
    }


    /**
     * 配置更新事件,给子类根据需要重载
     */
    protected void updateConfigEvent(Vendorconfig vendorconfig) {

    }

    /**
     * 配置删除事件,给子类根据需要重载
     */
    protected void removeConfigEvent(String projectId) {

    }

    protected RedissonClient getRedisssonClient() {
        return SpringUtil.getBean(RedissonClient.class);
    }


    protected PayAttachInfo generatePayAttach(StdPayParams stdPayParams) {
        PayAttachInfo payAttachInfo = new PayAttachInfo();
        payAttachInfo.setBookingids(stdPayParams.getOrderids()); //要批量创建预付款的订单号
        payAttachInfo.setProejectid(stdPayParams.getProjectId());
        payAttachInfo.setTotalPay(stdPayParams.getTotalPay());
        payAttachInfo.setOutTradeNo(stdPayParams.getOutTradeNo());
        payAttachInfo.setScene(stdPayParams.getPayscene());
        return payAttachInfo;
    }


    protected RefundAttachInfo generateRefundAttachInfo(StdRefundParams stdRefundParams) {
        RefundAttachInfo refundAttachInfo = new RefundAttachInfo();
        refundAttachInfo.setBookingid(stdRefundParams.getAttachBookingId());
        refundAttachInfo.setUid(stdRefundParams.getAttachUid());
        refundAttachInfo.setProejectid(stdRefundParams.getAttachProjectId());
        refundAttachInfo.setReqTime(LocalDateTime.now().toString());
        refundAttachInfo.setRefundAmount(stdRefundParams.getAttachRefundAmount());
        refundAttachInfo.setPayscene(stdRefundParams.getPayscene());
        return refundAttachInfo;
    }

    protected String getProjectIdByAppid(String appid) {
        return SystemUtil.CONSOLEHOTELID;
    }

}

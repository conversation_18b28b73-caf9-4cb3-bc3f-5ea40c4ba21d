package com.cw.core.vendor.dl.tt;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.VendorConfigCache;
import com.cw.core.handler.VendorAdapter;
import com.cw.core.vendor.dl.BaseOutDlVendor;
import com.cw.entity.Vendorconfig;
import com.cw.exception.DefinedException;
import com.cw.outsys.dl.StdDlParams;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.dto.app.res.AppDlPasswordRes;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.GlobalDataType;
import com.cw.utils.enums.VendorType;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMapCache;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.security.NoSuchAlgorithmException;
import java.util.concurrent.TimeUnit;

/**
 * TT通通锁厂商实现
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2025/1/3 15:50
 **/
@Slf4j
@VendorAdapter(vendorType = VendorType.TT_DL)
public class TTDlVendor extends BaseOutDlVendor {

    private static final String AUTH_URL = "https://cnapi.ttlock.com/oauth2/token";
    private static final String KEYBOARD_PASSWORD_URL = "https://cnapi.ttlock.com/v3/keyboardPwd/get";
    private static final String KEYBOARD_PASSWORD_LIST_URL = "https://cnapi.ttlock.com/v3/lock/listKeyboardPwd";

    // Token缓存key前缀
    private static final String TOKEN_CACHE_KEY = "TT_DL_TOKEN:";
    // Token有效期（90天，提前1天刷新）
    private static final long TOKEN_EXPIRE_SECONDS = 89 * 24 * 60 * 60;

    @Override
    public AppDlPasswordRes generatePassword(StdDlParams stdDlParams) throws DefinedException {
        try {
            String accessToken = getAccessToken(SystemUtil.CONSOLEHOTELID);
            if (StrUtil.isBlank(accessToken)) {
                return AppDlPasswordRes.failure("获取访问令牌失败");
            }

            Vendorconfig config = getVendorConfig(SystemUtil.CONSOLEHOTELID, VendorType.TT_DL);
            if (config == null) {
                return AppDlPasswordRes.failure("TT门锁配置不存在");
            }

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("clientId", config.getAppid());
            params.add("accessToken", accessToken);
            params.add("lockId", stdDlParams.getLockId());
            params.add("keyboardPwdType", String.valueOf(stdDlParams.getPasswordType()));
            params.add("keyboardPwdName", stdDlParams.getPasswordName());
            params.add("startDate", String.valueOf(stdDlParams.getStartDate()));
            if (stdDlParams.getEndDate() != null) {
                params.add("endDate", String.valueOf(stdDlParams.getEndDate()));
            }
            params.add("date", String.valueOf(System.currentTimeMillis()));

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
            URI uri = UriComponentsBuilder.fromHttpUrl(KEYBOARD_PASSWORD_URL).build().toUri();

            //// 打印cURL命令
            //StringBuilder curlCommand = new StringBuilder("curl -X POST ");
            //curlCommand.append("'").append(uri).append("' ");
            //headers.forEach((key, value) -> {
            //    curlCommand.append("-H '").append(key).append(": ").append(value.get(0)).append("' ");
            //});
            //curlCommand.append("-d '");
            //params.forEach((key, value) -> {
            //    curlCommand.append(key).append("=").append(value.get(0)).append("&");
            //});
            //if (curlCommand.toString().endsWith("&")) {
            //    curlCommand.deleteCharAt(curlCommand.length() - 1);
            //}
            //curlCommand.append("'");
            //log.info("Executing cURL command: {}", curlCommand.toString());

            RestTemplate restTemplate = SpringUtil.getBean(RestTemplate.class);
            ResponseEntity<String> response = restTemplate.postForEntity(uri, request, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                JSONObject result = JSON.parseObject(response.getBody());
                if (result.containsKey("keyboardPwd")) {
                    AppDlPasswordRes res = AppDlPasswordRes.success(
                            result.getString("keyboardPwd"),
                            result.getString("keyboardPwdId")
                    );
                    res.setPasswordType(stdDlParams.getPasswordType());
                    res.setPasswordName(stdDlParams.getPasswordName());
                    res.setStartDate(stdDlParams.getStartDate());
                    res.setEndDate(stdDlParams.getEndDate());
                    return res;
                } else {
                    return AppDlPasswordRes.failure("生成密码失败：" + result.getString("errmsg"));
                }
            } else {
                return AppDlPasswordRes.failure("请求失败：" + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("TT门锁生成密码异常", e);
            return AppDlPasswordRes.failure("生成密码异常：" + e.getMessage());
        }
    }

    @Override
    public String getAccessToken(String projectId) throws DefinedException {
        try {
            // 先从缓存获取token
            RMapCache<String, String> tokenCache = getRedissonClient().getMapCache("TT_DL_TOKENS");
            String cacheKey = TOKEN_CACHE_KEY + projectId;
            String cachedToken = tokenCache.get(cacheKey);

            if (StrUtil.isNotBlank(cachedToken)) {
                return cachedToken;
            }

            // 缓存中没有，重新获取
            Vendorconfig config = getVendorConfig(projectId, VendorType.TT_DL);
            if (config == null) {
                throw new DefinedException("TT门锁配置不存在", ResultCode.SERVER_ERROR.code());
            }

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("clientId", config.getAppid());
            params.add("clientSecret", config.getAppsecrect());
            params.add("username", config.getUserid());
            params.add("password", DigestUtil.md5Hex(config.getUserpwd()));

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
            URI uri = UriComponentsBuilder.fromHttpUrl(AUTH_URL).build().toUri();

            RestTemplate restTemplate = SpringUtil.getBean(RestTemplate.class);
            ResponseEntity<String> response = restTemplate.exchange(uri, HttpMethod.POST, request, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                JSONObject result = JSON.parseObject(response.getBody());
                String accessToken = result.getString("access_token");

                if (StrUtil.isNotBlank(accessToken)) {
                    // 缓存token
                    tokenCache.put(cacheKey, accessToken, TOKEN_EXPIRE_SECONDS, TimeUnit.SECONDS);
                    return accessToken;
                } else {
                    throw new DefinedException("获取访问令牌失败：" + result.getString("errmsg"), ResultCode.SERVER_ERROR.code());
                }
            } else {
                throw new DefinedException("获取访问令牌请求失败：" + response.getStatusCode(), ResultCode.SERVER_ERROR.code());
            }

        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取TT门锁访问令牌异常", e);
            throw new DefinedException("获取访问令牌异常：" + e.getMessage(), ResultCode.SERVER_ERROR.code());
        }
    }

    @Override
    public <T> T queryPasswordList(StdDlParams stdDlParams) throws DefinedException {
        try {
            String accessToken = getAccessToken(SystemUtil.CONSOLEHOTELID);
            if (StrUtil.isBlank(accessToken)) {
                throw new DefinedException("获取访问令牌失败", ResultCode.SERVER_ERROR.code());
            }

            Vendorconfig config = getVendorConfig(SystemUtil.CONSOLEHOTELID, VendorType.TT_DL);
            if (config == null) {
                throw new DefinedException("TT门锁配置不存在", ResultCode.SERVER_ERROR.code());
            }

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("clientId", config.getAppid());
            params.add("accessToken", accessToken);
            params.add("lockId", stdDlParams.getLockId());
            params.add("searchStr", stdDlParams.getSearchStr() != null ? stdDlParams.getSearchStr() : "");
            params.add("pageNo", String.valueOf(stdDlParams.getPageNo()));
            params.add("pageSize", String.valueOf(stdDlParams.getPageSize()));
            params.add("orderBy", String.valueOf(stdDlParams.getOrderBy()));
            params.add("date", String.valueOf(System.currentTimeMillis()));

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
            URI uri = UriComponentsBuilder.fromHttpUrl(KEYBOARD_PASSWORD_LIST_URL).build().toUri();

            RestTemplate restTemplate = SpringUtil.getBean(RestTemplate.class);
            ResponseEntity<String> response = restTemplate.postForEntity(uri, request, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                return (T) response.getBody();
            } else {
                throw new DefinedException("查询密码列表失败：" + response.getStatusCode(), ResultCode.SERVER_ERROR.code());
            }

        } catch (Exception e) {
            log.error("TT门锁查询密码列表异常", e);
            throw new DefinedException("查询密码列表异常：" + e.getMessage(), ResultCode.SERVER_ERROR.code());
        }
    }

    @Override
    protected void updateConfigEvent(Vendorconfig vendorconfig) {
        // 配置更新时清除对应的token缓存
        RMapCache<String, String> tokenCache = getRedissonClient().getMapCache("TT_DL_TOKENS");
        String cacheKey = TOKEN_CACHE_KEY + vendorconfig.getHotelId();
        tokenCache.remove(cacheKey);
        log.info("TT门锁配置更新，清除token缓存：{}", cacheKey);
    }

    @Override
    protected void removeConfigEvent(String projectId) {
        // 配置删除时清除对应的token缓存
        RMapCache<String, String> tokenCache = getRedissonClient().getMapCache("TT_DL_TOKENS");
        String cacheKey = TOKEN_CACHE_KEY + projectId;
        tokenCache.remove(cacheKey);
        log.info("TT门锁配置删除，清除token缓存：{}", cacheKey);
    }
}

package com.cw.core.vendor.dl;

import com.cw.cache.GlobalCache;
import com.cw.cache.impl.VendorConfigCache;
import com.cw.entity.Vendorconfig;
import com.cw.utils.SpringUtil;
import com.cw.utils.enums.GlobalDataType;
import com.google.common.collect.Maps;
import org.redisson.api.RedissonClient;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * 所有门锁厂商的基础访问实例
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2025/1/3 15:45
 **/
public abstract class BaseOutDlVendor implements DlVendorHandler {

    protected Map<String, Vendorconfig> configMap = Maps.newConcurrentMap();

    @Override
    public void initClient() {
        // 延迟初始化，在实际使用时再初始化配置
    }

    protected Vendorconfig getClient(String projectId) {
        if (!configMap.containsKey(projectId)) {
            throw new RuntimeException("没有找到" + projectId + "对应的门锁配置");
        } else {
            return configMap.get(projectId);
        }
    }

    private void initConfig(Vendorconfig vendorconfig) {
        configMap.put(vendorconfig.getHotelId(), vendorconfig);
        initConfigEvent(vendorconfig);
    }

    @Override
    public void refreshClientConfig(Vendorconfig vendorconfig) {
        configMap.put(vendorconfig.getHotelId(), vendorconfig);
        updateConfigEvent(vendorconfig);
        LoggerFactory.getLogger(this.getClass()).info("{} 刷新{}门锁客户端完成", vendorconfig.getHotelId(), vendorconfig.getVtype());
    }

    public void removeClient(String projectId) {
        removeConfigEvent(projectId);
        configMap.remove(projectId);
    }

    /**
     * 配置初始化事件,给子类根据需要重载
     */
    protected void initConfigEvent(Vendorconfig vendorconfig) {
    }

    /**
     * 配置更新事件,给子类根据需要重载
     */
    protected void updateConfigEvent(Vendorconfig vendorconfig) {
    }

    /**
     * 配置删除事件,给子类根据需要重载
     */
    protected void removeConfigEvent(String projectId) {
    }

    protected RedissonClient getRedissonClient() {
        return SpringUtil.getBean(RedissonClient.class);
    }

    /**
     * 获取厂商配置
     */
    protected Vendorconfig getVendorConfig(String projectId, com.cw.utils.enums.VendorType vendorType) {
        VendorConfigCache configCache = GlobalCache.getDataStructure().getCache(GlobalDataType.VENDORCONFIG);
        return configCache.getVendorConfig(projectId, vendorType);
    }
}

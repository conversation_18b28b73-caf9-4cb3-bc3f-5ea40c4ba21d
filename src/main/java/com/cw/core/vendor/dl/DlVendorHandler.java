package com.cw.core.vendor.dl;

import com.cw.entity.Vendorconfig;
import com.cw.exception.DefinedException;
import com.cw.outsys.dl.StdDlParams;
import com.cw.pojo.dto.app.res.AppDlPasswordRes;

/**
 * 门锁厂商处理能力接口
 * 实现类根据实例处理能力去调用相应的方法做实现
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/4/16 09:31
 **/
public interface DlVendorHandler {

    /**
     * 启动时初始化所有处理器
     */
    void initClient();

    /**
     * 刷新vendor 内的处理器
     *
     * @param vendorconfig
     */
    void refreshClientConfig(Vendorconfig vendorconfig);

    /**
     * 生成电子密码
     *
     * @param stdDlParams 标准门锁参数
     * @return 密码生成结果
     * @throws DefinedException
     */
    default AppDlPasswordRes generatePassword(StdDlParams stdDlParams) throws DefinedException {
        return null;
    }

    /**
     * 删除电子密码
     *
     * @param stdDlParams 标准门锁参数
     * @return 删除结果
     * @throws DefinedException
     */
    default boolean deletePassword(StdDlParams stdDlParams) throws DefinedException {
        return false;
    }

    /**
     * 查询密码列表
     *
     * @param stdDlParams 标准门锁参数
     * @return 密码列表
     * @throws DefinedException
     */
    default <T> T queryPasswordList(StdDlParams stdDlParams) throws DefinedException {
        return null;
    }

    /**
     * 获取访问令牌
     *
     * @param projectId 项目ID
     * @return 访问令牌
     * @throws DefinedException
     */
    default String getAccessToken(String projectId) throws DefinedException {
        return null;
    }
}

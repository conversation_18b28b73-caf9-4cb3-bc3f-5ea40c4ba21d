package com.cw.core;

import cn.hutool.core.util.StrUtil;
import com.cw.cache.GlobalCache;
import com.cw.utils.CalculateDate;
import com.cw.utils.ProdType;
import com.cw.utils.RedisKey;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.GlobalDataType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/6/7 01:19
 **/
@Slf4j
@Service
public class CoreCache {


    public void refreshCache(List<String> postHotelIds) {
        List<String> hotelids = Arrays.asList(SystemUtil.CONSOLEHOTELID);

        if (postHotelIds != null && postHotelIds.size() > 0) {
            hotelids.addAll(postHotelIds);
        }

        GlobalDataType[] typs = GlobalDataType.values();
        for (String hotelid : hotelids) {
            for (GlobalDataType typ : typs) {
                GlobalCache.getInstance().refreshAndNotify(typ, hotelid);
            }
        }

    }

    /**
     * 计算占用资源的开始,结束日期
     * 例如 1号入住,3号离店. start传1号,end 传2号
     *
     * @param channelid
     * @param productype
     * @param productCode
     * @param start       占用资源的开始日期
     * @param end         占用资源的结束日期
     * @return
     */
    public static List<String> getGridSkuWriteKeys(String projectId, String channelid, String productype, String productCode, Date start, Date end) {
        List<String> keys = getDateKeys(start, end);
        String signal = getGridCacheKey(projectId, channelid, productype, productCode);
        for (int i = 0; i < keys.size(); i++) {
            keys.set(i, signal + ":" + keys.get(i));
        }
        return keys;
    }

    /**
     * 获取缓存Map 关键字key
     *
     * @param projectId
     * @param channelid
     * @param productype
     * @param productCode
     * @return
     */
    public static String getGridCacheKey(String projectId, String channelid, String productype, String productCode) {
        String key = RedisKey.PmsGridCacheKey + projectId + ":" + channelid + ":" + productype + ":" + productCode;
        return key;
    }


    public static List<String> getDateKeys(Date start, Date end) {
        List<String> keys = new ArrayList<>();
        for (int i = 0; i < CalculateDate.compareDates(end, start) + 1; i++) {
            keys.add(CalculateDate.dateToString(CalculateDate.reckonDay(start, 5, i)));
        }
        return keys;
    }

    public static boolean isCacheProductType(String productType) {
        return true;
    }


    public static List<String> getProdFetchMapKeys(String productCode, String prodType, Date start, Date end) {
        if (isSpecProdType(prodType)) {
            return Arrays.asList(productCode);
        } else {
            return getDateKeys(start, end);
        }

    }

    public static boolean isSpecProdType(String prodType) {
        return false;//当前系统都是日历型产品  所以都是false
    }

}

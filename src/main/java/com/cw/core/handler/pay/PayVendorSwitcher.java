package com.cw.core.handler.pay;

import cn.hutool.core.lang.ClassScanner;
import com.cw.config.exception.CustomException;
import com.cw.core.handler.VendorAdapter;
import com.cw.core.vendor.pay.PayVendorHandler;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.utils.enums.VendorType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;

/**
 * 外部厂商路由器.提交业务给第三方确认处理
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2020/5/14 15:59
 **/
@Slf4j
@Component
public class PayVendorSwitcher {

    public HashMap<VendorType, PayVendorHandler> handlerFacotry = new HashMap();

    @PostConstruct
    protected void scanPackage() {
        //根据 包的路径.将请求类加载到map 缓存
        ClassScanner.scanAllPackageByAnnotation("com.cw.core.vendor.pay", VendorAdapter.class).forEach(clazz -> {
            VendorType vendorType = clazz.getAnnotation(VendorAdapter.class).vendorType();
            try {
                handlerFacotry.put(vendorType, (PayVendorHandler) clazz.newInstance());
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        log.info(" 支付适配厂商大小--> {}", handlerFacotry.size());
    }

    public void initHandlerClient() {
        handlerFacotry.forEach((k, v) -> {
            v.initClient();
        });
    }


    public PayVendorHandler getVendorHandler(VendorType type) {
        if (type.equals(VendorType.LOCAL)) {
            return null;
        }
        if (handlerFacotry.get(type) == null) {
            throw new CustomException(ResultJson.failure(ResultCode.SERVER_ERROR).msg("厂商还未开发适配"));
        }
        return handlerFacotry.get(type);
    }


}

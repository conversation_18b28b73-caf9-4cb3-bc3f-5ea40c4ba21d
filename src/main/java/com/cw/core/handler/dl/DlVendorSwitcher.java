package com.cw.core.handler.dl;

import cn.hutool.core.lang.ClassScanner;
import com.cw.config.exception.CustomException;
import com.cw.core.handler.DoorLockAdapter;
import com.cw.core.handler.VendorAdapter;
import com.cw.core.vendor.dl.DlVendorHandler;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.utils.enums.DoorLockBrand;
import com.cw.utils.enums.VendorType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;

/**
 * 门锁厂商路由器.提交业务给第三方确认处理
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2025/1/3 15:40
 **/
@Slf4j
@Component
public class DlVendorSwitcher {

    public HashMap<VendorType, DlVendorHandler> handlerFactory = new HashMap<>();

    @PostConstruct
    protected void scanPackage() {
        //根据包的路径.将请求类加载到map缓存
        ClassScanner.scanAllPackageByAnnotation("com.cw.core.vendor.dl", VendorAdapter.class).forEach(clazz -> {
            VendorType vendorType = clazz.getAnnotation(VendorAdapter.class).vendorType();
            try {
                handlerFactory.put(vendorType, (DlVendorHandler) clazz.newInstance());
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        log.info("门锁适配厂商大小--> {}", handlerFactory.size());
    }

    public void initHandlerClient() {
        handlerFactory.forEach((k, v) -> {
            v.initClient();
        });
    }

    public DlVendorHandler getVendorHandler(VendorType type) {
        if (type.equals(VendorType.LOCAL)) {
            return null;
        }
        if (handlerFactory.get(type) == null) {
            throw new CustomException(ResultJson.failure(ResultCode.SERVER_ERROR).msg("门锁厂商还未开发适配"));
        }
        return handlerFactory.get(type);
    }
}

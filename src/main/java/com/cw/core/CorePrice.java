package com.cw.core;

import cn.hutool.core.util.StrUtil;
import com.cw.arithmetic.func.Var;
import com.cw.arithmetic.others.CodeDetail;
import com.cw.arithmetic.rate.PriceTool;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.HotelCache;
import com.cw.cache.impl.RoomRateCache;
import com.cw.controller.pms.HotelInfoController;
import com.cw.entity.*;
import com.cw.exception.DefinedException;
import com.cw.mapper.RoomRateDetailMapper;
import com.cw.pojo.sqlresult.Produce_calcpricePo;
import com.cw.pojo.sqlresult.Produce_ratesqueryPo;
import com.cw.utils.CalculateDate;
import com.cw.utils.CalculateNumber;
import com.cw.utils.ProdType;
import com.cw.utils.SpringUtil;
import com.cw.utils.enums.GlobalDataType;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Multimap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.swing.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 房价相关统一计算方法
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/6/4 10:17
 **/
@Service
public class CorePrice {

    @Autowired
    RoomRateDetailMapper rateDetailMapper;

/*    public String getCalcWeekEnd(String rateCode, String hotelId) {
        HotelCache hotelCache = GlobalCache.getDataStructure().getCache(GlobalDataType.HOTEL);
        Hotel hotel = hotelCache.getRecord(hotelId, hotelId);
        if (hotel != null) {
            return hotel.getWeekendDefinition();
        }
        return "6,7";
    }*/

    /**
     * 返回指定房型的房价计算信息
     *
     * @param hotelId     酒店ID
     * @param ratecode    房价代码
     * @param type        产品代码.空的时候默认为计算房价
     * @param productcode 产品代码.通常传房价
     * @param startDate   入住日期
     * @param endDate    离店日期
     * @param person      成人数
     * @param child       小孩数
     * @param extrabad    加床数
     * @param pkgs        报价代码
     * @param kitcode     套餐代码
     * @param policycode  活动代码
     * @param needCheck   房价不全时.是否抛出异常
     * @param lfixRate    是否为固定价格,固定价格时不会查询数据库.直接返回输入价格
     * @param inputPrice  输入价格
     * @param rsNo        如果传预订号.返回的就是该预订已经存储的房价.不会返回最新房价.
     * @return
     * @throws DefinedException
     */
    public List<Produce_calcpricePo> calcPrice(String hotelId, String ratecode, String type, String productcode,
                                               Date startDate, Date endDate, int person, int child, int extrabad,
                                               String pkgs, String kitcode, String policycode,
                                               boolean needCheck, boolean lfixRate, BigDecimal inputPrice, String rsNo) throws DefinedException {
        List<Produce_calcpricePo> list = Lists.newArrayList();
        String hotelWeekend = PriceTool.getHotelWeekend(ratecode, hotelId);

        Multimap<Date, Produce_calcpricePo> map = ArrayListMultimap.create();
        int length = CalculateDate.compareDates(endDate, startDate).intValue();
        if (length == 0) {
            length++;
        }
        List<RoomRateDetail> roomRateDetails = rateDetailMapper.getCalcRoomRateDetailList(ratecode, productcode, hotelId, startDate, endDate);
        for (int i = 0; i < length; i++) {  //计算未来价格
            Date d = CalculateDate.reckonDay(startDate, 5, i);
            Produce_calcpricePo po = new Produce_calcpricePo();
            po.setDate(d);
            po.setPrice(PriceTool.seekUseRoomRate(roomRateDetails, d, person, hotelWeekend));
            list.add(po);
            map.put(d, po);
        }
        return list;
    }

    /**
     * 计算+返回需要保存的每日价格明细
     * <p>
     * 订单生成时.保存当时的每日房价.
     * 避免后台修改房价.影响前期预订价格.造成价格波动
     *
     * @param rs
     * @param lcalcOnly
     * @return
     */
    public List<RoomsDaily> writeRoomPrice(Reservation rs, boolean lcalcOnly) throws DefinedException {
        CoreDaily coreDaily = SpringUtil.getBean(CoreDaily.class);
        Date now = CalculateDate.getSystemDate();
        Date calcDate = CalculateDate.maxDate(rs.getArrivalDate(), now);
        boolean ldayuse = CalculateDate.isEqual(rs.getArrivalDate(), rs.getDepartureDate());
        List<RoomsDaily> dailyList = null;
        if (rs.getReservationNumber().isEmpty()) {  //如果是新建或者是仅仅要求计算
            dailyList = coreDaily.produceDaily(rs);
        } else {
            if (!lcalcOnly) {
                dailyList = coreDaily.getDailys(rs.getReservationNumber(), rs.getHotelId());
            } else {
                dailyList = coreDaily.produceDaily(rs);
            }
            if (dailyList == null) {
                dailyList = new ArrayList<RoomsDaily>();
            }
            dailyList = coreDaily.makeUpDaily(dailyList, rs, rs.getArrivalDate());
        }
        if ((ldayuse && CalculateDate.isAfter(now, rs.getDepartureDate()))
                || (!ldayuse && CalculateDate.afterEqual(now, rs.getDepartureDate()))) {
            return dailyList;
        }
        HashMap<String, RoomsDaily> dailyMap = new HashMap<>();
        for (RoomsDaily daily : dailyList) {
            dailyMap.put(CalculateDate.dateToString(daily.getDatum()), daily);
        }
        List<Produce_calcpricePo> prices = calcPrice(rs.getHotelId(), rs.getRateCode(),
                ProdType.ROOM.val(),
                rs.getRoomType(), rs.getArrivalDate(), rs.getDepartureDate(), rs.getPersonTotal(),
                0, 0, "", StrUtil.EMPTY,
                StrUtil.EMPTY, true, false, BigDecimal.ZERO, rs.getReservationNumber());
        boolean upd = false;
        if (!prices.isEmpty()) {//考虑FIX的情况.如果是FIX的预订下来.就不需要执行运算了
            BigDecimal total = BigDecimal.ZERO;
            RoomsDaily daily = null;
            for (Produce_calcpricePo po : prices) {
                Date d = po.getDate();  //CalculateDate.stringToDate(row[0].toString());
                BigDecimal calcprice = po.getPrice();//= new BigDecimal(row[1].toString());

                if (CalculateDate.isBefore(d, now)) {  //历史记录不做修改.只是计算总价
                    daily = dailyMap.get(CalculateDate.dateToString(d));
                    if (daily != null) {
                        total = total.add(daily.getPrice());
                    }
                    continue;
                }
                BigDecimal price = rs.getFixrate() ? rs.getPrice() : calcprice;//如果是fixrate了  直接使用预订时输入的价格
                total = total.add(price);
                if (CalculateDate.isEqual(calcDate, d)) {
                    rs.setPrice(price);
                    upd = true;
                }
                daily = dailyMap.get(CalculateDate.dateToString(d));
                if (daily != null) {
                    daily.setPrice(price);
                }
            }
            rs.setTotalPrice(total.multiply(BigDecimal.valueOf(rs.getRooms())));
        }

        //计算内含包价+可用包价服务


        if (!upd) {
            String errMsg = MessageFormat.format("计算客房价格失败.请求参数:价格码{0},房型{1},{2}-{3}",
                    rs.getRateCode(), rs.getRoomType(),
                    CalculateDate.dateToString(rs.getArrivalDate()),
                    CalculateDate.dateToString(rs.getDepartureDate()));
            throw new DefinedException(errMsg);
        }

        return dailyList;

    }

    public List<CodeDetail> queryAvlPriceGrid(String calcRates, String calcProducts, String hotelId, Date startDate, Date endDate) {
        List<CodeDetail> details = new ArrayList<>();
        HashMap<String, CodeDetail> detMap = new HashMap<>();
        String[] rateCodeArray = calcRates.split(",");
        for (String ratecode : rateCodeArray) {
            CodeDetail detail = new CodeDetail();
            detail.setRcodeName(ratecode);
            detail.setRcodeDesc(ratecode);
            detMap.put(ratecode, detail);
            details.add(detail);
        }


        for (CodeDetail detail : details) {
            //填充startDate 当日房价
            List<Produce_ratesqueryPo> prices = priceQuery(detail.getRcodeName(), calcProducts, hotelId, startDate);
            for (Produce_ratesqueryPo po : prices) {
                detail = detMap.get(po.getRatecode());
                if (detail != null) {
                    detail.putProductPrice(po.getProduct(), po.getPrice());
                }
            }
        }

        //details = details.stream().filter(d->!d.getProductPrice().isEmpty()).collect(Collectors.toList());//过滤掉没有价格的RP

        return details;
    }


    private List<Produce_ratesqueryPo> priceQuery(String rateCode, String calcProducts, String hotelId, Date calcDate) {
        List<Produce_ratesqueryPo> list = Lists.newArrayList();
        List<Produce_ratesqueryPo> result = rateDetailMapper.rateQuery(rateCode, hotelId, calcDate);
        String hotelWeekend = PriceTool.getHotelWeekend(rateCode, hotelId);
        for (Produce_ratesqueryPo produceRatesqueryPo : result) {
            produceRatesqueryPo.updPrice(calcDate, hotelWeekend);   //根据计算的当天是周末.还是非周末.返回展示价格
            list.add(produceRatesqueryPo);
        }

        return list;
    }


}

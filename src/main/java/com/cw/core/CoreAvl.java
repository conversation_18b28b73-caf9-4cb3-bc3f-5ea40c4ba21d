package com.cw.core;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.cw.arithmetic.sku.OpskuPickup;
import com.cw.arithmetic.sku.TMultiRsdata;
import com.cw.arithmetic.sku.TSkuUpd;
import com.cw.cache.GlobalCache;
import com.cw.cache.RedisTool;
import com.cw.cache.impl.AccountItemCache;
import com.cw.cache.impl.RoomTypeCache;
import com.cw.core.func.order.StdOrderData;
import com.cw.entity.DailyStat_Res;
import com.cw.entity.Reservation;
import com.cw.entity.RoomRateDetail;
import com.cw.entity.RoomType;
import com.cw.exception.DefinedException;
import com.cw.mapper.DailyStatResMapper;
import com.cw.mapper.RoomQuantityMapper;
import com.cw.mapper.RoomRateDetailMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.dto.pms.res.statistic.StatRevenue;
import com.cw.pojo.dto.pms.res.statistic.StatRevenues;
import com.cw.pojo.sqlresult.ProductMaxDatePo;
import com.cw.pojo.upload.constants.UploadType;
import com.cw.utils.*;
import com.cw.utils.enums.GlobalDataType;
import com.cw.utils.enums.IncomeTypeEnum;
import com.cw.utils.tool.DefValData;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 房量修复统计方法.提供给夜审或其他地方调用
 * 库存校验,写入扣减逻辑服务
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/10/20 10:10
 **/
@Service
public class CoreAvl {
    private static final Logger log = LoggerFactory.getLogger(CoreAvl.class);
    @Autowired
    private DaoLocal<?> daoLocal;


    @Autowired
    RoomQuantityMapper roomQuantityMapper;

    @Resource
    private DailyStatResMapper dailyStatResMapper;


    @Autowired
    RedisTemplate redisTemplate;

    @Autowired
    RoomRateDetailMapper rateDetailMapper;


    /**
     * 根据当前订单的预订情况.批量更新可卖房房表
     *
     * @param hotelId
     * @param startDate
     * @param endDate
     */
    public void fixAllHotelRoom(String hotelId, List<String> roomTypes, Date startDate, Date endDate) throws Exception {
        String strStartD = CalculateDate.dateToString(startDate);
        String strStartE = CalculateDate.dateToString(endDate);

        Stopwatch stopwatch = Stopwatch.createStarted();
        int maxDays = CalculateDate.compareDates(endDate, CalculateDate.getSystemDate()).intValue();

        //先按房间数修复roomtype 总数
        String fixRmtToal = "update room_type as r set r.room_number=(SELECT count(*) from room as o where o.room_type=r.room_type and o.hotelid=r.hotelid) where r.hotelid='" + hotelId + "'";
        daoLocal.batchNativeOption(fixRmtToal);

        String rroomsSql = "select room_type from rrooms where hotelid='" + hotelId + "' group by room_type";
        List<String> rroomLis = daoLocal.getNativeObjectList(rroomsSql);

        List<RoomType> cacheRmtLis = GlobalCache.getDataStructure().getCache(GlobalDataType.ROOMTYPE).getDataList(hotelId);
        for (RoomType cacheRmt : cacheRmtLis) {
            if (!rroomLis.contains(cacheRmt.getRoomType())) {
                generateRrooms(hotelId, Arrays.asList(cacheRmt.getRoomType()), null, null);
                log.info("发现新房型{}数据,现在生成资源表", cacheRmt.getRoomType());
            }
        }


        //String updSql = "update Rrooms set pickup=0,total=0,ooo=0 where hotelid='" + hotelId + "' and datum between '" + strStartD + "' and '" + strStartE + "'";
        //daoLocal.batchOption(updSql);

        String fixSql = "update rrooms,room_type set rrooms.total=room_type.room_number,pickup=0,ooo=0 WHERE rrooms.hotelid=room_type.hotelid  and rrooms.room_type=room_type.room_type and rrooms.hotelid='" + hotelId + "'";
        daoLocal.batchNativeOption(fixSql);


        //统计非日用房的每天库存占用数 -预抵与入住的订单才算当晚占用
        String sumSql = "SELECT dr.d as 'datum', r.room_type as rmtype ,SUM(r.rooms) AS total_booking \n" +
                "FROM  (select date_add('" + strStartD + "',interval number day) as d from sptvalue where number<=" + maxDays + "   ) as dr\n" +
                "LEFT JOIN   reservation r ON \n" +
                "  dr.d BETWEEN r.arrival_date AND   date_add(r.departure_date,interval -1 day)   \n" +
                "where r.hotelid='" + hotelId + "' and r.arrival_date<>r.departure_date and r.reservation_status in (0,1,3)  \n" +
                "GROUP BY  dr.d,r.room_type  ORDER BY  dr.d;";

        //System.out.println("统计修复SQL:" + sumSql);


        List<Object[]> rows = daoLocal.getNativeObjectList(sumSql);//将未来每一天的可卖情况统计出来
        if (rows.size() == 0) {
            return;
        }

        Connection connection = null;
        PreparedStatement pst = null;

        try {
            connection = daoLocal.getBatchConnection();
            connection.setAutoCommit(false);
            pst = connection.prepareStatement("update Rrooms set pickup=? where room_type=? and datum=? and hotelid='" + hotelId + "'");
            for (Object[] row : rows) {
                String datum = row[0].toString();
                String rmtype = row[1].toString();
                int total = Integer.parseInt(row[2].toString());
                pst.setInt(1, total);
                pst.setString(2, rmtype);
                pst.setString(3, datum);
                pst.addBatch();
            }
            int[] ss = pst.executeBatch();
            connection.commit();
            log.info("批量更新房型资源数: {},耗时:{}", ss.length, stopwatch.stop());
        } catch (Exception e) {
            e.printStackTrace();
        }

        //TODO  把OO 维修房给算出来.

        fixOOO(strStartD, maxDays, hotelId);
    }

    private void fixOOO(String strStartD, Integer maxDays, String hotelId) {
        //统计非日用房的每天库存占用数 -预抵与入住的订单才算当晚占用
        String sumSql = "SELECT dr.d as 'datum', r.room_type as rmtype ,count(id) AS total \n" +
                "FROM  (select date_add('" + strStartD + "',interval number day) as d from sptvalue where number<=" + maxDays + "   ) as dr\n" +
                "LEFT JOIN   room r ON \n" +
                "  dr.d BETWEEN r.start_time AND   r.end_time\n" +
                "where r.hotelid='" + hotelId + "' \n" +
                "GROUP BY  dr.d,r.room_type  ORDER BY  dr.d;";

        //System.out.println("统计维修房修复SQL:" + sumSql);


        List<Object[]> rows = daoLocal.getNativeObjectList(sumSql);//将未来每一天的可卖情况统计出来
        if (rows.size() == 0) {
            return;
        }

        Connection connection = null;
        PreparedStatement pst = null;

        try {
            connection = daoLocal.getBatchConnection();
            connection.setAutoCommit(false);
            pst = connection.prepareStatement("update Rrooms set ooo=? where room_type=? and datum=? and hotelid='" + hotelId + "'");
            for (Object[] row : rows) {
                String datum = row[0].toString();
                String rmtype = row[1].toString();
                int total = Integer.parseInt(row[2].toString());
                pst.setInt(1, total);
                pst.setString(2, rmtype);
                pst.setString(3, datum);
                pst.addBatch();
            }
            int[] ss = pst.executeBatch();
            connection.commit();
            log.info("批量更新房型OOO资源数: {}", ss.length);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 将列表中的房型.批量生成可卖房资源
     *
     * @param hotelId
     * @param roomTypes
     * @param startDate
     * @param endDate
     * @throws DefinedException
     */
    public void generateRrooms(String hotelId, List<String> roomTypes, Date startDate, Date endDate) throws Exception {
        if (startDate == null) {
            startDate = CalculateDate.getSystemDate();
        }
        if (endDate == null) {
            Date maxdate = startDate;
            List ls = daoLocal.getObjectList("select max(datum) from Rrooms where hotelId='" + hotelId + "'");
            if (ls != null && !ls.isEmpty() && ls.get(0) != null) {
                maxdate = CalculateDate.reckonDay((Date) ls.get(0), 5, 1);// 将数据里的最大日期往后加1
            } else {
                maxdate = CalculateDate.reckonDay(startDate, 5, 180);
            }
            endDate = maxdate;
        }
        String sdate = CalculateDate.dateToString(startDate);
        String edate = CalculateDate.dateToString(endDate);
        String deleteSql = "delete from Rrooms where room_type=? and (datum between '" + sdate + "' and '" + edate + "') and hotelid='" + hotelId + "'";
        String insertSql = "insert into Rrooms(room_type,datum,total,pickup,ooo,hotelid) select ?,date_add('"
                + sdate + "',interval number day),?,0,0,'" + hotelId + "' from sptvalue where date_add('" + sdate + "',interval number day)<='" + edate + "'   ";
        PreparedStatement pst = null;
        PreparedStatement pst2 = null;
        Connection connection = null;
        try {
            connection = daoLocal.getBatchConnection();
            connection.setAutoCommit(false);
            pst = connection.prepareStatement(deleteSql);
            for (String rmtype : roomTypes) {
                pst.setString(1, rmtype);
                pst.addBatch();
            }
            pst.executeBatch();
            connection.commit();
            //删除结束.新插入开始

            pst2 = connection.prepareStatement(insertSql);
            RoomTypeCache roomTypeCache = GlobalCache.getDataStructure().getCache(GlobalDataType.ROOMTYPE);
            for (String rmtype : roomTypes) {
                RoomType rt = roomTypeCache.getRecord(hotelId, rmtype);
                if (rt != null) {
                    pst2.setString(1, rmtype);
                    pst2.setInt(2, rt.getRoomNumber());
                    pst2.addBatch();
                }
            }
            int[] ss = pst2.executeBatch();

            log.info("批量插入房型资源数:{} ", ss);

            connection.commit();
        } finally {
            if (pst != null) {
                pst.close();
            }
            if (pst2 != null) {
                pst2.close();
            }
            if (connection != null) {
                connection.close();
            }
        }

    }







    private boolean isActBlock(String block) {
        return StringUtils.isNotBlank(block);
    }


    /**
     * @param hotelId
     * @return 获取房量表最大日期
     */
    public Date getRoomQuantityMaxDate(String hotelId) {
        Date maxDate = roomQuantityMapper.findRroomsMaxDate(hotelId);
        if (maxDate == null) {
            maxDate = CalculateDate.reckonDay(DateUtil.beginOfDay(new Date()), 5, 90);
        }
        return maxDate;
    }


    /**
     * 批量产品库存自动延长最大日期库存+1天
     *
     * @param hotelId 项目id
     * @param products  产品库存日期集合
     * @param endDate   最大日期库存+1天
     * @throws DefinedException
     * @throws Exception
     */
    public void generateProductResource(String hotelId, List<ProductMaxDatePo> products, Date endDate) throws DefinedException, Exception {

        String deleteSql = "";
        String insertSql = "";
        DaoLocal<?> daoLocal = SpringUtil.getBean(DaoLocal.class);
        String endDateStr = CalculateDate.dateToString(endDate);
        DataSource source = daoLocal.getDataSource();
        PreparedStatement pst = null;
        PreparedStatement pst2 = null;
        Connection connection = null;
        Date now = new Date();
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);
            //避免重复夜审消息添加
            deleteSql = "delete from Rrooms where  room_type=? and datum between ? and '" + endDateStr + "' and hotelid=?";
            //按照日期区间插入房量数据
            //todo 维修房数量是否查询
            insertSql = "insert into Rrooms(room_type,datum,ooo,pickup,total,hotelid) select ?,date_add(?,interval number day),0,0,?,? from sptvalue" +
                    " where  date_add(?,interval number day)<='" + endDateStr + "'";
            pst2 = connection.prepareStatement(insertSql);
            RoomTypeCache roomTypeCache = GlobalCache.getDataStructure().getCache(GlobalDataType.ROOMTYPE);
            List<RoomType> list = roomTypeCache.getDataList(hotelId);
            for (ProductMaxDatePo rmType : products) {
                RoomType rt = roomTypeCache.getRecord(hotelId, rmType.getCode());
                if (rt != null) {
                    Date startDate = CalculateDate.reckonDay(rmType.getDatum(), 5, 1);
                    pst2.setString(1, rmType.getCode());
                    pst2.setDate(2, DateUtil.date(startDate).toSqlDate());
                    pst2.setInt(3, rt.getRoomNumber());
                    pst2.setString(4, hotelId);
                    pst2.setDate(5, DateUtil.date(startDate).toSqlDate());
                    pst2.addBatch();
                }
            }
            pst = connection.prepareStatement(deleteSql);
            for (ProductMaxDatePo product : products) {
                //数据库内日期+1
                Date startDate = CalculateDate.reckonDay(product.getDatum(), 5, 1);
                pst.setString(1, product.getCode());
                pst.setDate(2, DateUtil.date(startDate).toSqlDate());
                pst.setString(3, hotelId);
                pst.addBatch();
            }
            pst.executeBatch();
            connection.commit();
            //删除结束.新插入开始
            pst2.executeBatch();
            connection.commit();
        } finally {
            if (pst != null) {
                pst.close();
            }
            if (pst2 != null) {
                pst2.close();
            }
            if (connection != null) {
                connection.close();
            }
        }
    }

    public List<TSkuUpd> getOrderSkuUpd(StdOrderData orgData, StdOrderData postData) {
        TMultiRsdata oldRsdata;
        TMultiRsdata newRsdata;
        if (orgData == null) {
            oldRsdata = new TMultiRsdata(true, StrUtil.EMPTY);
        } else {
            oldRsdata = new TMultiRsdata(false, orgData.getReSource()); //资源来源
            oldRsdata.fillArray(ProdType.ROOM.val(), Reservation.class, orgData.getRooms());
        }
        if (postData == null) {
            newRsdata = new TMultiRsdata(true, StrUtil.EMPTY);
        } else {
            newRsdata = new TMultiRsdata(false, postData.getReSource());  //资源来源
            newRsdata.fillArray(ProdType.ROOM.val(), Reservation.class, postData.getRooms());
        }
        return OpskuPickup.calcSkuPickup(oldRsdata, newRsdata);
    }

    public boolean checkCacheSku(String hotelId, List<TSkuUpd> skuUpds) throws DefinedException {
        if (skuUpds == null || skuUpds.isEmpty()) {
            return true;
        }


        for (TSkuUpd skuUpd : skuUpds) {
            if (!CoreCache.isCacheProductType(skuUpd.getProdType())) { // 目前只检查房的库存redis缓存
                continue;
            }
            String skuMapkey = OpskuPickup.getSkuMap(hotelId, skuUpd);
            List<String> skukeys = CoreCache.getProdFetchMapKeys(skuUpd.getSkuid(), skuUpd.getProdType(), skuUpd.getStartdate(), skuUpd.getEnddate());
            //.getDateKeys(skuUpd.getStartdate(), skuUpd.getEnddate());
            //判断缓存类型取KEY

            List redisResult = RedisTool.getInstance().opsForHash().multiGet(skuMapkey, skukeys);


            for (int i = 0; i < redisResult.size(); i++) {
                Integer cachenum = redisResult.get(i) == null ? 0 : Integer.parseInt(redisResult.get(i).toString());
                int postnum = skuUpd.getChangeNum();
                if (cachenum - postnum < 0) {
                    String productDesc = ContentCacheTool.getProductDesc(skuUpd.getProdType(), skuUpd.getSkuid(), hotelId);

                    throw new DefinedException(StrUtil.format("{}在{}库存不足", productDesc, skukeys.get(i)),
                            ResultCode.FORMERR.code());
                }
            }
        }
        return true;
    }

    public void updateSkuCache(String projectId, List<TSkuUpd> skuUpds) throws DefinedException {
        for (TSkuUpd skuUpd : skuUpds) {
            if (!CoreCache.isCacheProductType(skuUpd.getProdType())) { // 目前只检查房的库存redis缓存
                continue;
            }
            String skuMapkey = OpskuPickup.getSkuMap(projectId, skuUpd);
            boolean lspecSku = CoreCache.isSpecProdType(skuUpd.getProdType());
            List<String> gridKeys = CoreCache.getProdFetchMapKeys(skuUpd.getSkuid(), skuUpd.getProdType(), skuUpd.getStartdate(), skuUpd.getEnddate());
            List redisResult = RedisTool.getInstance().opsForHash().multiGet(skuMapkey, gridKeys);
            for (int i = 0; i < redisResult.size(); i++) {
                Integer cachenum = redisResult.get(i) == null ? 0 : Integer.parseInt(redisResult.get(i).toString());
                int postnum = skuUpd.getChangeNum();
                if (cachenum - postnum < 0) {
                    if (!lspecSku) {
                        throw new DefinedException(MessageFormat.format("{0}在{1}库存不足", skuUpd.getSkuid(), gridKeys.get(i)), ResultCode.OVERBOOK_ERR.code());
                    } else {
                        throw new DefinedException("库存不足", ResultCode.OVERBOOK_ERR.code());
                    }
                }
                RedisTool.getInstance().opsForHash().put(skuMapkey, gridKeys.get(i), (cachenum - postnum) + ""); //更新redis 缓存
            }
        }
    }

    public HashMap<String, Object> checkBatchCreateAvailability(List<Reservation> rlis, String block, String hotelId) {
        HashMap<Date, HashMap<String, Integer>> katChnageMap = new HashMap<Date, HashMap<String, Integer>>(); //存放每种房型要多订多少或者是少订多少
        HashSet<String> seekRoomTypes = new HashSet<String>();
        HashSet<Date> seekDates = new HashSet<Date>();
        Date hoteldate = new Date();
//        String msg = "";

        boolean lfreesell = block.isEmpty();//isFreeSell(channel);  目前暂时没有BLOCK

        HashMap<Reservation, String> rsMsgMap = new HashMap<Reservation, String>();
        for (Reservation g : rlis) {
            if (!CalculateDate.isEqual(g.getArrivalDate(), g.getDepartureDate())) {
                Date startDate = CalculateDate.maxDate(hoteldate, g.getArrivalDate());
                int len = CalculateDate.compareDates(g.getDepartureDate(), startDate).intValue();
                rsMsgMap.put(g, "");
                for (int i = 0; i < len; i++) {
                    Date d = CalculateDate.reckonDay(startDate, 5, i);
                    sumRoomTypePickup(g.getRoomType(), g.getRooms(), d, katChnageMap,
                            seekRoomTypes, seekDates);
                }
            }
        }
        if (katChnageMap.size() > 0 && !seekDates.isEmpty() && !seekRoomTypes.isEmpty()) {
            HashMap<String, HashMap<String, Integer>> katLeftMap = Maps.newHashMap();
            //组装数据。
            List<String> rms = Arrays.asList(seekRoomTypes.toArray(new String[]{}));
            List<Date> checkdates = Arrays.asList(seekDates.toArray(new Date[]{}));
            List<String> strCheckdates = checkdates.stream().map(CalculateDate::dateToString).collect(Collectors.toList());
            String roomLeftStr = lfreesell ? "total-ooo-pickup" : "avl";
            String rmTypeFilter = CalculateString.transFilter(rms, "'");


            List<Date[]> dateBetween = CalculateDate.getDateStartEnd2(checkdates);
            if (!rmTypeFilter.isEmpty() && !dateBetween.isEmpty()) {
                String datefilter = "";
                List<Date> params = new ArrayList<Date>();

                for (Date[] dates : dateBetween) {
                    String dbegin = "'" + CalculateDate.dateToString(dates[0]) + "'";
                    String dend = "'" + CalculateDate.dateToString(dates[1]) + "'";
                    datefilter += datefilter.isEmpty() ? "(datum between " + dbegin + " and " + dend + ")" : " or (datum between " + dbegin + " and " + dend + ")";
                    params.add(dates[0]);
                    params.add(dates[1]);
                }
                String resourceJpql = "select g.datum,g.rmtype,sum(" + roomLeftStr + ") from Rrooms as g where  ("
                        + datefilter + ") and g.rmtype in (" + rmTypeFilter + ") and channelid='" + block + "' and g.projectid='" + hotelId + "'  group by g.rmtype,g.datum order by datum";

                if (lfreesell) {//目前没有block
                    resourceJpql = "select g.datum,g.room_type,sum(" + roomLeftStr + ") from Rrooms as  g where  ("
                            + datefilter + ") and g.room_type in (" + rmTypeFilter + ") and g.hotelid='" + hotelId + "'  group by g.room_type,g.datum order by datum";
                }
                List<Object[]> data = SpringUtil.getBean(DaoLocal.class).getNativeObjectList(resourceJpql);


                for (int a = 0; a < data.size(); a++) {
                    Object[] os = data.get(a);
                    Date date = CalculateDate.stringToDate(os[0].toString());
                    String kat = os[1].toString();
                    Integer left = Integer.parseInt(os[2].toString());
                    sumRoomTypeLeft(kat, left, date, katLeftMap);
                }

                for (String date : strCheckdates) {
                    if (!katChnageMap.containsKey(date) || !rsMsgMap.containsValue("")) {//如果这天不需要检查或者都校验完了
                        continue;
                    }
                    if (katLeftMap.containsKey(date)) {
                        HashMap<String, Integer> changeMap = katChnageMap.get(date);
                        HashMap<String, Integer> leftMap = katLeftMap.get(date);
                        for (Map.Entry<String, Integer> entry : changeMap.entrySet()) {
                            if (leftMap.containsKey(entry.getKey())) {
                                int left = leftMap.get(entry.getKey());
                                int change = entry.getValue();//
                                String rmtdesc = ContentCacheTool.getProductDesc(ProdType.ROOM.val(), entry.getKey(), hotelId);
                                if (left - change < 0) {// 如果做进来会房型超预定的话
                                    //这天房间不够了
                                    String errmsg = MessageFormat.format("{2}在{0}仅剩余{1}间!", date, left, rmtdesc);
                                    if (left <= 0) {
                                        errmsg = MessageFormat.format("{1}在{0}已售罄!", date, rmtdesc);
                                    }
                                    putErrMsg(date, entry.getKey(), errmsg, rsMsgMap);
                                }  
                            } else {
                                String errmsg = MessageFormat.format("{1}在{0}未设置预留", date, entry.getKey());
                                putErrMsg(date, entry.getKey(), errmsg, rsMsgMap);
                            }
                        }
                    } else {
                        //这天房间不够了
                        HashMap<String, Integer> changeMap = katChnageMap.get(date);
                        for (String rmtype : changeMap.keySet()) {
                            String errmsg = MessageFormat.format("房型{1}在{0} 未设置留房", date, rmtype);
                            putErrMsg(date, rmtype, errmsg, rsMsgMap);
                        }
                    }
                }
            }
        }

        HashMap<String, Object> result = new HashMap<String, Object>();

        for (Map.Entry<Reservation, String> entry : rsMsgMap.entrySet()) {
            if (!entry.getValue().isEmpty()) {
                result.put(entry.getKey().getReservationNumber(), entry.getValue());
            }
        }
        return result;
    }

    private void sumRoomTypeLeft(String kat, Integer left, Date date,
                                 HashMap<String, HashMap<String, Integer>> roomLeftMap) {
        //date = CalculateDate.getDateKey(date);
        String dateKey = CalculateDate.dateToString(date);
        if (!roomLeftMap.containsKey(dateKey))
            roomLeftMap.put(dateKey, new HashMap<String, Integer>());
        roomLeftMap.get(dateKey).put(kat, left);
    }

    private void sumRoomTypePickup(String kat, Integer change, Date date,
                                   HashMap<Date, HashMap<String, Integer>> katChnageMap,
                                   HashSet<String> seekRoomTypes, HashSet<Date> seekDates) {
        date = CalculateDate.getDateKey(date);
        if (change > 0) {
            seekRoomTypes.add(kat);
            seekDates.add(date);
            if (!katChnageMap.containsKey(date))
                katChnageMap.put(date, new HashMap<String, Integer>());

            if (!katChnageMap.get(date).containsKey(kat))
                katChnageMap.get(date).put(kat, change);
            else
                katChnageMap.get(date).put(kat, katChnageMap.get(date).get(kat) + change);
        }
    }

    /**
     * 存放错误信息
     *
     * @param d
     * @param msg
     * @param rsMsgMap
     */
    private void putErrMsg(String d, String rmtype, String msg, HashMap<Reservation, String> rsMsgMap) {
        Date date = CalculateDate.stringToDate(d);
        for (Map.Entry<Reservation, String> entry : rsMsgMap.entrySet()) {
            Reservation r = entry.getKey();
            if (entry.getValue().isEmpty()
                    && CalculateDate.isInRange(date, r.getArrivalDate(), CalculateDate.reckonDay(r.getDepartureDate(), 5, -1))
                    && r.getRoomType().equals(rmtype)) {
                rsMsgMap.put(r, msg);//
            }
        }
    }


    public void updateResourceAvailability(List<TSkuUpd> updList, String hotelId, String prodType) {
        for (TSkuUpd tSkuUpd : updList) {
            //log.info("更新可卖房表: {}:{} ,{},{},{}", tSkuUpd.getChangeNum() > 0 ? "减少" : "增加", -tSkuUpd.getChangeNum(), tSkuUpd.getSkuid(),
            //        CalculateDate.dateToString(tSkuUpd.getStartdate()), CalculateDate.dateToString(tSkuUpd.getEnddate()));
//            List<RoomRateDetail> roomRateDetails = rateDetailMapper.getCalcRoomRateDetailList(tSkuUpd.getSkuid(), tSkuUpd.getSkuid(), hotelId, tSkuUpd.getStartdate(), tSkuUpd.getEnddate());
            roomQuantityMapper.updateRroomsPickup(tSkuUpd.getChangeNum(), hotelId, tSkuUpd.getSkuid(), CalculateDate.returnDate_ZeroTime(tSkuUpd.getStartdate()), CalculateDate.returnDate_ZeroTime(tSkuUpd.getEnddate()));
        }
    }


    /**
     * 获取可卖资源（目前只用来处理客房资源）
     *
     * @param products    产品代码列表
     * @param productType 产品类型 SystemUtil.ProductType.ROOM
     * @param block       锁房代码 为空查询景区资源
     * @param arr         到店日期
     * @param dept        离店日期
     * @param requireNum  大于这个数的资源才会被返回
     * @param rangeType   为1的话会把离店日期-1
     * @return
     */
    public HashMap<String, Integer> getResourceLeft_DB(List<String> products, String productType, String block,
                                                       Date arr, Date dept, Integer requireNum, int rangeType, String hotelId) {
        List<Map<String, Object>> dbresult = new ArrayList<>();
        arr = CalculateDate.returnDate_ZeroTime(arr);
        dept = CalculateDate.returnDate_ZeroTime(dept);
        Date endD = rangeType == 1 && productType.equals(ProdType.ROOM.val()) ?
                (CalculateDate.isEqual(arr, dept) ? arr : CalculateDate.reckonDay(dept, 5, -1)) : dept;

        HashMap<String, Integer> result = new HashMap<>();
        boolean infinite = productType.equals(ProdType.CANYIN.val()) || productType.equals(ProdType.TICKET.val());
        for (String product : products) {
            result.put(product, infinite ? -1 : 0); //默认为0.就是不可卖 餐饮门票默认无限
        }

        //后面有了block 再添加
        String prodcol = "sku";  //产品代码列名
        String avlcol = "avl";//可用数量列名

        if (productType.equals(ProdType.ROOM.val())) {
            if (block.isEmpty()) {
                dbresult = roomQuantityMapper.getResourceLeft(products, arr, endD, requireNum, hotelId);
            } else {
                //dbresult = rrooms_pmsMapper.getResourceLeft(products, arr, endD, BigDecimal.valueOf(requireNum));
                //dbresult = rroomsMapper.getResourceLeft(products, arr, endD, block, BigDecimal.valueOf(requireNum));
            }
        }
        for (Map<String, Object> row : dbresult) {
            String prod = row.get(prodcol).toString();
            Integer num = NumberUtil.parseInt(row.get(avlcol) + "");
            result.put(prod, num);
        }
        return result;
    }


    public boolean checkDBSkuLeft(String hotelId, List<TSkuUpd> skuUpds) throws DefinedException {
        if (skuUpds == null || skuUpds.isEmpty()) {
            return true;
        }


        for (TSkuUpd skuUpd : skuUpds) {
            if (!CoreCache.isCacheProductType(skuUpd.getProdType()) || skuUpd.getChangeNum() <= 0) { //如果是要减少库存 或者不是房型的库存缓存 不检查 just
                continue;
            }
            String checkSql = "select datum from Rrooms where hotelId=?1 and roomType=?2 and datum between ?3 and ?4 and total-ooo-pickup<?5";
            Date overSellDate = daoLocal.getObject(checkSql, hotelId, skuUpd.getSkuid(), skuUpd.getStartdate(), skuUpd.getEnddate(), skuUpd.getChangeNum());
            if (overSellDate != null) {
                throw new DefinedException(StrUtil.format("{}在{}库存不足!", ContentCacheTool.getProductDesc(skuUpd.getProdType(), skuUpd.getSkuid(), hotelId), CalculateDate.dateToString(overSellDate)), ResultCode.OVERBOOK_ERR.code());
            }

        }
        return true;
    }

    /**
     * 根据订单表 更新房间占用状态
     *
     * @param orgRoom 原始房间
     * @param newRoom 新房间
     */
    public void updRoomOccStatus(String orgRoom, String newRoom, String hotelId, boolean refreshAll) {

        String sql = "UPDATE room r \n" +
                "SET locc = CASE \n" +
                "    WHEN EXISTS (\n" +
                "        SELECT 1 \n" +
                "        FROM reservation o \n" +
                "        WHERE o.room_number = r.room_no\n" +
                "        AND o.hotelid = r.hotelid \n" +
                "        AND o.hotelid = '" + hotelId + "'\n" +
                "        AND (\n" +
                "            (o.reservation_status = 0 \n" +
                "            AND CURRENT_DATE BETWEEN o.arrival_date AND o.departure_date)\n" +
                "            OR \n" +
                "            (o.reservation_status = 1 \n" +
                "            AND CURRENT_DATE BETWEEN o.arrival_date AND o.departure_date)\n" +
                "            OR\n" +
                "            (o.reservation_status = 1 \n" +
                "            AND o.departure_date = CURRENT_DATE)\n" +
                "        )\n" +
                "    ) THEN 1\n" +
                "    ELSE 0\n" +
                "END\n" +
                "WHERE r.hotelid = '" + hotelId + "';";
        int count = daoLocal.batchNativeOption(sql);  //MYSQL 版本的语句.如果是其他数据库.再改咯

        if (count > 0) {
            GlobalCache.getInstance().refreshAndNotify(GlobalDataType.ROOM, hotelId);
        }

    }


    public void repResRevs(String hotelid) {
        List<Date> dates;
		{
			AccountItemCache cache_accitem = GlobalCache.getDataStructure().getCache(GlobalDataType.ACCOUNTITEM);
			DefValData<String, String> deptGrp = new DefValData<String, String>()
					.setInitHandler(code -> ObjectUtil.defaultIfNull(cache_accitem.getRecord(hotelid, code), e -> e.getIncomeGroup(), IncomeTypeEnum.OTHER.getCode()));
			dates = daoLocal.getNativeObjectList("select distinct business_date  from DailyStat_Res where hotelId=?1", hotelid);
			log.info("修复res.{}", dates.size());
			dates.forEach(da -> {
				DefValData<String, DefValData<String, Object[]>> currs = new DefValData<String, DefValData<String, Object[]>>()
						.setInitHandler(resno -> new DefValData<String, Object[]>().setInitHandler(deptcode -> new Object[] { resno, deptcode, BigDecimal.ZERO, BigDecimal.ZERO }));
				DefValData<String, DefValData<String, Object[]>> blcs = new DefValData<String, DefValData<String, Object[]>>()
						.setInitHandler(resno -> new DefValData<String, Object[]>().setInitHandler(deptcode -> new Object[] { resno, deptcode, BigDecimal.ZERO, BigDecimal.ZERO }));
				{
					List<Object[]> accList = daoLocal.getList(
							"select reservationNumber, departmentCode, sum(price*quantity), sum(credit) from GuestAccounts where hotelId=:hotelId and business_date=:date group by reservationNumber, departmentCode",
							Map.of("hotelId", hotelid, "date", DateUtil.toLocalDateTime(da).toLocalDate()));
					accList.forEach(acc -> {
						Object[] objs = currs.get((String) acc[0]).get((String) acc[1]);
						objs[2] = NumberUtil.add((BigDecimal) objs[2], (BigDecimal) acc[2]);
						objs[3] = NumberUtil.add((BigDecimal) objs[3], (BigDecimal) acc[3]);
					});
					accList = daoLocal.getList(
							"select reservationNumber, departmentCode, sum(price*quantity), sum(credit) from GuestAccountsHis where hotelId=:hotelId and business_date=:date group by reservationNumber, departmentCode",
							Map.of("hotelId", hotelid, "date", DateUtil.toLocalDateTime(da).toLocalDate()));
					accList.forEach(acc -> {
						Object[] objs = currs.get((String) acc[0]).get((String) acc[1]);
						objs[2] = NumberUtil.add((BigDecimal) objs[2], (BigDecimal) acc[2]);
						objs[3] = NumberUtil.add((BigDecimal) objs[3], (BigDecimal) acc[3]);
					});
				}
				{
					List<Object[]> accList = daoLocal.getList(
							"select reservationNumber, departmentCode, sum(price*quantity), sum(credit) from GuestAccounts where hotelId=:hotelId and business_date<=:date group by reservationNumber, departmentCode",
							Map.of("hotelId", hotelid, "date", DateUtil.toLocalDateTime(da).toLocalDate()));
					accList.forEach(acc -> {
						Object[] objs = blcs.get((String) acc[0]).get((String) acc[1]);
						objs[2] = NumberUtil.add((BigDecimal) objs[2], (BigDecimal) acc[2]);
						objs[3] = NumberUtil.add((BigDecimal) objs[3], (BigDecimal) acc[3]);
					});
					accList = daoLocal.getList(
							"select reservationNumber, departmentCode, sum(price*quantity), sum(credit) from GuestAccountsHis where hotelId=:hotelId and business_date<=:date group by reservationNumber, departmentCode",
							Map.of("hotelId", hotelid, "date", DateUtil.toLocalDateTime(da).toLocalDate()));
					accList.forEach(acc -> {
						Object[] objs = blcs.get((String) acc[0]).get((String) acc[1]);
						objs[2] = NumberUtil.add((BigDecimal) objs[2], (BigDecimal) acc[2]);
						objs[3] = NumberUtil.add((BigDecimal) objs[3], (BigDecimal) acc[3]);
					});
				}
				List<DailyStat_Res> es = daoLocal.getObjectList("from DailyStat_Res where hotelId=?1 and business_date=?2", hotelid, DateUtil.toLocalDateTime(da).toLocalDate());
				log.info("{}:{}", da, es.size());
				es.forEach(e -> {
					StatRevenues revenues = JSONUtil.toBean(e.getRevenues(), StatRevenues.class);
					{
						DefValData<String, StatRevenue> grpIncome = new DefValData<String, StatRevenue>().setInitHandler(k -> new StatRevenue().setCode(k));
						revenues.setAcc_code(new ArrayList<>());
						 currs.get(e.getReservationNumber()).getDataMap().values().forEach(objs -> {
							StatRevenue statRevenue = new StatRevenue().setCode((String) objs[1]).setDebit((BigDecimal) objs[2]).setCredit((BigDecimal) objs[3]);
							revenues.getAcc_code().add(statRevenue);
							StatRevenue incGrp = grpIncome.get(deptGrp.get(statRevenue.getCode()));
							incGrp.setDebit(incGrp.getDebit().add(statRevenue.getDebit()));
							incGrp.setCredit(incGrp.getCredit().add(statRevenue.getCredit()));
						});
						revenues.setIncome(grpIncome.getDataMap().values());
					}
					e.setRevenues(JSONUtil.toJsonStr(revenues));
					StatRevenues balances = new StatRevenues().setTotal(new StatRevenue().setDebit(BigDecimal.ZERO).setCredit(BigDecimal.ZERO)).setAcc_code(new ArrayList<>())
							.setIncome(null);
					blcs.get(e.getReservationNumber()).getDataMap().values().forEach(objs -> {
						balances.getTotal().setDebit(balances.getTotal().getDebit().add((BigDecimal) objs[2]));
						balances.getTotal().setCredit(balances.getTotal().getCredit().add((BigDecimal) objs[3]));
						balances.getAcc_code().add(new StatRevenue().setCode((String) objs[1]).setDebit((BigDecimal) objs[2]).setCredit((BigDecimal) objs[3]));
					});
					try {
						balances.setTotal(JSONUtil.toBean(e.getBalances(), StatRevenue.class));
					} catch (Exception e1) {
					}
					e.setBalances(JSONUtil.toJsonStr(balances));
				});
				dailyStatResMapper.saveAll(es);
				dailyStatResMapper.flush();
			});
		}
        if(false){
            dates = daoLocal.getNativeObjectList("select distinct business_date  from DailyStat_Res where hotelId=?1 and revenues->'$.income' IS NULL", hotelid);
            log.info("修复res.revenue.income {}", dates.size());
            dates.forEach(da -> {
                List<DailyStat_Res> es = daoLocal.getObjectList("from DailyStat_Res where hotelId=?1 and business_date=?2", hotelid, DateUtil.toLocalDateTime(da).toLocalDate());
                log.info("{}:{}", da, es.size());
                es.forEach(e -> {
                    StatRevenues data = JSONUtil.toBean(e.getRevenues(), StatRevenues.class);
                    if (CollectionUtil.isEmpty(data.getIncome()) && null != data.getGrp_income()) {
                        data.getGrp_income().forEach((k, v) -> v.setCode(k));
                        data.setIncome(data.getGrp_income().values());
//				data.setGrp_income(null);
                        e.setRevenues(JSONUtil.toJsonStr(data));
                    }
                });
                dailyStatResMapper.saveAll(es);
            });
        }
        if(false){
            dates = daoLocal.getNativeObjectList("select distinct business_date  from DailyStat_Res where hotelId=?1 and revenues->'$.acc_code' IS NULL", hotelid);
            log.info("修复res.revenue.acc_code {}", dates.size());
            dates.forEach(da -> {
                DefValData<String, DefValData<String, Object[]>> amts = new DefValData<String, DefValData<String, Object[]>>()
                        .setInitHandler(resno -> new DefValData<String, Object[]>().setInitHandler(deptcode -> new Object[]{resno, deptcode, BigDecimal.ZERO, BigDecimal.ZERO}));
                List<Object[]> accList = daoLocal.getList(
                        "select reservationNumber, departmentCode, sum(price*quantity), sum(credit) from GuestAccounts where hotelId=:hotelId and business_date=:date group by reservationNumber, departmentCode",
                        Map.of("hotelId", hotelid, "date", DateUtil.toLocalDateTime(da).toLocalDate()));
                accList.forEach(acc -> {
                    Object[] objs = amts.get((String) acc[0]).get((String) acc[1]);
                    objs[2] = NumberUtil.add((BigDecimal) objs[2], (BigDecimal) acc[2]);
                    objs[3] = NumberUtil.add((BigDecimal) objs[3], (BigDecimal) acc[3]);
                });
                accList = daoLocal.getList(
                        "select reservationNumber, departmentCode, sum(price*quantity), sum(credit) from GuestAccountsHis where hotelId=:hotelId and business_date=:date group by reservationNumber, departmentCode",
                        Map.of("hotelId", hotelid, "date", DateUtil.toLocalDateTime(da).toLocalDate()));
                accList.forEach(acc -> {
                    Object[] objs = amts.get((String) acc[0]).get((String) acc[1]);
                    objs[2] = NumberUtil.add((BigDecimal) objs[2], (BigDecimal) acc[2]);
                    objs[3] = NumberUtil.add((BigDecimal) objs[3], (BigDecimal) acc[3]);
                });
                List<DailyStat_Res> es = daoLocal.getObjectList("from DailyStat_Res where hotelId=?1 and business_date=?2", hotelid, DateUtil.toLocalDateTime(da).toLocalDate());
                log.info("{}:{}", da, es.size());
                es.forEach(e -> {
                    StatRevenues data = JSONUtil.toBean(e.getRevenues(), StatRevenues.class);
                    if (CollectionUtil.isEmpty(data.getAcc_code())) {
                        DefValData<String, Object[]> acc = amts.get(e.getReservationNumber());
                        data.setAcc_code(new ArrayList<>());
                        acc.getDataMap().values().forEach(objs -> {
                            data.getAcc_code().add(new StatRevenue().setCode((String) objs[1]).setDebit((BigDecimal) objs[2]).setCredit((BigDecimal) objs[3]));
                        });
                        e.setRevenues(JSONUtil.toJsonStr(data));
                    }
                });
                dailyStatResMapper.saveAll(es);
            });
        }
        if(false){
            dates = daoLocal.getNativeObjectList("select distinct business_date  from DailyStat_Res where hotelId=?1 and balances->'$.acc_code' IS NULL", hotelid);
            log.info("修复res.balances.acc_code {}", dates.size());
            dates.forEach(da -> {
                DefValData<String, DefValData<String, Object[]>> amts = new DefValData<String, DefValData<String, Object[]>>()
                        .setInitHandler(resno -> new DefValData<String, Object[]>().setInitHandler(deptcode -> new Object[]{resno, deptcode, BigDecimal.ZERO, BigDecimal.ZERO}));
                List<Object[]> accList = daoLocal.getList(
                        "select reservationNumber, departmentCode, sum(price*quantity), sum(credit) from GuestAccounts where hotelId=:hotelId and business_date<=:date group by reservationNumber, departmentCode",
                        Map.of("hotelId", hotelid, "date", DateUtil.toLocalDateTime(da).toLocalDate()));
                accList.forEach(acc -> {
                    Object[] objs = amts.get((String) acc[0]).get((String) acc[1]);
                    objs[2] = NumberUtil.add((BigDecimal) objs[2], (BigDecimal) acc[2]);
                    objs[3] = NumberUtil.add((BigDecimal) objs[3], (BigDecimal) acc[3]);
                });
                accList = daoLocal.getList(
                        "select reservationNumber, departmentCode, sum(price*quantity), sum(credit) from GuestAccountsHis where hotelId=:hotelId and business_date<=:date group by reservationNumber, departmentCode",
                        Map.of("hotelId", hotelid, "date", DateUtil.toLocalDateTime(da).toLocalDate()));
                accList.forEach(acc -> {
                    Object[] objs = amts.get((String) acc[0]).get((String) acc[1]);
                    objs[2] = NumberUtil.add((BigDecimal) objs[2], (BigDecimal) acc[2]);
                    objs[3] = NumberUtil.add((BigDecimal) objs[3], (BigDecimal) acc[3]);
                });
                List<DailyStat_Res> es = daoLocal.getObjectList("from DailyStat_Res where hotelId=?1 and business_date=?2", hotelid, DateUtil.toLocalDateTime(da).toLocalDate());
                log.info("{}:{}", da, es.size());
                es.forEach(e -> {
                    try {
                        StatRevenue total = null == e.getBalances() ? new StatRevenue().setDebit(BigDecimal.ZERO).setCredit(BigDecimal.ZERO)
                                : JSONUtil.toBean(e.getBalances(), StatRevenue.class);
                        StatRevenues data = new StatRevenues().setTotal(total).setAcc_code(new ArrayList<>()).setIncome(null);
                        DefValData<String, Object[]> acc = amts.get(e.getReservationNumber());
                        data.setAcc_code(new ArrayList<>());
                        acc.getDataMap().values().forEach(objs -> {
                            if (null == e.getBalances()) {
                                data.getTotal().setDebit(data.getTotal().getDebit().add((BigDecimal) objs[2]));
                                data.getTotal().setCredit(data.getTotal().getCredit().add((BigDecimal) objs[3]));
                            }
                            data.getAcc_code().add(new StatRevenue().setCode((String) objs[1]).setDebit((BigDecimal) objs[2]).setCredit((BigDecimal) objs[3]));
                        });
                        e.setBalances(JSONUtil.toJsonStr(data));
                    } catch (Exception e1) {
                    }
                });
                dailyStatResMapper.saveAll(es);
            });
        }
        log.info("统计表修复完成");
    }


}

package com.cw.core;

import cn.hutool.core.util.RandomUtil;
import com.cw.arithmetic.SysFunLibTool;
import com.cw.cache.GlobalCache;
import com.cw.cache.RedisTool;
import com.cw.utils.CalculateDate;
import com.cw.utils.RedisKey;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.GlobalDataType;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * 统一的获取订单号服务
 */
@Service
public class SeqNoService {


    static SeqNoService seqNoService;

    RedissonClient redissonClient;


    @Autowired
    public SeqNoService(RedissonClient client) {
        this.redissonClient = client;
        seqNoService = this;
    }

    public static SeqNoService getInstance() {
        return seqNoService;
    }

    public String getSequenceID(SystemUtil.SequenceKey sequenceKey) {
        return getRedisNewValue(sequenceKey);
    }


    /**
     * 根据请求的数字.等幂返回本地数字
     *
     * @param sequenceKey
     * @param orderNo
     * @return
     */
    public String getSequenceID_withOrderId(SystemUtil.SequenceKey sequenceKey, String orderNo) {
        //先用otaSeqNo 查找是否已经生成了序列号.如果有.不再生成一个新的.
        if (orderNo == null || orderNo.isEmpty()) {
            return getRedisNewValue(sequenceKey);
        }
        RMapCache<String, String> rMapCache = redissonClient.getMapCache(RedisKey.SeqNo_Map);
        String cacheKey = sequenceKey + orderNo;
        String cacheVal = rMapCache.getOrDefault(cacheKey, null);
        if (cacheVal != null) {
            return cacheVal;
        } else {
            cacheVal = getRedisNewValue(sequenceKey);
            rMapCache.put(cacheKey, cacheVal, 30, TimeUnit.MINUTES);  //一般是用于支付流水号.不用太长
        }
        return cacheVal;
    }


    private String getRedisNewValue(SystemUtil.SequenceKey sequenceKey) {
        String day = CalculateDate.dateToString(new Date(), false, CalculateDate.keyspd);
        String key = day + sequenceKey;
        if (Boolean.TRUE.equals(RedisTool.getInstance().opsForValue().setIfAbsent(key, "1"))) {//如果当天还没有值.就初始化为1,从1开始计数
            RedisTool.getInstance().expire(key, 25, TimeUnit.HOURS); //设置失效时间是25小时 .25小时之后就自动释放计数了
        }
        String value = RedisTool.getInstance().opsForValue().increment(key, 1).toString();
        int length = value.length();  //将当前数字补全
        if (length < 5) {
            Random random = new Random();
            String zero = "";
            for (int i = 0; i < 5 - length; i++) {//
                zero = "0" + zero;
            }
            int suijilength = zero.length() / 2;
            String suijistr = "";
            for (int i = 0; i < suijilength; i++) {
                suijistr = suijistr + random.nextInt(9);
            }
            if (suijilength < zero.length()) {
                zero = suijistr + zero.substring(suijilength);
            }
            value = zero + value;
        }
        return day + value;
    }

    /**
     * 用于生成唯一的表记录代码.比如酒店代码.房型代码等
     * 针对中文.生成拼音简写. 英文则用空格分割.取字母开头并大写
     *
     * @param description 用户输入的描述
     * @param type        可以不传.如果传入.则会检查是否已经存在.如果存在则在后面加上3位随机数
     * @param hotelId     当前酒店ID 结合type使用.
     * @return
     */
    public String getUniqueCode(String description, String hotelId, GlobalDataType type) {
        String uniqueCode = SysFunLibTool.produceSimpleStr(description);
        if (type != null) {
            Object o = GlobalCache.getDataStructure().getCache(type).getRecord(hotelId, uniqueCode);
            if (o != null) {
                uniqueCode = uniqueCode + RandomUtil.randomString(3).toUpperCase();
            }
        }
        return uniqueCode;
    }


}

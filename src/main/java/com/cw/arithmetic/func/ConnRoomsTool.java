package com.cw.arithmetic.func;

import cn.hutool.core.util.StrUtil;
import com.cw.arithmetic.others.ConnRoomInfo;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.RoomCache;
import com.cw.cache.impl.RoomTypeCache;
import com.cw.entity.Room;
import com.cw.entity.RoomType;
import com.cw.utils.enums.GlobalDataType;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2025/7/15 02:33
 **/
public class ConnRoomsTool {

    /**
     * 如果预订的是套房房型.就把关联的所有物理房间都拉取出来.包括房型,房号信息.为了方便创建预订
     *
     * @param roomType
     * @param hotelId
     * @return
     */
    public static List<ConnRoomInfo> dragMyConnRoom(String roomType, String hotelId) {
        RoomTypeCache rmt = GlobalCache.getDataStructure().getCache(GlobalDataType.ROOMTYPE);
        RoomType rt = rmt.getRecord(hotelId, roomType);
        if (rt != null && rt.getGeneric()) {
            RoomCache cache = GlobalCache.getDataStructure().getCache(GlobalDataType.ROOM);
            List<Room> rooms = cache.getDataListWithCondition(hotelId, r -> r.getRoomType().equals(rt.getRoomType()));
            for (Room room : rooms) {
                if (StrUtil.isNotBlank(room.getSuirooms())) {
                    List<ConnRoomInfo> connRoomInfos = Lists.newArrayList();
                    connRoomInfos.add(new ConnRoomInfo(room.getRoomType(), room.getRoomNo(), true));
                    String[] suirooms = room.getSuirooms().split(",");
                    for (String suiroom : suirooms) {
                        connRoomInfos.add(new ConnRoomInfo(room.getRoomType(), suiroom, false));
                    }
                    return connRoomInfos;
                }
                break;
            }
        }
        return Lists.newArrayList();
    }


}

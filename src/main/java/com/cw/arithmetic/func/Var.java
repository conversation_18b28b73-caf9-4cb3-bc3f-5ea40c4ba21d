package com.cw.arithmetic.func;

/**
 * 用来把变量带回来
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2022/1/10 23:59
 **/
public class Var<T> {
    public T value;

    public Var() {
    }

    public Var(T value) {
        this.value = value;
    }

    public T getValue() {
        return value;
    }

    public void setValue(T value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return value.toString();
    }

    @Override
    public int hashCode() {
        return value.hashCode();
    }

    @Override
    public boolean equals(Object obj) {
        return value.equals(obj);
    }
}


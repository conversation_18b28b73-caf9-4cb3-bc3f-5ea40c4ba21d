
package com.cw.arithmetic.pay;

import com.cw.exception.DefinedException;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

import java.math.BigDecimal;
import java.util.List;

/**
 * 收到支付成功或者退款成功之后往往需要对数据进行进一步处理，比如更新订单状态，保存支付状态等
 * 但是支付业务往往对应多种订单场景.所以写一个通用的接口，用于处理不同的订单场景
 * 例如：每个酒店订单需要做付款成功后的处理，每个账户订单需要做付款成功后的处理
 * 以及SAAS用户进行续费.或者开通服务的操作
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2022/10/14 09:06
 **/
public interface OrderSenseNotifyDataHandler<T> {

    //    void setData(T t);
    void initData();

    boolean isCancelStatus();

    String getOrderStatus();

    String getOrderUid();

    String getBookingId();

    BigDecimal getOrderAmount();


    void saveOrderPayStatus(String transactionId, String outTradeNo, String payment);


    void updateTicketOrderStatus(String hotelId, String bookingId, String status);

    void initOrders(List<String> orderids);

    int getOrdersSize();

    BigDecimal getPayOrderAmount();

    String getPayOrderDesc();

    Long getPayExpireTimeStamp(String hotelId) throws DefinedException;


}

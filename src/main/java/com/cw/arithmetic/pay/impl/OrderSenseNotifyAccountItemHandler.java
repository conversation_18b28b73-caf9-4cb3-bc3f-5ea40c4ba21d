package com.cw.arithmetic.pay.impl;

import com.cw.arithmetic.pay.OrderSenseNotifyDataHandler;
import com.cw.entity.AccountItem;
import com.cw.exception.DefinedException;

import java.math.BigDecimal;
import java.util.List;

/**
 * 根据入账数据请求.进行支付发起
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/4/18 16:21
 **/
public class OrderSenseNotifyAccountItemHandler implements OrderSenseNotifyDataHandler<AccountItem> {
    String bookingId;

    public OrderSenseNotifyAccountItemHandler(String bookingId) {
        this.bookingId = bookingId;
    }

    @Override
    public void initData() {

    }

    @Override
    public boolean isCancelStatus() {
        return false;
    }

    @Override
    public String getOrderStatus() {
        return null;
    }

    @Override
    public String getOrderUid() {
        return null;
    }

    @Override
    public String getBookingId() {
        return null;
    }

    @Override
    public BigDecimal getOrderAmount() {
        return null;
    }

    @Override
    public void saveOrderPayStatus(String transactionId, String outTradeNo, String payment) {

    }

    @Override
    public void updateTicketOrderStatus(String hotelId, String bookingId, String status) {

    }

    @Override
    public void initOrders(List<String> orderids) {

    }

    @Override
    public int getOrdersSize() {
        return 0;
    }

    @Override
    public BigDecimal getPayOrderAmount() {
        return null;
    }

    @Override
    public String getPayOrderDesc() {
        return null;
    }

    @Override
    public Long getPayExpireTimeStamp(String hotelId) throws DefinedException {
        return null;
    }
}

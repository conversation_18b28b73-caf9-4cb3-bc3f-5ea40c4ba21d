package com.cw.arithmetic;

import java.lang.reflect.Field;

public class PojoUtils {

    private static Class getClass(Class c) {
        return c;
//        String str = "class java.lang.Object";
//        if (c.toString().indexOf("entity") == -1)
//            return c;
//        if (c.getSuperclass().toString().equals(str))
//            return c;
//        else
//            return c.getSuperclass();
    }


    public static <CloneObj> CloneObj cloneEntity(CloneObj src, boolean... copyAll) {
        CloneObj result = null;
        try {
            Class c = getClass(src.getClass());
            Class orgc = src.getClass();
            boolean copysqlid = copyAll != null && copyAll.length > 0 && copyAll[0] != false;
            // getClass(Class.forName(c.getName()))
            result = (CloneObj) Class.forName(orgc.getName()).newInstance();
            Field[] fields = c.getDeclaredFields();
            Field.setAccessible(fields, true);
            for (Field field : fields) {
                if (field.getName().equals("serialVersionUID")) {
                    continue;
                }
                if (copysqlid) {
                    field.set(result, field.get(src));
                } else {
                    if (!field.getName().toLowerCase().contains("id"))
                        field.set(result, field.get(src));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        return result;
    }
}

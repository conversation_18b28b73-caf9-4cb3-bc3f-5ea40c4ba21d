package com.cw.arithmetic.rate;

import cn.hutool.core.util.StrUtil;
import com.cw.arithmetic.PojoUtils;
import com.cw.arithmetic.func.Var;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.HotelCache;
import com.cw.entity.Hotel;
import com.cw.entity.RoomRateDetail;
import com.cw.pojo.common.PkgNode;
import com.cw.utils.CalculateDate;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.GlobalDataType;
import com.google.common.collect.Lists;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/6/12 16:44
 **/
public class PriceTool {
    public static BigDecimal seekUseRoomRate(List<RoomRateDetail> roomRateDetails, Date calcDate, Integer person, String weekend) {
        for (RoomRateDetail roomRateDetail : roomRateDetails) {
            if (CalculateDate.isInRange(calcDate, roomRateDetail.getStartTime(), roomRateDetail.getEndTime())) {
                //return roomRateDetail.getRate1();
                int week = CalculateDate.getDateProperty(calcDate, Calendar.DAY_OF_WEEK); //java 里面星期天从1开始,然后是2,3,4,5,6,7  .依次类推 星期六代表7
                boolean lweekend = weekend.contains(week + "");
                return lweekend ? roomRateDetail.getWeekRate1() : roomRateDetail.getRate1();
            }
        }
        return BigDecimal.ZERO;
    }

    public static BigDecimal getDatesRate(Date calcDate, String weekend, RoomRateDetail roomRateDetail) {
        int week = CalculateDate.getDateProperty(calcDate, Calendar.DAY_OF_WEEK); //java 里面星期天从1开始,然后是2,3,4,5,6,7 .依次类推 星期六代表7
        boolean lweekend = weekend.contains(week + "");
        return lweekend ? roomRateDetail.getWeekRate1() : roomRateDetail.getRate1();
    }

    public static String getHotelWeekend(String rateCode, String hotelId) {
        HotelCache hotelCache = GlobalCache.getDataStructure().getCache(GlobalDataType.HOTEL);
        Hotel hotel = hotelCache.getRecord(hotelId, hotelId);
        if (hotel != null) {
            return hotel.getWeekendDefinition();
        }
        return "6,7";
    }


    public static BigDecimal getDatesRate(Date calcDate, BigDecimal normalRate, BigDecimal weekendRate, String weekend) {
        int week = CalculateDate.getDateProperty(calcDate, Calendar.DAY_OF_WEEK); //java 里面星期天从1开始,然后是2,3,4,5,6,7  .依次类推 星期六代表7
        boolean lweekend = weekend.contains(week + "");
        return lweekend ? weekendRate : normalRate;
    }

    public void splitRate(List<RoomRateDetail> toBeSaveRatedetail, Date fromDate, Date toDate, RoomRateDetail postForm) {
        List<RoomRateDetail> temp = new ArrayList<RoomRateDetail>();
        for (RoomRateDetail rate_det : toBeSaveRatedetail) {
            boolean split = (CalculateDate.beforeEqual(rate_det.getStartTime(), fromDate) && CalculateDate.afterEqual(rate_det.getEndTime(), fromDate))
                    || (CalculateDate.afterEqual(rate_det.getStartTime(), fromDate) && CalculateDate.beforeEqual(rate_det.getStartTime(), toDate));

            boolean override = CalculateDate.beforeEqual(fromDate, rate_det.getStartTime()) && CalculateDate.afterEqual(toDate, rate_det.getEndTime());//这条记录应该被覆盖更新价格
            RoomRateDetail oldRate = PojoUtils.cloneEntity(rate_det, true);

            if (override) {
                applyUpdate(rate_det, postForm);
            } else if (split) {
                //判断是切成两段。还是切成3段
                if (CalculateDate.isAfter(fromDate, rate_det.getStartTime()) && CalculateDate.isBefore(toDate, rate_det.getEndTime())) {//如果更新区段在这条记录的日期范围之内  切成3段
                    Date newEndDate = CalculateDate.maxDate(rate_det.getStartTime(), CalculateDate.reckonDay(fromDate, 5, -1));
                    Date orgEndDate = rate_det.getEndTime();
                    rate_det.setEndTime(newEndDate);//第一段

                    RoomRateDetail newRate_det = PojoUtils.cloneEntity(rate_det);// ref.copyEntityBean(rate_det, new Rate_det());
                    newRate_det.setStartTime(fromDate);
                    Date newRecordEndDate = CalculateDate.minDate(orgEndDate, toDate);
                    newRate_det.setEndTime(newRecordEndDate);
                    applyUpdate(newRate_det, postForm);
                    temp.add(newRate_det);//第二段

                    if (CalculateDate.isBefore(newRecordEndDate, orgEndDate)) {
                        RoomRateDetail newRate_det2 = PojoUtils.cloneEntity(rate_det);
                        newRate_det2.setStartTime(CalculateDate.reckonDay(newRecordEndDate, 5, 1));
                        newRate_det2.setEndTime(orgEndDate);
                        temp.add(newRate_det2);//第三段
                    }
                } else {//切成2段
                    Date newEndDate = null;
                    Date orgEndDate = rate_det.getEndTime();
                    if (CalculateDate.isAfter(toDate, rate_det.getStartTime()) && CalculateDate.isBefore(toDate, rate_det.getEndTime())) {
                        newEndDate = toDate; //更新前半段的价格
                    } else {
                        newEndDate = CalculateDate.maxDate(rate_det.getStartTime(), CalculateDate.reckonDay(fromDate, 5, -1));//更新后半段的价格
                    }
                    rate_det.setEndTime(newEndDate);//先将原记录先改短
                    boolean lupd1 = CalculateDate.beforeEqual(fromDate, rate_det.getStartTime());
                    if (lupd1) {//判断是更新前一段还是更新后一段的价格
                        applyUpdate(rate_det, postForm);
                    }

                    RoomRateDetail newRate_det = PojoUtils.cloneEntity(oldRate);
                    newRate_det.setStartTime(CalculateDate.reckonDay(newEndDate, 5, 1));
                    newRate_det.setEndTime(oldRate.getEndTime());
                    boolean lupd2 = CalculateDate.isEqual(newRate_det.getStartTime(), fromDate);
                    if (lupd2) {//判断是更新交集的前面一段还是更新后面一段
                        applyUpdate(newRate_det, postForm);
                    }
                    temp.add(newRate_det);
                }
            }
        }

        int length = CalculateDate.compareDates(toDate, fromDate).intValue() + 1;
        List<Date> dates = Lists.newArrayList();
        boolean lexist = false;
        for (int j = 0; j < length; j++) {  //循环一遍.检查每一天.看是否更新的时间段内有空缺.空的就记录下来.回补一下. JUST 2021.01.24
            Date d = CalculateDate.reckonDay(fromDate, 5, j);
            lexist = false;
            for (RoomRateDetail ratedetail : toBeSaveRatedetail) {   //循环一遍数据库已有记录.看看是否还需要缺的日期.
                if (CalculateDate.isInRange(d, ratedetail.getStartTime(), ratedetail.getEndTime())) {
                    lexist = true;
                }
            }
            if (!lexist) {
                for (RoomRateDetail ratedetail : temp) {
                    if (CalculateDate.isInRange(d, ratedetail.getStartTime(), ratedetail.getEndTime())) {
                        lexist = true;
                    }
                }
            }
            if (!lexist) {
                dates.add(d);
            }
        }
        List<Date[]> ds = CalculateDate.getDateStartEnd2(dates);  //分离出 start end
        for (Date[] d : ds) {
            RoomRateDetail ratedetail = PojoUtils.cloneEntity(postForm);
            ratedetail.setStartTime(d[0]);//补充缺的日期段
            ratedetail.setEndTime(d[1]);//补充缺的日期段
            temp.add(ratedetail);
        }
        if (!temp.isEmpty()) {
            toBeSaveRatedetail.addAll(temp);
        }
    }

    private void applyUpdate(RoomRateDetail ratedetail, RoomRateDetail req) {
        //一人房价
        ratedetail.setRate1(req.getRate1() == null ? ratedetail.getRate1() : req.getRate1());
        ratedetail.setWeekRate1(req.getWeekRate1() == null ? ratedetail.getWeekRate1() : req.getWeekRate1());
    }


    public static List<PkgNode> calculatePackages(List<String> packageStrings, Var<String> sumaryStr) {
        // 用于存储最终结果的Map, key为产品代码，value为数量
        Map<String, Integer> packageMap = new HashMap<>();

        // 遍历输入的字符串列表
        for (String pkgStr : packageStrings) {
            if (StrUtil.isBlank(pkgStr)) {  //避免处理空串
                continue;
            }
            // 处理每个字符串中的多个项目（以逗号分隔）
            String[] items = pkgStr.split(",");

            for (String item : items) {
                // 分割数量和产品代码
                String[] parts = item.split("\\|");
                if (parts.length == 2) {
                    int quantity = Integer.parseInt(parts[0].trim());
                    String code = parts[1].trim();
                    // 将数量添加到Map中，如果已存在则累加
                    packageMap.merge(code, quantity, Integer::sum);
                }
            }
        }

        // 将Map转换为PkgNode列表
        String sumary = StrUtil.EMPTY;
        List<PkgNode> result = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : packageMap.entrySet()) {
            PkgNode node = new PkgNode();
            node.setNum(entry.getValue());
            node.setPkg(entry.getKey());
            String s = entry.getValue() + SystemUtil.PARAMSIGNAL + entry.getKey();
            sumary += sumary.isEmpty() ? s : "," + s;
            result.add(node);
        }

        if (sumaryStr != null) {
            sumaryStr.setValue(sumary);
        }

        return result;
    }


}

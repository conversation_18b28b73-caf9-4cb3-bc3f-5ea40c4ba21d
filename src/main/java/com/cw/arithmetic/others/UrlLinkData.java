package com.cw.arithmetic.others;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 外部链接数据属性
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2022/11/23 15:24
 **/
@Data
@ApiModel("跳转链接数据-也用于轮播图")
public class UrlLinkData {

    @ApiModelProperty(value = "图片链接地址")
    String imgurl; //图片链接

    @ApiModelProperty(value = "内部链接地址-PC端/移动端可识别的路径地址")
    String url; //内部标准链接格式

    @ApiModelProperty(value = "小程序跳转链接地址 链接格式: appid +'|'+ 页面url  小程序内这个优先级别最高")
    String wxappurl;  //链接格式: appid +'|'+ 页面url  例如: wx27fefa396dc3cb05|pages/performance/performance

    @ApiModelProperty(value = "通常用于底部链接,配置外部友情链接,或者说公众号文章,PC端内这个优先级最高")
    String outurl;// 新窗口打开外部链接: https://www.sina.com.cn

}

package com.cw.arithmetic.others;

import java.util.Date;
import java.util.HashMap;

/**
 * <AUTHOR>
 * 这个房型在每个渠道里每天的更新信息
 */
public class Tinfo {
    private Date date;
    private HashMap<String, Integer> orgMap = new HashMap<String, Integer>();
    private HashMap<String, Integer> currentMap = new HashMap<String, Integer>();
    private HashMap<String, Integer> channelMap = new HashMap<String, Integer>();//最后的结果就是用currentMap-orgMap里对应的数字

    public int getChannelUpdateNum(String channel) {
        if (channelMap.containsKey(channel))
            return channelMap.get(channel);
        else
            return 0;
    }


    public void setOrgPickup(String channel, int pickup) {
//        if (!channel.isEmpty()) {
            orgMap.put(channel, pickup);
            if (!currentMap.containsKey(channel)) {
                currentMap.put(channel, 0);
            }
            if (!channelMap.containsKey(channel)) {
                channelMap.put(channel, 0);
            }
//        }
    }

    public void setCurrentPickup(String channel, int pickup) {
//        if (!channel.isEmpty()) {
            currentMap.put(channel, pickup);
            if (!orgMap.containsKey(channel)) {
                orgMap.put(channel, 0);
            }
            if (!channelMap.containsKey(channel)) {
                channelMap.put(channel, 0);
            }
//        }
    }

    public int getPickupChange(String channel) {
        if (channelMap.containsKey(channel)) {
            return channelMap.get(channel);
        }
        return 0;
    }

    public void sumPicupChange() {
        for (String key : channelMap.keySet()) {
            channelMap.put(key, currentMap.get(key) - orgMap.get(key));
        }
    }


    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }


}

package com.cw.arithmetic.others;

import java.util.HashMap;

public class SkuDetail {
    private HashMap<String, Integer> skuMap = new HashMap<String, Integer>();

    public void putProductAvl(String product, Integer avl) {
        skuMap.put(product, avl);
    }

    public Integer getProductAvl(String product) {
        if (skuMap.containsKey(product)) {
            return skuMap.get(product);
        } else {
            return 0;
        }
    }

   /* public void print() {
        //我拿来调试用的
        String printStr = "Ratecode:" + rcodeName + "(" + rcodeDesc + ") 的价格信息:";
        for (Entry<String, BigDecimal> entry : productPrice.entrySet()) {
            String i = MessageFormat.format("--房型{0} :价格 {1}--", entry.getKey(), entry.getValue());
            printStr = printStr + i;
        }
        System.out.println(printStr);
    }*/

}

package com.cw.arithmetic.others;

import com.alibaba.fastjson.JSON;
import com.cw.config.exception.CustomException;
import com.cw.entity.Profile;
import com.cw.entity.Reservation;
import com.cw.mapper.ProfileMapper;
import com.cw.mapper.ReservationMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.pms.req.profile.ProfileReq;
import com.cw.pojo.dto.pms.req.reservation.Accompany;
import com.cw.pojo.dto.pms.res.profile.ProfileRes;
import com.cw.service.config.profile.ProfileService;
import com.cw.service.context.GlobalContext;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 多预订,多证件.入住登记信息处理器
 * 改为Spring管理的Bean，确保事务一致性
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/10/11 11:03
 **/
@Service
@Transactional
public class GuestCheckinProfileProcessor {

    @Autowired
    private ReservationMapper rsMapper;

    @Autowired
    private ProfileService profileService;

    @Autowired
    private DaoLocal<?> daoLocal;

    /**
     * 处理入住登记的档案信息
     *
     * @param profileReqList
     * @param reservationNumberList
     * @param hotelId 酒店ID
     */
    public void processInfo(List<ProfileReq> profileReqList, List<String> reservationNumberList, String hotelId) {
        if (profileReqList.isEmpty() || reservationNumberList.isEmpty() || profileReqList.size() < reservationNumberList.size()) {
            if (profileReqList.size() < reservationNumberList.size()) {
                throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("入住登记信息数量至少需要填写" + reservationNumberList.size() + "份"));
            } else {
                throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("入住登记信息为空或预订号列表为空"));
            }
        }

        Map<String, List<ProfileReq>> reservationProfiles = distributeProfiles(profileReqList, reservationNumberList);

        for (Map.Entry<String, List<ProfileReq>> entry : reservationProfiles.entrySet()) {
            processReservation(entry.getKey(), entry.getValue(), hotelId);
        }
    }

    /**
     * 分离入住登记信息到各个预订号中
     *
     * @param profileReqList
     * @param reservationNumberList
     * @return
     */
    private Map<String, List<ProfileReq>> distributeProfiles(List<ProfileReq> profileReqList, List<String> reservationNumberList) {
        Map<String, List<ProfileReq>> result = new LinkedHashMap<>(); // 保持顺序
        int profileCount = profileReqList.size();
        int reservationsCount = reservationNumberList.size();


        // 计算基础分配数和余数
        int baseProfilesPerReservation = profileCount / reservationsCount;
        int remainingProfiles = profileCount % reservationsCount;

        int currentIndex = 0;

        // 为每个订单分配档案
        for (int i = 0; i < reservationsCount; i++) {
            String reservationNumber = reservationNumberList.get(i);
            List<ProfileReq> profiles = new ArrayList<>();

            // 计算当前订单应该分配的档案数量
            // 如果还有余数，多分配一个档案
            int profilesForThisReservation = baseProfilesPerReservation + (i < remainingProfiles ? 1 : 0);

            // 添加档案到当前订单
            for (int j = 0; j < profilesForThisReservation; j++) {
                profiles.add(profileReqList.get(currentIndex++));
            }

            result.put(reservationNumber, profiles);
        }

        return result;
    }

    /**
     * 将本订单号分配到的登记信息写入到订单表中
     *
     * @param reservationNumber
     * @param profiles
     * @param hotelId 酒店ID
     */
    private void processReservation(String reservationNumber, List<ProfileReq> profiles, String hotelId) {
        Reservation reservation = daoLocal.getObject("from Reservation where hotelId=?1 and reservationNumber=?2", hotelId, reservationNumber);
        if (reservation == null) {
            throw new RuntimeException("找不到预订信息");
        }

        ProfileReq mainProfile = findMainProfileReq(profiles);
        List<Accompany> accompanyList = new ArrayList<>();
        ProfileRes profileRes = profileService.addProfile(mainProfile, hotelId);

        for (ProfileReq profileReq : profiles) {
            if (profileReq != mainProfile) {
                // 陪同人员也需要档案号，需要通过订单号查询该订单下的所有入住人员信息，通过档案号去查询每个人
                ProfileRes profileAcc = profileService.addProfile(profileReq, hotelId);
                accompanyList.add(createAccompany(profileAcc.getProfileNumber(), profileReq));
            }
        }

        updateReservationPrecisely(reservation, profileRes, accompanyList);
    }

    private ProfileReq findMainProfileReq(List<ProfileReq> profiles) {
        ProfileReq profileReq = profiles.stream()
                .filter(ProfileReq::isMain)
                .findFirst()
                .orElse(profiles.get(0));
        //根据分配到的请求查找对应的Profile 表中档案信息
        return profileReq;
    }

    private Accompany createAccompany(String profileNumber, ProfileReq profileReq) {
        Accompany accompany = new Accompany();
        accompany.setGender(profileReq.getGender());
        accompany.setIdCard(profileReq.getIdCard());
        accompany.setProfileNumber(profileNumber);  //TODO  考虑下入住人员是否也需要档案号
        accompany.setGuestName(profileReq.getGuestName());
        return accompany;
    }

    /**
     * 精准更新预订信息，只更新档案相关字段，避免覆盖其他字段
     *
     * @param reservation   预订对象
     * @param mainProfile   主入住人档案
     * @param accompanyList 陪同人员列表
     */
    private void updateReservationPrecisely(Reservation reservation, ProfileRes mainProfile, List<Accompany> accompanyList) {
        String hotelId = reservation.getHotelId();
        String reservationNumber = reservation.getReservationNumber();
        String profileNumber = mainProfile.getProfileNumber();
        String accompanyJson = JSON.toJSONString(accompanyList);
        String guestName = mainProfile.getGuestName();
        int personTotal = accompanyList.size() + 1;
        Date modifiedTime = new Date();
        String userId = GlobalContext.getCurrentUserId();

        // 使用精准更新，只更新档案相关字段 .避免这里事物提交慢.下一步的 checkin 状态没改
        String updateJpql = "UPDATE Reservation SET " +
                "profileNumber = ?1, " +
                "accompany = ?2, " +
                "guestName = ?3, " +
                "personTotal = ?4, " +
                "modifiedTime = ?5, " +
                "createBy = ?6 " +
                "WHERE hotelId = ?7 AND reservationNumber = ?8";

        int updatedRows = daoLocal.batchOption(updateJpql,
                profileNumber, accompanyJson, guestName, personTotal,
                modifiedTime, userId, hotelId, reservationNumber);


        LoggerFactory.getLogger(this.getClass()).info("入住登记信息处理完成{} 客人姓名 {} 合住信息 {}",
                reservationNumber, guestName, accompanyJson);
    }

}

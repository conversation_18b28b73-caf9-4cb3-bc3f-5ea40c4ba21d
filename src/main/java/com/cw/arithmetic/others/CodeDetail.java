package com.cw.arithmetic.others;

import java.math.BigDecimal;
import java.util.HashMap;


public class CodeDetail {

    private String rcodeName = "";
    private String rcodeDesc = "";
    private HashMap<String, BigDecimal> productPrice = new HashMap<String, BigDecimal>();

    public void putProductPrice(String product, BigDecimal price) {
        productPrice.put(product, price);
    }

    public BigDecimal getProductPrice(String product) {
        if (productPrice.containsKey(product)) {
            return productPrice.get(product);
        } else {
            return BigDecimal.valueOf(-1);
        }
    }

    public HashMap<String, BigDecimal> getProductPrice() {
        return productPrice;
    }

    public String getRcodeName() {
        return rcodeName;
    }

    public void setRcodeName(String rcodeName) {
        this.rcodeName = rcodeName;
    }

    public String getRcodeDesc() {
        return rcodeDesc;
    }

    public void setRcodeDesc(String rcodeDesc) {
        this.rcodeDesc = rcodeDesc;
    }

    /* public void print() {
        //我拿来调试用的
        String printStr = "Ratecode:" + rcodeName + "(" + rcodeDesc + ") 的价格信息:";
        for (Entry<String, BigDecimal> entry : productPrice.entrySet()) {
            String i = MessageFormat.format("--房型{0} :价格 {1}--", entry.getKey(), entry.getValue());
            printStr = printStr + i;
        }
        System.out.println(printStr);
    }*/

}

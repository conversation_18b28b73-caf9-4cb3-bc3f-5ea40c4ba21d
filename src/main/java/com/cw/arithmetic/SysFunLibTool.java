package com.cw.arithmetic;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.cw.utils.CalculateDate;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.GlobalDataType;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.util.Date;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/3/13 14:36
 **/
public class SysFunLibTool {
    private static String aesKey = "CITYBAYTECH";  //暂时写死.后面弄成配置类型

    public static String getAesKey() {
        return SecureUtil.md5(aesKey).substring(0, 16);
    }

    public static String getAvlSource(String block) {
        return StrUtil.isBlank(block) ? SystemUtil.DEF_RESOURCEID : block;
    }

    /**
     * 加密内容向客户端传输
     *
     * @param content
     * @return
     */
    public static String encodeAesContent(String content) {
        String key = getAesKey();
        AES aes = SecureUtil.aes(key.getBytes());
        String entry = aes.encryptBase64(content.getBytes(StandardCharsets.UTF_8));//转成字符串后还需要再一次BASE64 .去掉一些符号
        return Base64.encode(entry);
    }

    /**
     * 解密客户端传输内容
     *
     * @param content
     * @return
     */
    public static String decodeAesContent(String content) {
        String key = getAesKey();
        AES aes = SecureUtil.aes(key.getBytes());
        String postContent = Base64.decodeStr(content);
        String decryptStr = null;
        try {
            decryptStr = aes.decryptStr(postContent);
        } catch (Exception e) {
            return StrUtil.EMPTY;
        }
        return decryptStr;
    }

    /**
     * 根据输入的描述.生成简写字符串.用于生成关键字
     *
     * @param description
     * @return
     */
    public static String produceSimpleStr(String description) {
        // 去除非中文英文数字的字符
        description = description.replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5 ]", "");
        StringBuilder resultBuilder = new StringBuilder();
        // 分词处理
        String[] words = description.split(" ");
        for (String word : words) {
            if (StrUtil.isBlank(word)) {
                continue;
            }
            // 判断当前词是否包含中文
            if (word.matches(".*[\u4e00-\u9fa5]+.*")) {
                // 中文取拼音首字母
                resultBuilder.append(PinyinUtil.getFirstLetter(word, ""));
            } else {
                // 英文保留所有字母
                resultBuilder.append(word.replaceAll("[^a-zA-Z]", ""));
            }
        }
        String result = resultBuilder.toString();
        // 如果结果为空，添加随机字符串
        if (StrUtil.isBlank(result)) {
            result = RandomUtil.randomString(6) + result;
        }
        // 如果原始输入为纯数字，将所有数字添加到结果末尾
        if (description.matches("^[0-9]+$")) {
            result += description.replaceAll("[^0-9]", "");
        }
        return result.toUpperCase();
    }


    public static String getShowPrice(BigDecimal price) {
        return price.setScale(2, RoundingMode.HALF_EVEN).stripTrailingZeros().toPlainString();
    }

    public static Date[] getQueryProperRange(Date startDate, Date endDate) {

        if (CalculateDate.isAfter(startDate, endDate)) {
            Date temp = startDate;
            startDate = endDate;
            endDate = temp;
        }

        // 确保开始日期不早于当前日期
        Date today = DateUtil.beginOfDay(new Date());
        if (startDate.before(today)) {
            startDate = today;
        }

        // 限制查询范围最多120天
        Date maxEndDate = DateUtil.offsetDay(startDate, 120);
        if (endDate.after(maxEndDate)) {
            endDate = maxEndDate;
        }

        return new Date[]{startDate, endDate};

    }

}

package com.cw.arithmetic.sku;



import com.cw.utils.ProdType;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2018/9/15 14:35
 **/
public class ToccItem {

    private Table<String, String, Integer> orderinfo = HashBasedTable.create();//产品类型-产品id-数量



    public void updOcc(String prodType, String skuid, Integer num) {
        orderinfo.put(prodType, skuid, num);
    }

    public Integer getOcc(String prodType, String skuid) {
        Integer num= orderinfo.get(prodType, skuid);
        return num==null?0:num;
    }

    public Map<String, Integer> getOccMap(String prodType) {
        return orderinfo.row(prodType);
    }

    public Map<String, Map<String, Integer>> getAllProdOccMap() {
        return  orderinfo.rowMap();
    }

    /**
     *
     * @return //产品类型-产品id-数量
     */
    public Table<String, String, Integer> getOrderinfo() {
        return orderinfo;
    }

}

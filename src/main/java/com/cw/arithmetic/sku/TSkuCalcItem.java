package com.cw.arithmetic.sku;


import cn.hutool.core.util.StrUtil;
import com.cw.utils.ProdType;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Maps;
import com.google.common.collect.Table;

import java.util.*;


/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/9/15 17:31
 **/
public class TSkuCalcItem {
    private Date date;


    //private Table<String, String, Integer> orgMap = HashBasedTable.create();
    //private Table<String, String, Integer> currentMap = HashBasedTable.create();
    //private Table<String, String, Integer> updMap = HashBasedTable.create();


    private Map<String,Table<String, String, Integer>> orgMap= Maps.newHashMap();//每种产品类型. 不同渠道的原始数据
    private Map<String,Table<String, String, Integer>> currentMap= Maps.newHashMap();//每种产品类型. 不同渠道的当前数据
    private Map<String,Table<String, String, Integer>> updRoomMap= Maps.newHashMap(); //每种产品类型. 不同渠道的更新数据


    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public void fillOrgInfo(String channel, ToccItem toccItem) {
        if (channel.isEmpty() || toccItem == null) {
            return;
        }

        Map<String, Map<String, Integer>> prodOccMap = toccItem.getAllProdOccMap();
        for (Map.Entry<String, Map<String, Integer>> entry : prodOccMap.entrySet()) {
            String prodType = entry.getKey();
            Map<String, Integer> occMap = entry.getValue();
            for (Map.Entry<String, Integer> occEntry : occMap.entrySet()) {
                String skuid = occEntry.getKey();
                int occ = occEntry.getValue();
                if (orgMap.containsKey(prodType)) {
                    orgMap.get(prodType).put(channel, skuid, occ);
                } else {
                    Table<String, String, Integer> table = HashBasedTable.create();
                    table.put(channel, skuid, occ);
                    orgMap.put(prodType, table);
                }
            }
        }
        fillUpdInfo(channel, toccItem);
    }



    public void fillCurrentInfo(String channel, ToccItem toccItem) {
        if (channel.isEmpty() || toccItem == null) {
            return;
        }

        Map<String, Map<String, Integer>> prodOccMap = toccItem.getAllProdOccMap();
        for (Map.Entry<String, Map<String, Integer>> entry : prodOccMap.entrySet()) {
            String prodType = entry.getKey();
            Map<String, Integer> occMap = entry.getValue();
            for (Map.Entry<String, Integer> occEntry : occMap.entrySet()) {
                String skuid = occEntry.getKey();
                int occ = occEntry.getValue();
                if (currentMap.containsKey(prodType)) {
                    currentMap.get(prodType).put(channel, skuid, occ);
                } else {
                    Table<String, String, Integer> table = HashBasedTable.create();
                    table.put(channel, skuid, occ);
                    currentMap.put(prodType, table);
                }
            }
        }
        fillUpdInfo(channel, toccItem);
    }

    /**
     * 填充.拓充更新矩阵
     *
     * @param channel
     * @param toccItem
     */
    private void fillUpdInfo(String channel, ToccItem toccItem) {
        if (channel.isEmpty() || toccItem == null) {
            return;
        }
        Map<String, Map<String, Integer>> prodOccMap = toccItem.getAllProdOccMap();
        for (Map.Entry<String, Map<String, Integer>> entry : prodOccMap.entrySet()) {
            String prodType = entry.getKey();
            Map<String, Integer> occMap = entry.getValue();
            for (Map.Entry<String, Integer> occEntry : occMap.entrySet()) {
                String skuid = occEntry.getKey();
                int occ = occEntry.getValue();
                if (updRoomMap.containsKey(prodType)) {
                    updRoomMap.get(prodType).put(channel, skuid, occ);
                } else {
                    Table<String, String, Integer> table = HashBasedTable.create();
                    table.put(channel, skuid, occ);
                    updRoomMap.put(prodType, table);
                }
            }
        }
    }


    /**
     * 累加比较.有差异的.就存入updMap
     */
    public void sumPickupChange() {
        //要不要先把upd**Map的值都先全部设为0呢?
        ProdType[] types = ProdType.values();
        for (ProdType type : types) {
            Map<String, Map<String, Integer>> updmap = getUpdMap(type.val());
            for (Map.Entry<String, Map<String, Integer>> entry : updmap.entrySet()) {
                String channel = entry.getKey();
                Map<String, Integer> valmap = entry.getValue();
                for (String skuid : entry.getValue().keySet()) {
                    int changeNum = getUpdnum(channel, skuid, type.val());
                    valmap.put(skuid, changeNum);
                }
                Iterator<Map.Entry<String, Integer>> it = valmap.entrySet().iterator();
                while (it.hasNext()) {
                    Map.Entry<String, Integer> valentry = it.next();
                    if (valentry.getValue() == 0) {
                        it.remove();   //移除upd为0 的值. 0 的不需要做更新
                    }
                }
            }
        }
    }

    public List<String> getUpdInfo() {
        List<String> reult = new ArrayList<>();
        ProdType[] types = ProdType.values();
        for (ProdType type : types) {
            Map<String, Map<String, Integer>> updmap = getUpdMap(type.val());
            for (Map.Entry<String, Map<String, Integer>> entry : updmap.entrySet()) {
                String channel = entry.getKey();
                //Map<String, Integer> valmap = entry.getValue();
                for (String skuid : entry.getValue().keySet()) {
                    int changeNum = getUpdnum(channel, skuid, type.val());
                    reult.add(channel + "," + type.val() + "," + skuid + "," + changeNum);
                }
            }
        }
        return reult;
    }


    private int getUpdnum(String channel, String skuid, String type) {
        int orgnum = 0;
        int currnum = 0;

        Map<String, Map<String, Integer>> orgmap = getOrgMap(type);
        Map<String, Map<String, Integer>> currmap = getCurrMap(type);
        if (orgmap.containsKey(channel)) {
            orgnum = orgmap.get(channel).getOrDefault(skuid, 0);
        }
        if (currmap.containsKey(channel)) {
            currnum = currmap.get(channel).getOrDefault(skuid, 0);
        }

        return currnum - orgnum;
    }

    private Map<String, Map<String, Integer>> getOrgMap(String type) {
        if(orgMap.containsKey(type)) {
            return orgMap.get(type).rowMap();
        }else {
            return Maps.newHashMap();
        }
    }

    private Map<String, Map<String, Integer>> getCurrMap(String type) {
        if(currentMap.containsKey(type)) {
            return currentMap.get(type).rowMap();
        }else {
            return Maps.newHashMap();
        }
    }

    private Map<String, Map<String, Integer>> getUpdMap(String type) {
        if(updRoomMap.containsKey(type)) {
            return updRoomMap.get(type).rowMap();
        }else {
            return Maps.newHashMap();
        }
    }


}

package com.cw.arithmetic.sku;

import com.cw.core.CoreCache;
import com.cw.utils.CalculateDate;

import java.util.*;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2018/9/15 15:43
 **/
public class OpskuPickup {

    public static List<TSkuUpd> calcSkuPickup(TMultiRsdata olddata, TMultiRsdata newdata) {


        HashMap<Date, TSkuCalcItem> matrix = new HashMap<>();//运算矩阵

        HashMap<Date, ToccItem> olditems = olddata.getOccItems();
        HashMap<Date, ToccItem> curritems = newdata.getOccItems();

        //初始化运算矩阵
        if (!olddata.getChannel().isEmpty()) {  //填充修改前
            for (Map.Entry<Date, ToccItem> oldentry : olditems.entrySet()) {
                Date key = oldentry.getKey();
                matrix.putIfAbsent(key, new TSkuCalcItem());
                matrix.get(key).fillOrgInfo(olddata.getChannel(), oldentry.getValue());
            }
        }

        if (!newdata.getChannel().isEmpty() || true) {//填充修改后
            for (Map.Entry<Date, ToccItem> newentry : curritems.entrySet()) {
                Date key = newentry.getKey();
                matrix.putIfAbsent(key, new TSkuCalcItem());
                matrix.get(key).fillCurrentInfo(newdata.getChannel(), newentry.getValue());
            }
        }
        //前后比较得出结果
        for (Map.Entry<Date, TSkuCalcItem> calcItemEntry : matrix.entrySet()) {
            calcItemEntry.getValue().sumPickupChange();
        }
        List<TSkuUpd> updArrays = getPickupResult(matrix);
        return updArrays;
    }

    private static List<TSkuUpd> getPickupResult(HashMap<Date, TSkuCalcItem> matrix) {
        HashMap<String, List<Date>> datas = new HashMap<String, List<Date>>();//相同更新条件的.放在一个MAP里
        for (Map.Entry<Date, TSkuCalcItem> entry : matrix.entrySet()) {
            TSkuCalcItem calcItem = entry.getValue();
            Date d = entry.getKey();
            List<String> updinfo = calcItem.getUpdInfo();//反向推导每一天,每个渠道,每个房型或资源,要更新的数量. 生成更新语句
            for (String s : updinfo) {
                datas.putIfAbsent(s, new ArrayList<>());  // 更新信息. 渠道,产品类型.更新数量. 按  reult.add(channel+","+type+","+changeNum);
                datas.get(s).add(d);
            }
        }
        String channel;
        String prodType;
        String skuid;
        Integer num = 0;

        List<TSkuUpd> result = new ArrayList<>();
        for (Map.Entry<String, List<Date>> entry : datas.entrySet()) {
            String[] keys = entry.getKey().split(",");
            channel = keys[0];
            prodType = keys[1];
            skuid = keys[2];
            num = Integer.parseInt(keys[3]);

                List<Date[]> dates = CalculateDate.getDateStartEnd2(entry.getValue());
                for (Date[] range : dates) {
                    TSkuUpd skuUpd = new TSkuUpd();
                    skuUpd.setProdType(prodType);
                    skuUpd.setSkuid(skuid);
                    skuUpd.setChannel(channel);
                    skuUpd.setChangeNum(num);
                    skuUpd.setStartdate(range[0]);
                    skuUpd.setEnddate(range[1]);
                    result.add(skuUpd);
                }
            }
        return result;
    }

    public static List<String> getLockKeys(String projectId, List<TSkuUpd> skuUpds) {
        List<String> keys = new ArrayList<>();
        if (skuUpds != null) {
            for (TSkuUpd skuUpd : skuUpds) {
                if (CoreCache.isCacheProductType(skuUpd.getProdType())) {  //18 .09.18  JUST 目前只针对房进行锁. 其他的等库存放入缓存再说
                    keys.addAll(CoreCache.getGridSkuWriteKeys(projectId, skuUpd.getChannel(),
                            skuUpd.getProdType(), skuUpd.getSkuid(),
                            skuUpd.getStartdate(), skuUpd.getEnddate()));
                }
            }
        }
        return keys;
    }

    public static String getSkuMap(String projectId, TSkuUpd upd) {
        return CoreCache.getGridCacheKey(projectId, upd.getChannel(), upd.getProdType(), upd.getSkuid());
    }

    public static List<TSkuUpd> splitSkus(List<TSkuUpd> totals, String type) {
        List<TSkuUpd> skus = new ArrayList<>();
        for (TSkuUpd row : totals) {
            if (row.getProdType().equals(type)) {
                skus.add(row);
            }
        }
        return skus;
    }


}

package com.cw.arithmetic.sku;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 存放一段时间内一批产品的最小可用数.
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2020/1/6 08:56
 **/
@Data
public class SkuRpItem {
    Map<String, SkuItem> infoMap = new HashMap<>();

    public void putItem(SkuItem item) {
        this.infoMap.put(item.getSku(), item);
    }

    public SkuItem getItem(String sku) {
        return infoMap.getOrDefault(sku, new SkuItem(sku, null, 0, 0));
    }


}

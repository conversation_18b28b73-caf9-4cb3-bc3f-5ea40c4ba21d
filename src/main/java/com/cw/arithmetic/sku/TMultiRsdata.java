package com.cw.arithmetic.sku;

import cn.hutool.core.util.StrUtil;
import com.cw.entity.Reservation;
import com.cw.utils.CalculateDate;
import com.cw.utils.ProdType;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * 针对一个list 的订单做处理 .
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2018/9/15 10:31
 **/
public class TMultiRsdata {

    private boolean emp = false; //空数组 定义.  用来方便的直接加减.例如新建时. 或者取消订单时

    private String channel = "";

    private Table<String, Class<?>, List<?>> prodTable = HashBasedTable.create();

    //************后续如果需要增加其他订单的缓存. 就从这里添加***********/

    private HashMap<Date, ToccItem> items; //存放每个订单的占用


    /**
     * @param emp     新建时第一次传true
     * @param channel 当emp传false时. channel传实际要扣减的渠道
     */
    public TMultiRsdata(boolean emp, String channel) {
        items = new HashMap<Date, ToccItem>();
        if (!emp) {
            this.channel = channel;
        }
    }

    public HashMap<Date, ToccItem> getOccItems() {
        return items;
    }


    @SuppressWarnings("unchecked")
    public <T>  void fillArray(String prodType,Class<T> clazz,List<T> list) {
        for (T t : list) {
            if(prodType.equals(ProdType.ROOM.val())){
                Reservation rs = (Reservation) t;
                int count = CalculateDate.compareDates(rs.getDepartureDate(), rs.getArrivalDate()).intValue();
                for (int i = 0; i < count; i++) {
                    Date d = CalculateDate.reckonDay(rs.getArrivalDate(), 5, i);
                    updDateItem(d, ProdType.ROOM.val(), rs.getRoomType(), rs.getRooms());
                }
            }
            //后续有其他类型的库存产品. 就在这里添加.
        }
    }

    private void updDateItem(Date d, String productType, String code, int num) {
        items.putIfAbsent(d, new ToccItem());
        items.get(d).updOcc(productType, code, num);
    }


    public String getChannel() {
        return channel;
    }


    @SuppressWarnings("unchecked")
    private  <T> List<T> getCalcList(String key, Class<T> clazz) {
        return (List<T>) prodTable.get(key, clazz);
    }

}

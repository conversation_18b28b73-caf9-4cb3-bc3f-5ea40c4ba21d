package com.cw.arithmetic.sku;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.fasterxml.jackson.dataformat.yaml.YAMLGenerator;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/4/24 16:24
 **/
public class YamlTool {
    public static YAMLFactory f;
    public static ObjectMapper yamlMapper;

    static {
        f = new YAMLFactory();
        f.configure(YAMLGenerator.Feature.MINIMIZE_QUOTES, true);
        yamlMapper = new ObjectMapper(f);
        yamlMapper.setSerializationInclusion(JsonInclude.Include.NON_EMPTY);//空串不会进行序列化
        yamlMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);//忽略未知属性,BEAN没有的属性,YAML 写多了也不会报错

    }

    public static String writeValueAsString(Object obj) {
        try {
            return yamlMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            e.printStackTrace();

        }
        return StrUtil.EMPTY;
    }

    public static <T> T readValue(String content, Class<T> valueType) {
        try {
            return yamlMapper.readValue(content, valueType);
        } catch (JsonProcessingException e) {
            e.printStackTrace();

        }
        return null;
    }


}

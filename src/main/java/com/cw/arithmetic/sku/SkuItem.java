package com.cw.arithmetic.sku;

import lombok.Data;

import java.util.Date;

/**
 * 描述一个产品每天 .总数多少.剩余多少
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/12/16 09:56
 **/
@Data
public class SkuItem {
    String sku = "";
    String period = ""; //暂时没用.对应的是餐段
    Date date = null;  //用来填充一段时间的最小库存时.这个就是空的.
    Integer total = 0;
    Integer avl = 0;
    Integer overbook = 0; //可超预订的数字..留给后面的功能用.比如说.A 房型可以超预订3间

    public SkuItem(String sku, Date date, Integer total, Integer avl) {
        this.sku = sku;
        this.date = date;
        this.total = total;
        this.avl = avl;
    }
}

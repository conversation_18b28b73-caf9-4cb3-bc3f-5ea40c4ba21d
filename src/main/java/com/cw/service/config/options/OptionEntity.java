package com.cw.service.config.options;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

/**
 * <AUTHOR>
 * @date 2025/7/11
 **/
@Data
public class OptionEntity {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "开关组")
    private String group = "";/* 开关组 */

    @ApiModelProperty(value = "选项代码")
    private String option = "";/* 选项代码 */

    @ApiModelProperty(value = "选项描述")
    private String desc = "";/* 选项描述(中文) */

    @ApiModelProperty(value = "开关状态")
    private Boolean switchstatus = false;/* 开关状态 */

    @ApiModelProperty(value = "参数值")
    private String val = "";

    @ApiModelProperty(value ="同组序号")
    private int sortIndex = 0;
}

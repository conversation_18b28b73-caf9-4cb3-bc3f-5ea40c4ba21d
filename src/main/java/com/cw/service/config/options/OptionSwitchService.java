package com.cw.service.config.options;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.cw.cache.GlobalCache;
import com.cw.cache.OptionSwitchTool;
import com.cw.config.exception.BizException;
import com.cw.core.SeqNoService;
import com.cw.entity.Optionswitch;
import com.cw.entity.Saler;
import com.cw.pojo.dto.pms.req.option.OptionReq;
import com.cw.pojo.dto.pms.req.others.SalerEntity;
import com.cw.service.context.GlobalContext;
import com.cw.utils.enums.GlobalDataType;

import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2025/7/10 17:10
 **/
public interface OptionSwitchService {

    public OptionEntity save(OptionEntity entity);

    List<Optionswitch> getOptionData(OptionReq req);
}

package com.cw.service.config.pkgdet.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.cw.config.exception.BizException;
import com.cw.entity.Pkgdet;
import com.cw.mapper.PkgdetMapper;
import com.cw.pojo.dto.pms.req.pkgdet.PkgdetReq;
import com.cw.pojo.dto.pms.req.pkgdet.QueryPkgdetReq;
import com.cw.pojo.dto.pms.req.pkgdet.UpdatePkgdetReq;
import com.cw.pojo.dto.pms.res.pkgdet.PkgdetListRes;
import com.cw.pojo.dto.pms.res.pkgdet.PkgdetRes;
import com.cw.service.config.pkgdet.PkgdetService;
import com.cw.service.context.GlobalContext;
import com.cw.utils.CalculateDate;
import com.cw.utils.JpaUtil;
import com.cw.utils.pages.PageResUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 包价详情业务逻辑实现类
 * <AUTHOR>
 * @date 2024-07-30
 */
@Service
@Transactional
public class PkgdetServiceImpl implements PkgdetService {

    private static final Logger logger = LoggerFactory.getLogger(PkgdetServiceImpl.class);

    @Resource
    private PkgdetMapper pkgdetMapper;

    @Override
    public void addPkgdet(PkgdetReq req) {
        String hotelId = GlobalContext.getCurrentHotelId();
        
        // 批量检查重复，减少重复查询
        List<String> duplicateRoomTypes = new ArrayList<>();
        
        for (String roomtype : req.getRoomtypes()) {
            // 检查是否存在重复记录
            int count = pkgdetMapper.countByCodeAndIdNotAndRoomtypeAndHotelIdAndTimeOverlap(
                    req.getRatecode(), 0L, roomtype, hotelId, req.getStartTime(), req.getEndtime());
            
            if (count > 0) {
                duplicateRoomTypes.add(roomtype);
            }
        }
        
        // 如果有重复的房型，抛出异常
        if (!duplicateRoomTypes.isEmpty()) {
            throw new BizException(StrUtil.format("房型{}在该{} - {}时段，已经配置有重复的包价详情。请检查后重新添加",
                    String.join(",", duplicateRoomTypes),
                    CalculateDate.dateToString(req.getStartTime()), 
                    CalculateDate.dateToString(req.getEndtime())));
        }
        
        // 批量创建包价详情记录
        List<Pkgdet> pkgdetList = new ArrayList<>();
        for (String roomtype : req.getRoomtypes()) {
            Pkgdet pkgdet = new Pkgdet();
            BeanUtil.copyProperties(req, pkgdet);
            pkgdet.setHotelId(hotelId);
            pkgdet.setRoomtype(roomtype);
            pkgdetList.add(JpaUtil.appendEntity(pkgdet));
        }
        
        // 批量保存
        pkgdetMapper.saveAll(pkgdetList);
        
        logger.info("成功添加包价详情记录{}条，房价代码：{}，房型：{}", 
                pkgdetList.size(), req.getRatecode(), String.join(",", req.getRoomtypes()));
    }

    @Override
    public void updatePkgdet(UpdatePkgdetReq req) {
        String hotelId = GlobalContext.getCurrentHotelId();
        
        // 检查记录是否存在
        Optional<Pkgdet> optionalPkgdet = pkgdetMapper.findById(req.getId());
        if (!optionalPkgdet.isPresent()) {
            throw new BizException("包价详情记录不存在");
        }
        
        // 检查是否存在重复记录（排除自身）
        int count = pkgdetMapper.countByCodeAndIdNotAndRoomtypeAndHotelIdAndTimeOverlap(
                req.getRatecode(), req.getId(), req.getRoomtype(), hotelId, 
                req.getStartTime(), req.getEndtime());
        
        if (count > 0) {
            throw new BizException(StrUtil.format("该房型{}在该{} - {}时段，已经配置有重复的包价详情。请使用其他时间段", 
                    req.getRoomtype(),
                    CalculateDate.dateToString(req.getStartTime()), 
                    CalculateDate.dateToString(req.getEndtime())));
        }
        
        Pkgdet pkgdet = optionalPkgdet.get();
        BeanUtil.copyProperties(req, pkgdet);
        pkgdet.setHotelId(hotelId);
        pkgdetMapper.save(JpaUtil.appendEntity(pkgdet));
        
        logger.info("成功修改包价详情记录，ID：{}，房价代码：{}，房型：{}", 
                req.getId(), req.getRatecode(), req.getRoomtype());
    }

    @Override
    public void deletePkgdet(Long id) {
        // 检查记录是否存在
        Optional<Pkgdet> optionalPkgdet = pkgdetMapper.findById(id);
        if (!optionalPkgdet.isPresent()) {
            throw new BizException("包价详情记录不存在");
        }
        
        pkgdetMapper.deleteById(id);
        logger.info("成功删除包价详情记录，ID：{}", id);
    }

    @Override
    public PkgdetListRes listPkgdet(QueryPkgdetReq req) {
        String hotelId = GlobalContext.getCurrentHotelId();
        int currentPage = req.getPages().getQueryStartPage();
        int pageSize = req.getPages().getPagesize();

        // 使用JPA Criteria API构建查询条件
        Page<Pkgdet> pkgdetPage = pkgdetMapper.findAll((Root<Pkgdet> root, CriteriaQuery<?> query, CriteriaBuilder cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 酒店ID条件（必须）
            predicates.add(cb.equal(root.get("hotelId"), hotelId));

            // 房价代码模糊查询
            if (StrUtil.isNotBlank(req.getRatecode())) {
                predicates.add(cb.like(root.get("ratecode"), "%" + req.getRatecode() + "%"));
            }

            // 房型代码模糊查询
            if (StrUtil.isNotBlank(req.getRoomtype())) {
                predicates.add(cb.like(root.get("roomtype"), "%" + req.getRoomtype() + "%"));
            }

            // 开始时间条件
            if (req.getStartTime() != null) {
                predicates.add(cb.greaterThanOrEqualTo(root.get("startTime"), req.getStartTime()));
            }

            // 结束时间条件
            if (req.getEndTime() != null) {
                predicates.add(cb.lessThanOrEqualTo(root.get("endtime"), req.getEndTime()));
            }

            // 包价代码模糊查询
            if (StrUtil.isNotBlank(req.getIncludeCode())) {
                predicates.add(cb.like(root.get("includeCode"), "%" + req.getIncludeCode() + "%"));
            }

            // 有效时间查询（时间范围内的记录）
            if (req.getValidTime() != null) {
                predicates.add(cb.lessThanOrEqualTo(root.get("startTime"), req.getValidTime()));
                predicates.add(cb.greaterThanOrEqualTo(root.get("endtime"), req.getValidTime()));
            }

            // 将所有条件组合为AND关系
            Predicate[] predicateArray = new Predicate[predicates.size()];
            query.where(cb.and(predicates.toArray(predicateArray)));
            return query.getRestriction();
        }, PageRequest.of(currentPage, pageSize, Sort.by(req.getPages().getDirection(),
                req.getPages().getSortname().split(","))));

        // 转换为响应对象
        PkgdetListRes res = new PkgdetListRes();
        res.fillPageData(pkgdetPage);
        return res;
    }

    @Override
    public PkgdetRes findPkgdet(Long id) {
        Optional<Pkgdet> optionalPkgdet = pkgdetMapper.findById(id);
        if (!optionalPkgdet.isPresent()) {
            throw new BizException("包价详情记录不存在");
        }
        
        Pkgdet pkgdet = optionalPkgdet.get();
        PkgdetRes pkgdetRes = new PkgdetRes();
        BeanUtil.copyProperties(pkgdet, pkgdetRes);
        
        return pkgdetRes;
    }
}

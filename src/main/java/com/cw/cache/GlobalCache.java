package com.cw.cache;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.cw.cache.redisimpl.redisimpl.RedisGlobalDataCache;
import com.cw.mapper.common.DaoLocal;
import com.cw.pojo.upload.constants.UploadType;

import com.cw.pojo.upload.pojo.PmsRatedetSyncData;
import com.cw.pojo.upload.pojo.PmsReservationSyncData;
import com.cw.service.mq.MsgNotifyer;
import com.cw.service.mq.msgmodel.ConfigUpdEventModel;
import com.cw.utils.SpringUtil;

import com.cw.utils.enums.GlobalDataType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2018/5/25 14:52
 **/
@Slf4j
@Service
public class GlobalCache extends BaseGlobalCache {

    static GlobalCache globalCache;

    @Autowired
    public GlobalCache(DataStructure dataStructure) {
        init(dataStructure);
        globalCache = this;
    }

    public static DataStructure getDataStructure() {
        return (DataStructure) memoryHT.get(KEY__DATASTRUTURE);
    }

    public static GlobalCache getInstance() {
        return globalCache;
    }


    @SafeVarargs
    public final synchronized <T> void refreshAndNotify(GlobalDataType type, String hotelId, T... t) {
        //广播消息.发送数据

        MsgNotifyer notifyer = SpringUtil.getBean(MsgNotifyer.class);
        //发送通知者负责先刷新内存值
        writeNewValToRedis(type, hotelId, t);
        String refreshKey = null;
        if (t != null && t.length > 0) {
            BaseCacheData<T> cacheData = dataStructure.getCache(type);//.getUniqueKey(t);
            for (T r : t) {
                refreshKey = cacheData.getUniqueKey(r);
                notifyer.sendGlobalNotify(new ConfigUpdEventModel(type, hotelId, refreshKey));
            }
        } else {
            notifyer.sendGlobalNotify(new ConfigUpdEventModel(type, hotelId, null));
        }


    }

    /**
     * 删除记录.刷新内存
     *
     * @param type
     * @param hotelId
     * @param t
     * @param <T>
     */
    @SafeVarargs
    public final synchronized <T> void deleteAndNotify(GlobalDataType type, String hotelId, T... t) {//TODO  后台删除数据的地方要加上
        MsgNotifyer notifyer = SpringUtil.getBean(MsgNotifyer.class);
        //发送通知者负责先刷新内存值
        if (t != null && t.length == 1) { //删除时传了指定对象
            removeOldValToRedis(type, hotelId, t[0]);
        }
        String refreshKey = null;
        if (t != null) {
            BaseCacheData<T> cacheData = dataStructure.getCache(type);//.getUniqueKey(t);
            for (T r : t) {
                refreshKey = cacheData.getUniqueKey(r);
                notifyer.sendGlobalNotify(new ConfigUpdEventModel(type, hotelId, refreshKey));
            }
        } else {
            notifyer.sendGlobalNotify(new ConfigUpdEventModel(type, hotelId, null));
        }
    }


    /**
     * 67
     * 将数据库单酒店数据在数据库查询之后.写入到redis
     *
     * @param type
     * @param hotelId
     * @param t
     * @param <T>
     */
    @SafeVarargs
    private final <T> void writeNewValToRedis(GlobalDataType type, String hotelId, T... t) {
        if (t != null && t.length == 1) {//刷新单条记录
            RedisGlobalDataCache.updRecords(type, t);
            BaseCacheData<T> baseCacheData = GlobalCache.getDataStructure().getCache(type);
            log.info("刷新redis 单条记录 {} ID: {}", type, baseCacheData.getRediskey(t[0]));
        } else if (StrUtil.isNotBlank(hotelId)) {
            //NULL 更新对象为NULL 先将内存中的跟DB做比较.DB中没有的就做删除
            BaseCacheData cacheData = dataStructure.getCache(type);
            List<?> dbdata = null;
            DaoLocal<?> daoLocal = SpringUtil.getBean(DaoLocal.class);
            if (cacheData == null || cacheData.getCacheTableClass() == null) {
                return;   //如果没有配置缓存表类,则不刷新
            }
            dbdata = daoLocal.getHotelIdObjectList(cacheData.getCacheTableClass(), hotelId);

            //先清理redis中的数据
            RedisGlobalDataCache.cleanBeforeUpd(hotelId, type, dbdata);

            //再将数据写入redis
            if (CollectionUtil.isNotEmpty(dbdata)) {
                RedisGlobalDataCache.updRecords(type, dbdata.toArray());
            }
            log.info("刷新redis 批量记录 {} hotelid: {}", type, hotelId);
        }

    }

    private <T> void removeOldValToRedis(GlobalDataType type, String hotelId, T... t) {
        RedisGlobalDataCache.removeRecords(hotelId, type, t);
    }


}
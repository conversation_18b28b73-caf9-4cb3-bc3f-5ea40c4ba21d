package com.cw.cache;

import cn.hutool.core.lang.ClassScanner;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.cw.pojo.dto.common.pagedata.SelectDataNode;
import com.cw.utils.SystemUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 数据填充,翻译处理器
 * 根据 customdatakey 找到对应的处理器.返回对应的数据
 * 例如常常需要获取房型下拉框内容.房型代码转成中文
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/12/4 10:45
 **/
@Service
@Slf4j
public class CustomData {
    private static CustomData factory;
    private ConcurrentMap<SystemUtil.CustomDataKey, BaseCustomHandler> handlerMap = new ConcurrentHashMap<>();

    /**
     * 根据 code 返回这个code 对应的描述
     *
     * @param code
     * @param key
     * @return
     */
    public static String getDesc(String hotelId, String code, SystemUtil.CustomDataKey key, String... defaultValue) {
        BaseCustomHandler handler = factory.handlerMap.get(key);
        if (handler != null && StrUtil.isNotBlank(code)) {
            String transResult = handler.getTableTrans(hotelId, code);
            if (transResult.equals(code)) {
                return defaultValue != null && defaultValue.length > 0 ? defaultValue[0] : code;//如果上层要求返回空串.那就返回空串
            }
            return transResult;
        }
        return code;
    }

    public static CustomData getFactory() {
        return factory;
    }

    /**
     * 通用的表格 code 转描述方法
     *
     * @param datas
     * @param calcinfo 字段对应的 customdatakey 处理类
     * @return
     */
    public static <T> List<T> transTableData(String hotelId, List<T> datas, Map<String, SystemUtil.CustomDataKey> calcinfo, boolean lConcatParse) {

        return transTableData(hotelId, datas, calcinfo, null, lConcatParse);
    }

    public static <T> List<T> transTableData(String hotelId, List<T> datas, Map<String, SystemUtil.CustomDataKey> calcinfo, Map<String, String> mappings, boolean lConcatParse) {
        if (datas == null || datas.isEmpty()) {
            return datas;
        }
        for (T t : datas) {
            for (String transcol : calcinfo.keySet()) {
                Object orgval = ReflectUtil.getFieldValue(t, transcol);
                // 初始
                String attrName = transcol;
                if (MapUtil.isNotEmpty(mappings)) {
                    // 需要重新映射赋值的字段
                    String mapStr = MapUtil.getStr(mappings, transcol);
                    if(StrUtil.isNotEmpty(mapStr)){
                        attrName = mapStr;;
                    }
                }
                if (orgval != null) {   //如果查询出的列有需要转换显示的列
                    SystemUtil.CustomDataKey key = calcinfo.get(transcol);
                    if (lConcatParse) {
                        String[] vals = String.valueOf(orgval).split(",");
                        String updColValue = "";
                        for (String val : vals) {
                            String desc = getDesc(hotelId, val, key);
                            updColValue += updColValue.isEmpty() ? desc : "," + desc;
                        }
                        ReflectUtil.setFieldValue(t, attrName, updColValue);
                    } else {
                        if (orgval instanceof String) {
                            ReflectUtil.setFieldValue(t, attrName, getDesc(hotelId, (String) orgval, key));
                        }
                    }
                }
            }
        }
        return datas;
    }


    @PostConstruct
    public void initAdpter() {
        String cacheUnitPath = "com.cw.cache.customs";
        //根据 包的路径.将请求类加载到map 缓存
        ClassScanner.scanPackageByAnnotation(cacheUnitPath, CustomDataProp.class).forEach(clazz -> {
            SystemUtil.CustomDataKey dataKey = clazz.getAnnotation(CustomDataProp.class).dataType();
            try {
                handlerMap.put(dataKey, (BaseCustomHandler) clazz.newInstance());
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        log.info("{} customdata 下拉框数据组装 工厂初始化完毕!!!", handlerMap.values().size());
        factory = this;
    }

    public List<SelectDataNode> getSelectData(String hotelId, SystemUtil.CustomDataKey key, String... param) {
        if (!handlerMap.containsKey(key)) {
            return new ArrayList<>();
        } else {
            return handlerMap.get(key).getSelectData(hotelId, param);
        }
    }

    public BaseCustomHandler getHandler(SystemUtil.CustomDataKey key) {//返回处理器方便获取处理器方法
        if (handlerMap.containsKey(key)) {
            return handlerMap.get(key);
        }
        return null;
    }


    public static <T> List<T> transTableData2(String hotelId, List<T> datas, List<CtableTransInfo> transInfos, String concatChar) {
        if (datas == null || datas.isEmpty()) {
            return datas;
        }
        for (T t : datas) {


            for (CtableTransInfo transInfo : transInfos) {
                Object orgval = ReflectUtil.getFieldValue(t, transInfo.getOrgCol());
                if (orgval != null) {   //如果查询出的列有需要转换显示的列
                    SystemUtil.CustomDataKey key = transInfo.getKey();// calcinfo.get(transcol);

                    String transcol = transInfo.getTargetCol();
                    if (orgval instanceof String) {
                        String desc = getDesc(hotelId, (String) orgval, key);
                        String newValue = transInfo.isLneedCode() ? orgval + concatChar + desc : desc;
                        ReflectUtil.setFieldValue(t, transcol, newValue);
                    }
                }
            }
        }
        return datas;
    }

}

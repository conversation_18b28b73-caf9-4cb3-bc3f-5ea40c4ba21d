package com.cw.cache;


import com.cw.utils.SystemUtil;
import com.cw.utils.enums.GlobalDataType;
import lombok.Data;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by flyhigh on 2016/7/10.
 */
@Data
@Component
public class DataStructure extends BaseDataStructure {
    private ConcurrentHashMap<GlobalDataType, BaseCacheData> cacheMap = new ConcurrentHashMap<>();

    /**
     * 初始化扫描..实例化.添加注解缓存类
     */
    @PostConstruct
    public void initAdpter() {
        scanPackage("com.cw.cache.impl");
    }


}

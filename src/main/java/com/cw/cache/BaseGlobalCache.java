package com.cw.cache;


import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2020/10/26 16:13
 **/
public class BaseGlobalCache {
    protected static final String KEY__DATASTRUTURE = "DataAttribute";

    public static ConcurrentHashMap<String, Object> memoryHT = new ConcurrentHashMap<String, Object>();

    protected BaseDataStructure dataStructure;

    protected void init(BaseDataStructure dataStructure) {
        this.dataStructure = dataStructure;
        memoryHT.put(KEY__DATASTRUTURE, this.dataStructure);
    }

}

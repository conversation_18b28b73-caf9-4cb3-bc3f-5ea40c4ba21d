package com.cw.cache;

import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/9/6 12:39
 **/
@Component
public class RedisTool {

    private static RedisTemplate redisTemplate;

    private static RedissonClient redissonClient;

    @Autowired
    public RedisTool(RedisTemplate redisTemplate, RedissonClient redissonClient) {
        RedisTool.redisTemplate = redisTemplate;
        RedisTool.redissonClient = redissonClient;
    }

    public static RedisTemplate getInstance() {
        return redisTemplate;
    }

    public static RedissonClient getRedissonClient() {
        return redissonClient;
    }

}

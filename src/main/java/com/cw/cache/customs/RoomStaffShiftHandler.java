package com.cw.cache.customs;

import cn.hutool.core.util.EnumUtil;
import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.pojo.dto.common.pagedata.SelectDataNode;
import com.cw.utils.SystemUtil;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/16
 **/
@CustomDataProp(dataType = SystemUtil.CustomDataKey.roomstaffshift)
public class RoomStaffShiftHandler extends BaseCustomHandler {
    @Override
    public String trans(String hotelId, String key) {
//        RoomRateCache cache = GlobalCache.getDataStructure().getCache(GlobalDataType.ROOMRATE);
//        RoomRate roomRate = cache.getRecord(hotelId, key);
//        if (roomRate != null) {
//            return roomRate.getDescription();
//        }
        return EnumUtil.fromString(SystemUtil.StaffShiftStatus.class, key, SystemUtil.StaffShiftStatus.ACTIVE).getStatusDesc();
    }

    @Override
    public List<SelectDataNode> getSelectData(String hotelId, String... param) {

        List<SystemUtil.StaffShiftStatus> list = Arrays.asList(SystemUtil.StaffShiftStatus.values());
        //重新组装数据
        List<SelectDataNode> selectList = list.stream().map(
                row -> {
                    SelectDataNode node = new SelectDataNode();
                    node.setCode(row.name());
                    node.setDesc(row.getStatusDesc());
                    return node;
                }
        ).collect(Collectors.toList());
        return selectList;
    }
}
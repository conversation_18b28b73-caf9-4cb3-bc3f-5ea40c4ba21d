package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.IncludeRateCache;
import com.cw.entity.IncludeRate;
import com.cw.pojo.dto.common.pagedata.SelectDataNode;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.GlobalDataType;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2024-04-02
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.includerate)
public class IncludeRateDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String hotelId, String key) {
        IncludeRateCache cache = GlobalCache.getDataStructure().getCache(GlobalDataType.INCLUDERATE);
        IncludeRate includeRate = cache.getRecord(hotelId, key);
        if (includeRate != null) {
            return includeRate.getDescription();
        }
        return key;
    }

    @Override
    public List<SelectDataNode> getSelectData(String hotelId, String... param) {
        List<IncludeRate> list = GlobalCache.getDataStructure().getCache(GlobalDataType.INCLUDERATE).getDataList(hotelId);
        //重新组装数据
        List<SelectDataNode> selectList = list.stream().map(
                row -> {
                    SelectDataNode node = new SelectDataNode();
                    node.setCode(row.getCode());
                    node.setDesc(row.getDescription());
                    return node;
                }
        ).collect(Collectors.toList());
        return selectList;
    }
}

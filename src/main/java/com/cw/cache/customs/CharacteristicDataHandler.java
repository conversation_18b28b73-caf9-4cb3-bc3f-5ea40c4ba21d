package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.CharacteristicCache;
import com.cw.entity.Characteristic;
import com.cw.pojo.dto.common.pagedata.SelectDataNode;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.GlobalDataType;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Describe 房间特性
 * <AUTHOR> <PERSON>
 * @Create on 2024-04-03
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.characteristic)
public class CharacteristicDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String hotelId, String key) {
        CharacteristicCache cache = GlobalCache.getDataStructure().getCache(GlobalDataType.CHARACTERISTIC);
        Characteristic characteristic = cache.getRecord(hotelId, key);
        if (characteristic != null) {
            return characteristic.getCharacterName();
        }
        return key;
    }

    @Override
    public List<SelectDataNode> getSelectData(String hotelId, String... param) {
        List<Characteristic> list = GlobalCache.getDataStructure().getCache(GlobalDataType.CHARACTERISTIC).getDataList(hotelId);
        //重新组装数据
        List<SelectDataNode> selectList = list.stream().map(
                row -> {
                    SelectDataNode node = new SelectDataNode();
                    node.setCode(row.getCharacterCode());
                    node.setDesc(row.getCharacterName());
                    return node;
                }
        ).collect(Collectors.toList());
        return selectList;
    }
}

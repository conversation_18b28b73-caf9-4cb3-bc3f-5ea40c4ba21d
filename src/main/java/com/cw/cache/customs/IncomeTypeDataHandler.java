package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.pojo.dto.common.pagedata.SelectDataNode;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.IncomeTypeEnum;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe 收入组
 * <AUTHOR> <PERSON>
 * @Create on 2024-04-03
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.incometype)
public class IncomeTypeDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String hotelId, String key) {
        IncomeTypeEnum[] values = IncomeTypeEnum.values();
        for (IncomeTypeEnum incomeTypeEnum : values) {
            if (key.equals(incomeTypeEnum.getCode())) {
                return incomeTypeEnum.getDescription();
            }
        }
        return key;
    }

    @Override
    public List<SelectDataNode> getSelectData(String hotelId, String... param) {
        IncomeTypeEnum[] values = IncomeTypeEnum.values();
        List<SelectDataNode> list = new ArrayList<>();
        for (IncomeTypeEnum incomeTypeEnum : values) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(incomeTypeEnum.getCode());
            node.setDesc(incomeTypeEnum.getDescription());
            list.add(node);
        }

        return list;
    }
}

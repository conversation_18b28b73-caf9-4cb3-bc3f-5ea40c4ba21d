package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.AccountItemCache;
import com.cw.cache.impl.ArAccountCache;
import com.cw.entity.AccountItem;
import com.cw.entity.ArAccount;
import com.cw.pojo.dto.common.pagedata.SelectDataNode;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.GlobalDataType;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 应收选项过滤器
 *
 * @Describe
 * <AUTHOR> Just
 * @Create on 2024-04-02
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.araccount)
public class ArAccountDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String hotelId, String key) {
        ArAccountCache cache = GlobalCache.getDataStructure().getCache(GlobalDataType.ARACCOUNT);
        ArAccount accountItem = cache.getRecord(hotelId, key);
        if (accountItem != null) {
            return accountItem.getDescription();
        }
        return key;
    }

    @Override
    public List<SelectDataNode> getSelectData(String hotelId, String... param) {
        List<ArAccount> list = GlobalCache.getDataStructure().getCache(GlobalDataType.ARACCOUNT).getDataList(hotelId);
        //重新组装数据
        List<SelectDataNode> selectList = list.stream().filter(r -> r.getActive()).map(
                row -> {
                    SelectDataNode node = new SelectDataNode();
                    node.setCode(row.getCode());
                    node.setDesc(row.getDescription());
                    return node;
                }
        ).collect(Collectors.toList());
        return selectList;
    }
}

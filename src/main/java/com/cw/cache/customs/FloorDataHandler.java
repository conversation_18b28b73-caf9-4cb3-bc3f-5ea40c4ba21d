package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.FloorCache;
import com.cw.cache.impl.MarketCache;
import com.cw.entity.Floor;
import com.cw.entity.Market;
import com.cw.entity.Saler;
import com.cw.pojo.dto.common.pagedata.SelectDataNode;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.GlobalDataType;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Describe 楼层
 * <AUTHOR> Just
 * @Create on 2024-04-03
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.floor)
public class FloorDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String hotelId, String key) {
        FloorCache cache = GlobalCache.getDataStructure().getCache(GlobalDataType.FLOOR);
        Floor floor = cache.getRecord(hotelId, key);
        if (floor != null) {
            return floor.getDescription();
        }
        return key;
    }

    @Override
    public List<SelectDataNode> getSelectData(String hotelId, String... param) {
        List<Floor> list = GlobalCache.getDataStructure().getCache(GlobalDataType.FLOOR).getDataList(hotelId);
        //重新组装数据
        List<SelectDataNode> selectList = list.stream().map(
                row -> {
                    SelectDataNode node = new SelectDataNode();
                    node.setCode(row.getCode() + "");
                    node.setDesc(row.getDescription());
                    return node;
                }
        ).collect(Collectors.toList());
        return selectList;
    }
}

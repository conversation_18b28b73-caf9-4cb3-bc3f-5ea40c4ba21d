package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.HotelCache;
import com.cw.entity.Hotel;
import com.cw.pojo.dto.common.pagedata.SelectDataNode;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.GlobalDataType;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2024-04-03
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.hotel)
public class HotelDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String hotelId, String key) {
        HotelCache cache = GlobalCache.getDataStructure().getCache(GlobalDataType.HOTEL);
        Hotel hotel = cache.getRecord(hotelId, key);
        if (hotel != null) {
            return hotel.getDescription();
        }
        return key;
    }

    @Override
    public List<SelectDataNode> getSelectData(String hotelId, String... param) {
        //先直接查 后续用缓存
        //先直接查 后续用缓存
        List<Hotel> list = GlobalCache.getDataStructure().getCache(GlobalDataType.HOTEL).getDataList(hotelId);
        //重新组装数据
        List<SelectDataNode> selectList = list.stream().map(
                row -> {
                    SelectDataNode node = new SelectDataNode();
                    node.setCode(row.getHotelId());
                    node.setDesc(row.getDescription());
                    return node;
                }
        ).collect(Collectors.toList());
        return selectList;
    }
}

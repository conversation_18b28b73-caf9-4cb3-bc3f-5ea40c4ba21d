package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.FactorCache;
import com.cw.entity.Factor;
import com.cw.pojo.dto.common.pagedata.SelectDataNode;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.GlobalDataType;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/9/23 0023
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.factor)
public class FactorDataHandler extends BaseCustomHandler {
    /**
     * 子类重载修改
     */
    protected SystemUtil.FactoryType factoryType;


    @Override
    public String trans(String hotelId, String key) {
        FactorCache cache = GlobalCache.getDataStructure().getCache(GlobalDataType.FACTOR);
        Factor record = cache.getRecord(hotelId, key);
        return record == null ? key : record.getDescription();
    }


    @Override
    public List<SelectDataNode> getSelectData(String hotelId, String... param) {
        FactorCache cache = GlobalCache.getDataStructure().getCache(GlobalDataType.FACTOR);
        //取分组数据
        List<Factor> list = cache.getGroupList(hotelId, factoryType.name());
        String header = param != null && param.length > 0 && param[0] != null ? param[0] : "";
        List<SelectDataNode> selectList = new ArrayList<>();
        for (Factor factor : list) {
            //启动状态下拉框
            if (header.isEmpty() || factor.getHeader().equalsIgnoreCase(header) && factor.getStatus() == 1) {
                SelectDataNode node = new SelectDataNode();
                node.setCode(factor.getCode());
                node.setDesc(factor.getDescription());
                node.setGroup(factor.getHeader());
                selectList.add(node);
            }
        }
        return selectList;
    }

    public String getHeaderByCode(String hotelId, String code) {
        FactorCache cache = GlobalCache.getDataStructure().getCache(GlobalDataType.FACTOR);
        Factor factor = cache.getRecord(hotelId, code);
        if (factor != null) {
            return factor.getHeader();
        }
        return "";
    }
}

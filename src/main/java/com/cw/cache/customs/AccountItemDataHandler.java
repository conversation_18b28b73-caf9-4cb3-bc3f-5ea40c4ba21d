package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.AccountItemCache;
import com.cw.entity.AccountItem;
import com.cw.pojo.dto.common.pagedata.SelectDataNode;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.GlobalDataType;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2024-04-02
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.accountitem)
public class AccountItemDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String hotelId, String key) {
        AccountItemCache cache = GlobalCache.getDataStructure().getCache(GlobalDataType.ACCOUNTITEM);
        AccountItem accountItem = cache.getRecord(hotelId, key);
        if (accountItem != null) {
            return accountItem.getDescription();
        }
        return key;
    }

    @Override
    public List<SelectDataNode> getSelectData(String hotelId, String... param) {
        //先直接查 后续用缓存
        //AccountItemMapper mapper = SpringUtil.getBean(AccountItemMapper.class);
        List<AccountItem> list = GlobalCache.getDataStructure().getCache(GlobalDataType.ACCOUNTITEM).getDataList(hotelId);
        //重新组装数据
        List<SelectDataNode> selectList = list.stream().map(
                row -> {
                    SelectDataNode node = new SelectDataNode();
                    node.setCode(row.getCode());
                    node.setDesc(row.getDescription());
                    return node;
                }
        ).collect(Collectors.toList());
        return selectList;
    }
}

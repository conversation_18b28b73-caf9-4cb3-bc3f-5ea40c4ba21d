package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.PaymentCache;
import com.cw.entity.Payment;
import com.cw.pojo.dto.common.pagedata.SelectDataNode;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.GlobalDataType;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2024-04-02
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.payment)
public class PaymentDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String hotelId, String key) {
        PaymentCache cache = GlobalCache.getDataStructure().getCache(GlobalDataType.PAYMENT);
        Payment payment = cache.getRecord(hotelId, key);
        if (payment != null) {
            return payment.getDescription();
        }
        return key;
    }

    @Override
    public List<SelectDataNode> getSelectData(String hotelId, String... param) {

        List<Payment> list = GlobalCache.getDataStructure().getCache(GlobalDataType.PAYMENT).getDataList(hotelId);
        //重新组装数据
        List<SelectDataNode> selectList = list.stream().map(
                row -> {
                    SelectDataNode node = new SelectDataNode();
                    node.setCode(row.getPayCode());
                    node.setDesc(row.getDescription());
                    return node;
                }
        ).collect(Collectors.toList());
        return selectList;
    }
}

package com.cw.cache.customs;

import cn.hutool.core.util.StrUtil;
import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.BuildingCache;
import com.cw.entity.Building;
import com.cw.pojo.dto.common.pagedata.SelectDataNode;
import com.cw.service.context.GlobalContext;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.GlobalDataType;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2024-04-02
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.building)
public class BuildingDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String hotelId, String key) {
        BuildingCache cache = GlobalCache.getDataStructure().getCache(GlobalDataType.BUILDING);
        Building building = cache.getRecord(hotelId, key);
        if (building != null) {
            return building.getDescription();
        }
        return key;
    }

    @Override
    public List<SelectDataNode> getSelectData(String hotelId, String... param) {
        //先直接查 后续用缓存
        List<Building> list = GlobalCache.getDataStructure().getCache(GlobalDataType.BUILDING).getDataList(hotelId);
        String buildingNo = GlobalContext.getCurrentBuildingNo();
        if (StrUtil.isNotBlank(buildingNo)) {
            list = list.stream().filter(r -> r.getCode().equals(buildingNo)).collect(Collectors.toList());
        }

        //重新组装数据
        List<SelectDataNode> selectList = list.stream().map(
                row -> {
                    SelectDataNode node = new SelectDataNode();
                    node.setCode(row.getCode());
                    node.setDesc(row.getDescription());
                    return node;
                }
        ).collect(Collectors.toList());
        return selectList;
    }
}

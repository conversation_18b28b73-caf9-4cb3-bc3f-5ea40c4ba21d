package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.MarketCache;
import com.cw.cache.impl.SalerCache;
import com.cw.entity.Market;
import com.cw.entity.Saler;
import com.cw.pojo.dto.common.pagedata.SelectDataNode;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.GlobalDataType;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Describe 市场下拉框
 * <AUTHOR> <PERSON>
 * @Create on 2024-04-03
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.saler)
public class SalerDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String hotelId, String key) {
        SalerCache cache = GlobalCache.getDataStructure().getCache(GlobalDataType.SALER);
        Saler saler = cache.getRecord(hotelId, key);
        if (saler != null) {
            return saler.getDescription();
        }
        return key;
    }

    @Override
    public List<SelectDataNode> getSelectData(String hotelId, String... param) {
        List<Saler> list = GlobalCache.getDataStructure().getCache(GlobalDataType.SALER).getDataList(hotelId);
        //重新组装数据
        List<SelectDataNode> selectList = list.stream().map(
                        row -> {
                            if (StringUtils.isNotBlank(row.getSalerid()) && Boolean.valueOf(row.getOstatus())) {
                                SelectDataNode node = new SelectDataNode();
                                node.setCode(row.getSalerid());
                                node.setDesc(row.getDescription());
                                return node;
                            }
                            return null;
                        }

                )
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return selectList;
    }
}

package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.RsTypeCache;
import com.cw.entity.ReservationType;
import com.cw.pojo.dto.common.pagedata.SelectDataNode;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.GlobalDataType;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2024-04-03
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.restype)
public class ReservationTypeDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String hotelId, String key) {
        RsTypeCache cache = GlobalCache.getDataStructure().getCache(GlobalDataType.RSTYPE);
        ReservationType reservationType = cache.getRecord(hotelId, key);
        if (reservationType != null) {
            return reservationType.getDescription();
        }
        return key;
    }

    @Override
    public List<SelectDataNode> getSelectData(String hotelId, String... param) {

        List<ReservationType> list = GlobalCache.getDataStructure().getCache(GlobalDataType.RSTYPE).getDataList(hotelId);
        //重新组装数据
        List<SelectDataNode> selectList = list.stream().map(
                row -> {
                    SelectDataNode node = new SelectDataNode();
                    node.setCode(row.getCode());
                    node.setDesc(row.getDescription());
                    return node;
                }
        ).collect(Collectors.toList());
        return selectList;
    }
}

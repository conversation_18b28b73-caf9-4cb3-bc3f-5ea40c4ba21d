package com.cw.cache.customs;

import cn.hutool.core.util.EnumUtil;
import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.RoomRateCache;
import com.cw.entity.RoomRate;
import com.cw.pojo.dto.common.pagedata.SelectDataNode;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.GlobalDataType;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/13
 **/
@CustomDataProp(dataType = SystemUtil.CustomDataKey.roomstaff)
public class RoomStaffDataHandler extends BaseCustomHandler {
    @Override
    public String trans(String hotelId, String key) {

        return EnumUtil.fromString(SystemUtil.PositionType.class, key, SystemUtil.PositionType.CL).getPositionDesc();
    }

    @Override
    public List<SelectDataNode> getSelectData(String hotelId, String... param) {

        List<SystemUtil.PositionType> list = Arrays.asList(SystemUtil.PositionType.values());
        //重新组装数据
        List<SelectDataNode> selectList = list.stream().map(
                row -> {
                    SelectDataNode node = new SelectDataNode();
                    node.setCode(row.name());
                    node.setDesc(row.getPositionDesc());
                    return node;
                }
        ).collect(Collectors.toList());
        return selectList;
    }
}

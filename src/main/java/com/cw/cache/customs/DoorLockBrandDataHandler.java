package com.cw.cache.customs;

import cn.hutool.core.util.EnumUtil;
import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.entity.Floor;
import com.cw.pojo.dto.common.pagedata.SelectDataNode;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.DoorLockBrand;
import com.cw.utils.enums.GlobalDataType;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Describe 门锁品牌
 * <AUTHOR> Just
 * @Create on 2024-04-03
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.doorlockbrand)
public class DoorLockBrandDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String hotelId, String key) {
        DoorLockBrand brand = EnumUtil.fromString(DoorLockBrand.class, key);
        return brand == null ? key : brand.getDesc();
    }

    @Override
    public List<SelectDataNode> getSelectData(String hotelId, String... param) {
        List<Floor> list = GlobalCache.getDataStructure().getCache(GlobalDataType.FLOOR).getDataList(hotelId);
        DoorLockBrand[] values = DoorLockBrand.values();

        //重新组装数据
        List<SelectDataNode> selectList = Arrays.stream(values).map(r -> {
            SelectDataNode node = new SelectDataNode();
            node.setCode(r.name());
            node.setDesc(r.getDesc());
            node.setOption(r.isLocal() + "");
            return node;
        }).collect(Collectors.toList());
        return selectList;
    }
}

package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.ChannelCache;
import com.cw.entity.Channel;
import com.cw.pojo.dto.common.pagedata.SelectDataNode;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.GlobalDataType;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Describe 渠道下拉框
 * <AUTHOR> <PERSON>
 * @Create on 2024-04-03
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.channel)
public class ChannelDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String hotelId, String key) {
        ChannelCache roomTypeCache = GlobalCache.getDataStructure().getCache(GlobalDataType.CHANNEL);
        Channel channel = roomTypeCache.getRecord(hotelId, key);
        if (channel != null) {
            return channel.getDescription();
        }
        return key;
    }

    @Override
    public List<SelectDataNode> getSelectData(String hotelId, String... param) {
        List<Channel> list = GlobalCache.getDataStructure().getCache(GlobalDataType.CHANNEL).getDataList(hotelId);
        //重新组装数据
        List<SelectDataNode> selectList = list.stream().map(
                row -> {
                    SelectDataNode node = new SelectDataNode();
                    node.setCode(row.getCode());
                    node.setDesc(row.getDescription());
                    return node;
                }
        ).collect(Collectors.toList());
        return selectList;
    }
}

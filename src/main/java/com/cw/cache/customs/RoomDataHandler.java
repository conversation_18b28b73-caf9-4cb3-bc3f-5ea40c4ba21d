package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.RoomCache;
import com.cw.cache.impl.RoomTypeCache;
import com.cw.entity.Room;
import com.cw.entity.RoomType;
import com.cw.pojo.dto.common.pagedata.SelectDataNode;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.GlobalDataType;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2024-04-02
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.room)
public class RoomDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String hotelId, String key) {
        RoomCache roomCache = GlobalCache.getDataStructure().getCache(GlobalDataType.ROOM);

        Room room = roomCache.getRecord(hotelId, key);
        if (room != null) {
            return room.getRoomNo();
        }
        return key;
    }

    @Override
    public List<SelectDataNode> getSelectData(String hotelId, String... param) {
        List<Room> list = GlobalCache.getDataStructure().getCache(GlobalDataType.ROOM).getDataList(hotelId);
        //重新组装数据
        List<SelectDataNode> selectList = list.stream().map(
                row -> {
                    SelectDataNode node = new SelectDataNode();
                    node.setCode(row.getRoomNo());
                    node.setDesc(row.getRoomNo());
                    return node;
                }
        ).collect(Collectors.toList());
        return selectList;
    }
}

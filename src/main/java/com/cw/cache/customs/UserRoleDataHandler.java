package com.cw.cache.customs;


import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.OpRoleCache;
import com.cw.entity.OpRole;
import com.cw.mapper.OpRoleMapper;
import com.cw.pojo.dto.common.pagedata.SelectDataNode;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.GlobalDataType;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/12/4 13:53
 **/
@CustomDataProp(dataType = SystemUtil.CustomDataKey.userrole)
public class UserRoleDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String hotelId, String key) {
        OpRoleCache cache = GlobalCache.getDataStructure().getCache(GlobalDataType.USER_ROLE);
        OpRole record = cache.getRecord(hotelId, key);
        if (record != null) {
            return record.getName();
        } else {
            return key;
        }

    }

    @Override
    public List<SelectDataNode> getSelectData(String hotelId, String... param) {
        OpRoleCache cache = GlobalCache.getDataStructure().getCache(GlobalDataType.USER_ROLE);
        List<OpRole> list = cache.getDataList(hotelId);
        //重新组装数据
        List<SelectDataNode> selectList = list.stream().map(
                row -> {
                    SelectDataNode node = new SelectDataNode();
                    node.setCode(row.getRoleId());
                    node.setDesc(row.getName());
                    return node;
                }
        ).collect(Collectors.toList());
        return selectList;
    }


}

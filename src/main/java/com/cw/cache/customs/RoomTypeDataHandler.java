package com.cw.cache.customs;

import cn.hutool.core.util.StrUtil;
import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.RoomTypeCache;
import com.cw.entity.RoomType;
import com.cw.pojo.dto.common.pagedata.SelectDataNode;
import com.cw.service.context.GlobalContext;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.GlobalDataType;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2024-04-02
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.roomtype)
public class RoomTypeDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String hotelId, String key) {
        RoomTypeCache roomTypeCache = GlobalCache.getDataStructure().getCache(GlobalDataType.ROOMTYPE);

        RoomType roomType = roomTypeCache.getRecord(hotelId, key);
        if (roomType != null) {
            return roomType.getDescription();
        }
        return key;
    }

    @Override
    public List<SelectDataNode> getSelectData(String hotelId, String... param) {
        List<RoomType> list = GlobalCache.getDataStructure().getCache(GlobalDataType.ROOMTYPE).getDataList(hotelId);
        String buildingNo = GlobalContext.getCurrentBuildingNo();
        if (StrUtil.isNotBlank(buildingNo)) {
            list = list.stream().filter(r -> r.getBuildingNo().equals(buildingNo)).collect(Collectors.toList());
        }
        //重新组装数据
        List<SelectDataNode> selectList = list.stream().map(
                row -> {
                    SelectDataNode node = new SelectDataNode();
                    node.setCode(row.getRoomType());
                    node.setDesc(row.getDescription());
                    node.setGeneric(row.getGeneric());
                    return node;
                }
        ).collect(Collectors.toList());
        return selectList;
    }
}

package com.cw.cache.customs;


import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.OpRoleCache;
import com.cw.cache.impl.OpUserCache;
import com.cw.entity.OpRole;
import com.cw.entity.OpUser;
import com.cw.pojo.dto.common.pagedata.SelectDataNode;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.GlobalDataType;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/12/4 13:53
 **/
@CustomDataProp(dataType = SystemUtil.CustomDataKey.opuser)
public class OpUserDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String hotelId, String key) {
        OpUserCache cache = GlobalCache.getDataStructure().getCache(GlobalDataType.USER);
        OpUser record = cache.getRecord(hotelId, key);
        if (record != null) {
            return record.getUserid();
        } else {
            return key;
        }

    }

    @Override
    public List<SelectDataNode> getSelectData(String hotelId, String... param) {
        OpUserCache cache = GlobalCache.getDataStructure().getCache(GlobalDataType.USER);
        List<OpUser> list = cache.getDataList(hotelId);
        //重新组装数据
        List<SelectDataNode> selectList = list.stream().map(
                row -> {
                    SelectDataNode node = new SelectDataNode();
                    node.setCode(row.getUserid());
                    node.setDesc(row.getUsername());
                    return node;
                }
        ).collect(Collectors.toList());
        return selectList;
    }


}

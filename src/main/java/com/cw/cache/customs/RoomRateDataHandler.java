package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.RoomRateCache;
import com.cw.entity.RoomRate;
import com.cw.pojo.dto.common.pagedata.SelectDataNode;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.GlobalDataType;

import java.util.List;
import java.util.stream.Collectors;


/**
 *
 * <AUTHOR>
 * @Date 2024-06-04 10:18
 *
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.roomrate)
public class RoomRateDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String hotelId, String key) {
        RoomRateCache cache = GlobalCache.getDataStructure().getCache(GlobalDataType.ROOMRATE);
        RoomRate roomRate = cache.getRecord(hotelId, key);
        if (roomRate != null) {
            return roomRate.getDescription();
        }
        return key;
    }

    @Override
    public List<SelectDataNode> getSelectData(String hotelId, String... param) {
        List<RoomRate> list = GlobalCache.getDataStructure().getCache(GlobalDataType.ROOMRATE).getDataList(hotelId);
        //重新组装数据
        List<SelectDataNode> selectList = list.stream().map(
                row -> {
                    SelectDataNode node = new SelectDataNode();
                    node.setCode(row.getCode());
                    node.setDesc(row.getDescription());
                    return node;
                }
        ).collect(Collectors.toList());
        return selectList;
    }
}

package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.pojo.dto.common.pagedata.SelectDataNode;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.BedType;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2024-06-27
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.bedtype)
public class BedTypeDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String hotelId, String key) {
        BedType[] values = BedType.values();
        for (BedType bedType : values) {
            if (key.equals(bedType.getVal())) {
                return bedType.getDesc();
            }
        }
        return key;
    }

    @Override
    public List<SelectDataNode> getSelectData(String hotelId, String... param) {
        BedType[] values = BedType.values();
        List<SelectDataNode> list = new ArrayList<>();
        for (BedType bedType : values) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(bedType.getVal());
            node.setDesc(bedType.getDesc());
            list.add(node);
        }

        return list;
    }
}

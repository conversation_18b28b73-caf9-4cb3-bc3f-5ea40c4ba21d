package com.cw.cache;


import com.cw.utils.SystemUtil;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/9/6 11:25
 **/
@Target(ElementType.TYPE)  //应用于接口,类.枚举.注解
@Retention(RetentionPolicy.RUNTIME)  //反射的时候能够获取
@Documented              //允许 JAVA DOC 生成描述
@Inherited                    //允许子类继承使用
public @interface CustomDataProp {
    String description() default "";

    SystemUtil.CustomDataKey dataType(); //对应的 datakey .用来注册


}

package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Saler;
import com.cw.entity.Shift;
import com.cw.utils.RedisKey;
import com.cw.utils.enums.GlobalDataType;

/**
 * <AUTHOR>
 * @date 2025/5/14
 **/
@CacheProp(dataType = GlobalDataType.SHIFT, cacheClass = Shift.class)
public class ShiftCache extends BaseCacheData<Shift> {

    @Override
    public String getUniqueKey(Shift shift) {
        return String.valueOf(shift.getShiftId());
    }

    @Override
    public String getRediskey(Shift shift) {
        return shift.getHotelId() + RedisKey.SPLITSIGNAL + shift.getShiftId();
    }
}

package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.AccountItem;
import com.cw.entity.OpRoleRight;
import com.cw.entity.Payment;
import com.cw.utils.RedisKey;
import com.cw.utils.enums.GlobalDataType;

import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/4/9 23:47
 **/
@CacheProp(dataType = GlobalDataType.PAYMENT, cacheClass = Payment.class)
public class PaymentCache extends BaseCacheData<Payment> {
    @Override
    public String getUniqueKey(Payment payment) {
        return payment.getPayCode();
    }

    @Override
    public String getRediskey(Payment payment) {
        return payment.getHotelId() + RedisKey.SPLITSIGNAL + payment.getPayCode();
    }

    @Override
    protected void sortList(List<Payment> list) {
        list.sort(Comparator.comparing(Payment::getSeq));
    }
}

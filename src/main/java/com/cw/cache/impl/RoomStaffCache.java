package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.RoomStaff;
import com.cw.entity.Saler;
import com.cw.utils.RedisKey;
import com.cw.utils.enums.GlobalDataType;

/**
 * <AUTHOR>
 * @date 2025/5/13
 **/
@CacheProp(dataType = GlobalDataType.ROOMSTAFF, cacheClass = RoomStaff.class)
public class RoomStaffCache extends BaseCacheData<RoomStaff> {

    @Override
    public String getUniqueKey(RoomStaff roomStaff) {
        return String.valueOf(roomStaff.getWxUserId());
    }

    @Override
    public String getRediskey(RoomStaff roomStaff) {
        return roomStaff.getHotelId() + RedisKey.SPLITSIGNAL + roomStaff.getWxUserId();
    }
}
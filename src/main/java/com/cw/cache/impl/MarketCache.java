package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Factor;
import com.cw.entity.Market;
import com.cw.utils.RedisKey;
import com.cw.utils.enums.GlobalDataType;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/4/9 23:47
 **/
@CacheProp(dataType = GlobalDataType.MARKET, cacheClass = Market.class)
public class MarketCache extends BaseCacheData<Market> {

    @Override
    public String getUniqueKey(Market market) {
        return market.getCode();
    }

    @Override
    public String getRediskey(Market market) {
        return market.getHotelId() + RedisKey.SPLITSIGNAL + market.getCode();
    }
}

package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.RoomRate;
import com.cw.utils.RedisKey;
import com.cw.utils.enums.GlobalDataType;

import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/4/10 00:33
 **/
@CacheProp(dataType = GlobalDataType.ROOMRATE, cacheClass = RoomRate.class)
public class RoomRateCache extends BaseCacheData<RoomRate> {
    @Override
    public String getUniqueKey(RoomRate roomRate) {
        return roomRate.getCode();
    }

    @Override
    public String getRediskey(RoomRate roomRate) {
        return roomRate.getHotelId() + RedisKey.SPLITSIGNAL + roomRate.getCode();
    }

    @Override
    protected void sortList(List<RoomRate> list) {
        list.sort(Comparator.comparing(RoomRate::getSeq));
    }
}

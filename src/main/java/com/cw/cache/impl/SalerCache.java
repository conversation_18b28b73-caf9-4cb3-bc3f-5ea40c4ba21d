package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Market;
import com.cw.entity.Saler;
import com.cw.utils.RedisKey;
import com.cw.utils.enums.GlobalDataType;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/4/9 23:47
 **/
@CacheProp(dataType = GlobalDataType.SALER, cacheClass = Saler.class)
public class SalerCache extends BaseCacheData<Saler> {

    @Override
    public String getUniqueKey(Saler saler) {
        return saler.getSalerid();
    }

    @Override
    public String getRediskey(Saler saler) {
        return saler.getHotelId() + RedisKey.SPLITSIGNAL + saler.getSalerid();
    }
}

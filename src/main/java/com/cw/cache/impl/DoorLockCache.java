package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Doorlock;
import com.cw.entity.Market;
import com.cw.utils.RedisKey;
import com.cw.utils.enums.GlobalDataType;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/4/9 23:47
 **/
@CacheProp(dataType = GlobalDataType.DOORLORK, cacheClass = Doorlock.class)
public class DoorLockCache extends BaseCacheData<Doorlock> {

    @Override
    public String getUniqueKey(Doorlock doorlock) {
        return doorlock.getLockno();
    }

    @Override
    public String getRediskey(Doorlock doorlock) {
        return doorlock.getHotelId() + RedisKey.SPLITSIGNAL + doorlock.getLockno();
    }
}

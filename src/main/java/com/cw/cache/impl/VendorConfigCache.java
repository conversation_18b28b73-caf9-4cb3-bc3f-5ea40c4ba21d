package com.cw.cache.impl;

import cn.hutool.core.util.StrUtil;
import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Vendorconfig;
import com.cw.utils.RedisKey;
import com.cw.utils.enums.GlobalDataType;
import com.cw.utils.enums.VendorType;
import org.springframework.boot.autoconfigure.cache.CacheProperties;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/4/9 23:48
 **/
@CacheProp(dataType = GlobalDataType.VENDORCONFIG, cacheClass = Vendorconfig.class)
public class VendorConfigCache extends BaseCacheData<Vendorconfig> {
    @Override
    public String getUniqueKey(Vendorconfig vendorconfig) {
        return vendorconfig.getVtype() + vendorconfig.getId();
    }

    @Override
    public String getRediskey(Vendorconfig vendorconfig) {
        return vendorconfig.getHotelId() + RedisKey.SPLITSIGNAL + vendorconfig.getVtype() + RedisKey.SPLITSIGNAL + vendorconfig.getId();
    }

    @Override
    public List<String> getIndexKeys(Vendorconfig vendorconfig) {
        if (StrUtil.isNotBlank(vendorconfig.getAppid())) {
            return null;
        }
        List<String> ids = Arrays.asList(vendorconfig.getAppid().isEmpty() ? vendorconfig.getId() + "" : vendorconfig.getAppid());
        return ids;
    }


    public Vendorconfig getVendorConfig(String hotelId, VendorType vendorType) {//获取当前项目中的该供应商激活配置
        List<Vendorconfig> vendorconfigs = getDataList(hotelId);
        for (Vendorconfig vendorconfig : vendorconfigs) {
            if (vendorconfig.getActive() && vendorconfig.getVtype().equals(vendorType.name())) {
                return vendorconfig;
            }
        }
        return null;
    }
}

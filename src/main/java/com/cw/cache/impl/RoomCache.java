package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.OpRoleRight;
import com.cw.entity.Room;
import com.cw.utils.RedisKey;
import com.cw.utils.enums.GlobalDataType;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/4/9 23:47
 **/
@CacheProp(dataType = GlobalDataType.ROOM, cacheClass = Room.class)
public class RoomCache extends BaseCacheData<Room> {
    @Override
    public String getUniqueKey(Room room) {
        return room.getRoomNo();
    }

    @Override
    public String getRediskey(Room room) {
        return room.getHotelId() + RedisKey.SPLITSIGNAL + room.getRoomNo();
    }
}

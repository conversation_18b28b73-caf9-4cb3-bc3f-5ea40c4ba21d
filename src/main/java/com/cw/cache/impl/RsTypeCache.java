package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.OpRoleRight;
import com.cw.entity.ReservationType;
import com.cw.utils.RedisKey;
import com.cw.utils.enums.GlobalDataType;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/4/9 23:47
 **/
@CacheProp(dataType = GlobalDataType.RSTYPE, cacheClass = ReservationType.class)
public class RsTypeCache extends BaseCacheData<ReservationType> {
    @Override
    public String getUniqueKey(ReservationType reservationType) {
        return reservationType.getCode();
    }

    @Override
    public String getRediskey(ReservationType reservationType) {
        return reservationType.getHotelId() + RedisKey.SPLITSIGNAL + reservationType.getCode();
    }
}

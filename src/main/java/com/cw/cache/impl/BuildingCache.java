package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Building;
import com.cw.entity.Factor;
import com.cw.utils.RedisKey;
import com.cw.utils.enums.GlobalDataType;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/4/9 23:46
 **/
@CacheProp(dataType = GlobalDataType.BUILDING, cacheClass = Building.class)
public class BuildingCache extends BaseCacheData<Building> {
    @Override
    public String getUniqueKey(Building building) {
        return building.getCode();
    }

    @Override
    public String getRediskey(Building building) {
        return building.getHotelId() + RedisKey.SPLITSIGNAL + building.getCode();
    }
}

package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Optionswitch;
import com.cw.utils.RedisKey;
import com.cw.utils.enums.GlobalDataType;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/4/9 23:43
 **/
@CacheProp(dataType = GlobalDataType.OPTIONS, cacheClass = Optionswitch.class)
public class OptionsCache extends BaseCacheData<Optionswitch> {
    @Override
    public String getUniqueKey(Optionswitch optionswitch) {
        return optionswitch.getOption();
    }

    @Override
    public String getRediskey(Optionswitch optionswitch) {
        return optionswitch.getHotelId() + RedisKey.SPLITSIGNAL + optionswitch.getOption();
    }
}

package com.cw.cache.impl;


import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.NaParam;
import com.cw.utils.RedisKey;
import com.cw.utils.enums.GlobalDataType;

import java.util.Collections;
import java.util.List;

@CacheProp(dataType = GlobalDataType.NA_PARAM, cacheClass = NaParam.class)
public class NaParamCache extends BaseCacheData<NaParam> {


    @Override
    public String getUniqueKey(NaParam naParam) {
        return naParam.getParamValue();
    }

    @Override
    public String getRediskey(NaParam naParam) {
        return naParam.getHotelId() + RedisKey.SPLITSIGNAL + naParam.getParamName();
    }

    @Override
    protected void sortList(List<NaParam> list) {
        Collections.sort(list, (o1, o2) -> o1.getParamName().compareTo(o2.getParamName()));
    }


}

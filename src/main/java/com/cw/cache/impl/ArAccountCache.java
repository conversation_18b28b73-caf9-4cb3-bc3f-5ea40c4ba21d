package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.AccountItem;
import com.cw.entity.ArAccount;
import com.cw.utils.RedisKey;
import com.cw.utils.enums.GlobalDataType;

import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/4/9 23:45
 **/
@CacheProp(dataType = GlobalDataType.ARACCOUNT, cacheClass = ArAccount.class)
public class ArAccountCache extends BaseCacheData<ArAccount> {
    @Override
    public String getUniqueKey(ArAccount accountItem) {
        return accountItem.getCode();
    }

    @Override
    public String getRediskey(ArAccount accountItem) {
        return accountItem.getHotelId() + RedisKey.SPLITSIGNAL + accountItem.getCode();
    }

    @Override
    protected void sortList(List<ArAccount> list) {
        super.sortList(list);
        list.sort(Comparator.comparing(ArAccount::getCode));
    }
}

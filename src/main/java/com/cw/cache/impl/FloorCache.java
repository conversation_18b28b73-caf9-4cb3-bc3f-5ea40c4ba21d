package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Floor;
import com.cw.entity.Market;
import com.cw.utils.RedisKey;
import com.cw.utils.enums.GlobalDataType;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/4/9 23:47
 **/
@CacheProp(dataType = GlobalDataType.FLOOR, cacheClass = Floor.class)
public class FloorCache extends BaseCacheData<Floor> {

    @Override
    public String getUniqueKey(Floor floor) {
        return floor.getCode() + "";
    }

    @Override
    public String getRediskey(Floor floor) {
        return floor.getHotelId() + RedisKey.SPLITSIGNAL + floor.getCode();
    }
}

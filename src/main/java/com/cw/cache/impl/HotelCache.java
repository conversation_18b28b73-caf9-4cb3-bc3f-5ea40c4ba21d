package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Hotel;
import com.cw.utils.RedisKey;
import com.cw.utils.enums.GlobalDataType;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2024-05-29
 */
@CacheProp(dataType = GlobalDataType.HOTEL, cacheClass = Hotel.class)
public class HotelCache extends BaseCacheData<Hotel> {

    @Override
    public String getUniqueKey(Hotel hotel) {
        return hotel.getHotelId();
    }

    @Override
    public String getRediskey(Hotel hotel) {
        return hotel.getHotelId() + RedisKey.SPLITSIGNAL + hotel.getHotelId();
    }
}

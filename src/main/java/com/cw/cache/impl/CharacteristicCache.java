package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Characteristic;
import com.cw.entity.Factor;
import com.cw.utils.RedisKey;
import com.cw.utils.enums.GlobalDataType;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/4/9 23:46
 **/
@CacheProp(dataType = GlobalDataType.CHARACTERISTIC, cacheClass = Characteristic.class)
public class CharacteristicCache extends BaseCacheData<Characteristic> {
    @Override
    public String getUniqueKey(Characteristic characteristic) {
        return characteristic.getCharacterCode();
    }

    @Override
    public String getRediskey(Characteristic characteristic) {
        return characteristic.getHotelId() + RedisKey.SPLITSIGNAL + characteristic.getCharacterCode();
    }
}

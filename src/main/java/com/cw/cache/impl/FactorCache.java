package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Factor;
import com.cw.utils.RedisKey;
import com.cw.utils.enums.GlobalDataType;

import java.util.Comparator;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/9/23 0023
 */
@CacheProp(dataType = GlobalDataType.FACTOR, cacheClass = Factor.class)
public class FactorCache extends BaseCacheData<Factor> {

    @Override
    public String getUniqueKey(Factor factor) {
        return factor.getCode();

    }

    @Override
    public String getRediskey(Factor factor) {
        return factor.getHotelId() + RedisKey.SPLITSIGNAL + factor.getCode();
    }


    @Override
    protected void sortList(List<Factor> list) {
        super.sortList(list);
        list.sort(Comparator.comparing(Factor::getType).thenComparing(Factor::getHeader).thenComparing(Factor::getSeq));
    }


}

package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.OpRole;
import com.cw.entity.OpRoleRight;
import com.cw.utils.RedisKey;
import com.cw.utils.enums.GlobalDataType;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/4/9 23:44
 **/
@CacheProp(dataType = GlobalDataType.OP_ROLE_RIGHT, cacheClass = OpRoleRight.class)
public class OpRoleRightCache extends BaseCacheData<OpRoleRight> {
    @Override
    public String getUniqueKey(OpRoleRight opRoleRight) {
        return opRoleRight.getRoleId() + opRoleRight.getRightId();
    }

    @Override
    public String getRediskey(OpRoleRight opRoleRight) {
        return opRoleRight.getHotelId() + RedisKey.SPLITSIGNAL + opRoleRight.getRoleId() + RedisKey.SPLITSIGNAL + opRoleRight.getRightId();
    }
}

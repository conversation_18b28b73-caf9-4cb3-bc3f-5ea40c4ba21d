package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.RoomType;
import com.cw.utils.RedisKey;
import com.cw.utils.enums.GlobalDataType;

import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/12/2 09:47
 **/
@CacheProp(dataType = GlobalDataType.ROOMTYPE, cacheClass = RoomType.class)
public class RoomTypeCache extends BaseCacheData<RoomType> {


    //
    //@Override
    //protected void refreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, RoomType>> datas) {
    //    RoomTypeMapper mapper = SpringUtil.getBean(RoomTypeMapper.class);
    //    List<RoomType> lists = mapper.findAll();
    //    Map<String, List<RoomType>> hotelIdData = lists.stream().collect(Collectors.groupingBy(RoomType::getHotelId));
    //    //按照酒店分组
    //    for (String hotelId : hotelIdData.keySet()) {
    //        datas.put(hotelId, Maps.newConcurrentMap());
    //        ConcurrentMap<String, List<RoomType>> RoomTypes = lists.stream().filter(r -> r.getHotelId().equals(hotelId))
    //                .collect(Collectors.groupingByConcurrent(RoomType::getHotelId));
    //        for (Map.Entry<String, List<RoomType>> entry : RoomTypes.entrySet()) {
    //            groupdatas.put(hotelId, entry.getKey(), entry.getValue());
    //        }
    //    }
    //
    //    for (Map.Entry<String, List<RoomType>> entry : hotelIdData.entrySet()) {
    //        ConcurrentMap<String, RoomType> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(RoomType::getRoomTypeCode,
    //                Function.identity(), (t1, t2) -> t2));
    //        datas.put(entry.getKey(), map);
    //    }
    //
    //
    //}
    //
    //@Override
    //protected void refreshHotelData(ConcurrentMap<String, RoomType> hotelIdDatas, String hotelId) {
    //    DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
    //    List<RoomType> RoomTypes = daoLocal.getHotelIdObjectList(RoomType.class, hotelId);
    //    for (RoomType RoomType : RoomTypes) {
    //        hotelIdDatas.put(RoomType.getRoomTypeCode(), RoomType);
    //    }
    //    ConcurrentMap<String, List<RoomType>> RoomTypeMap = RoomTypes.stream().collect(Collectors.groupingByConcurrent(RoomType::getHotelId));
    //    for (Map.Entry<String, List<RoomType>> entry : RoomTypeMap.entrySet()) {
    //        groupdatas.put(hotelId, entry.getKey(), entry.getValue());
    //    }
    //}

    @Override
    public String getUniqueKey(RoomType roomType) {
        return roomType.getRoomType();
    }

    @Override
    public String getRediskey(RoomType roomType) {
        return roomType.getHotelId() + RedisKey.SPLITSIGNAL + roomType.getRoomType();
    }

    @Override
    protected void sortList(List<RoomType> list) {
        list.sort(Comparator.comparing(RoomType::getSeq));
    }


}

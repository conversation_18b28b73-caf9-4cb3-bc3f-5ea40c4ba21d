package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Channel;
import com.cw.entity.Factor;
import com.cw.utils.RedisKey;
import com.cw.utils.enums.GlobalDataType;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/4/9 23:43
 **/
@CacheProp(dataType = GlobalDataType.CHANNEL, cacheClass = Channel.class)
public class ChannelCache extends BaseCacheData<Channel> {


    @Override
    public String getUniqueKey(Channel channel) {
        return channel.getCode();
    }

    @Override
    public String getRediskey(Channel channel) {
        return channel.getHotelId() + RedisKey.SPLITSIGNAL + channel.getCode();
    }
}

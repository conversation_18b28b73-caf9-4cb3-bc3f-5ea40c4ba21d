package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Hotel;
import com.cw.entity.ReservationType;
import com.cw.utils.enums.GlobalDataType;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/4/9 23:43
 **/
//@CacheProp(dataType = GlobalDataType.HOTEL, cacheClass = Hotel.class)
public class SysconfCache extends BaseCacheData<Hotel> {


    @Override
    public String getUniqueKey(Hotel hotel) {
        return hotel.getHotelId();
    }

    @Override
    public String getRediskey(Hotel hotel) {
        return hotel.getHotelId();
    }
}

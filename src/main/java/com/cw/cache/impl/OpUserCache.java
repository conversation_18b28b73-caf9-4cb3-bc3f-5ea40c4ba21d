package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.OpUser;
import com.cw.utils.RedisKey;
import com.cw.utils.enums.GlobalDataType;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2024-06-27
 */
@CacheProp(dataType = GlobalDataType.USER, cacheClass = OpUser.class)
public class OpUserCache extends BaseCacheData<OpUser> {

    public OpUserCache() {
        super();
        this.defaultCodeFiled = "userid";  //重载.修改默认的下拉取值 key 字段
    }

    @Override
    public String getUniqueKey(OpUser opUser) {
        return opUser.getUserid();
    }

    @Override
    public String getRediskey(OpUser opUser) {
        return opUser.getHotelId() + RedisKey.SPLITSIGNAL + opUser.getUserid();
    }
}

package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.AccountItem;
import com.cw.utils.RedisKey;
import com.cw.utils.enums.GlobalDataType;

import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/4/9 23:45
 **/
@CacheProp(dataType = GlobalDataType.ACCOUNTITEM, cacheClass = AccountItem.class)
public class AccountItemCache extends BaseCacheData<AccountItem> {
    @Override
    public String getUniqueKey(AccountItem accountItem) {
        return accountItem.getCode();
    }

    @Override
    public String getRediskey(AccountItem accountItem) {
        return accountItem.getHotelId() + RedisKey.SPLITSIGNAL + accountItem.getCode();
    }

    @Override
    protected void sortList(List<AccountItem> list) {
        list.sort(Comparator.comparing(AccountItem::getSeq));
    }
}

package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.IncludeRate;
import com.cw.entity.RoomRate;
import com.cw.utils.RedisKey;
import com.cw.utils.enums.GlobalDataType;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/4/10 00:33
 **/
@CacheProp(dataType = GlobalDataType.INCLUDERATE, cacheClass = IncludeRate.class)
public class IncludeRateCache extends BaseCacheData<IncludeRate> {
    @Override
    public String getUniqueKey(IncludeRate includeRate) {
        return includeRate.getCode();
    }

    @Override
    public String getRediskey(IncludeRate includeRate) {
        return includeRate.getHotelId() + RedisKey.SPLITSIGNAL + includeRate.getCode();
    }
}

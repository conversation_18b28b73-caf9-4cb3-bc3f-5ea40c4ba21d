package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.OpRole;
import com.cw.mapper.OpRoleMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.RedisKey;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.GlobalDataType;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/12/2 09:47
 **/
@CacheProp(dataType = GlobalDataType.USER_ROLE, cacheClass = OpRole.class)
public class OpRoleCache extends BaseCacheData<OpRole> {


    public OpRoleCache() {
        super();
        //重载.修改默认的下拉取值 key 字段
        this.defaultCodeFiled = "roleid";
    }


    @Override
    public String getUniqueKey(OpRole opRole) {
        return opRole.getRoleId();
    }

    @Override
    public String getRediskey(OpRole opRole) {
        return opRole.getHotelId() + RedisKey.SPLITSIGNAL + opRole.getRoleId();
    }
}

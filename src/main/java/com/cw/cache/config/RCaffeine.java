package com.cw.cache.config;

import com.cw.utils.SystemUtil;
import com.cw.utils.enums.GlobalDataType;
import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Descripstion 本地缓存中心.负责存储单个JAVA对象与酒店的单表数据
 * @Create 2024/3/28 10:22
 **/
@Configuration
public class RCaffeine {
    public static CaffeineCacheManager cacheManager;
    public static final String uniqueStr = "UNIQUE";
    int maxSize = 100000;
    int ttl = 60;

    public static CacheManager getCacheManager() {
        return cacheManager;
    }

    public static Cache getCache(GlobalDataType type) {
        Cache cache = cacheManager.getCache(type.name());
        return cache;
    }

    /**
     * 除了每个酒店数据的单个本地缓存外.增加一个唯一字段数据缓存
     *
     * @param type
     * @return
     */
    public static Cache getUniqueCache(GlobalDataType type) {
        Cache cache = cacheManager.getCache(uniqueStr + type.name());
        if (cache == null) {
            cacheManager.registerCustomCache(uniqueStr + type.name(),
                    Caffeine.newBuilder()
                            .expireAfterAccess(600, TimeUnit.SECONDS)
                            .build());
            cache = cacheManager.getCache(uniqueStr + type.name());
        }
        return cache;
    }

    @Bean
    public CacheManager cacheManager() {
        cacheManager = new CaffeineCacheManager();
        List<GlobalDataType> ls = Arrays.asList(GlobalDataType.values());

        for (GlobalDataType datatype : ls) {
            //针对每种类型的数据，设置不同的缓存过期时间. 测试初期.这个缓存时间暂定为60秒
            cacheManager.registerCustomCache(datatype.name(),
                    Caffeine.newBuilder()
                            .expireAfterAccess(ttl, TimeUnit.SECONDS)
                            .recordStats()
                            .build());

        }
        return cacheManager;
    }


}

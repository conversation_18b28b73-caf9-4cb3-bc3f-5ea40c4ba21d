package com.cw.cache;

import cn.hutool.core.collection.CollectionUtil;
import com.cw.cache.impl.OptionsCache;
import com.cw.entity.Optionswitch;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.GlobalDataType;
import com.cw.utils.options.Options;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @createTime 2021/08/04/ 10:58
 */
@Component
@Service
public class OptionSwitchTool {

    public static boolean getOptionSatus(Options options, String hotelId) {
        boolean satus = false;
        com.cw.cache.DataStructure dataStructure = GlobalCache.getDataStructure();
        if (dataStructure == null) {
            // 可根据实际情况记录日志
            System.err.println("GlobalCache 数据结构未初始化，hotelId: " + hotelId);
        } else {
            OptionsCache optionsCache = dataStructure.getCache(GlobalDataType.OPTIONS);

            Optionswitch optionswitch = optionsCache.getRecord(hotelId, options.name());

            if (optionswitch != null) {
                satus = optionswitch.getSwitchstatus();
            } else { //如果数据库没保存 取枚举默认值
                satus = options.getDefaultValue();
            }
        }
        return satus;
    }

    public static String getOptionVal(Options options, String hotelId) {
        OptionsCache optionsCache = GlobalCache.getDataStructure().getCache(GlobalDataType.OPTIONS);

        Optionswitch optionswitch = optionsCache.getRecord(hotelId, options.name());

        if (optionswitch != null) {
            return optionswitch.getVal();
        } else { //如果数据库没保存 取枚举默认值
            return options.getVal();
        }
    }

    public List<Optionswitch> getOptionData(String hotelId) {
        List<Optionswitch> optionswitchs = CollectionUtil.newArrayList();
        com.cw.cache.DataStructure dataStructure = GlobalCache.getDataStructure();
        if (dataStructure == null) {
            // 可根据实际情况记录日志
            System.err.println("GlobalCache 数据结构未初始化，hotelId: " + hotelId);
        } else {
            OptionsCache optionsCache = dataStructure.getCache(GlobalDataType.OPTIONS);
            optionswitchs = optionsCache.getDataList(hotelId);
        }

        return optionswitchs;
    }
}

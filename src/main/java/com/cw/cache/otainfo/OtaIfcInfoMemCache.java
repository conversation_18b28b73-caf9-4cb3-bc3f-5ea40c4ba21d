package com.cw.cache.otainfo;

import com.cw.entity.IfcUser;

import java.util.Map;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2025/2/27 16:05
 **/
public class OtaIfcInfoMemCache {

    private static Map<String, IfcUser> appidInfo;

    static {
        appidInfo = new java.util.HashMap<>();

        IfcUser otaIfcUser = new IfcUser();
        otaIfcUser.setAppid("001");
        otaIfcUser.setSecret("5bcaqh19ks128q5auw1lo23v6fi7cjht");
        otaIfcUser.setHotelId("001");

    }


}

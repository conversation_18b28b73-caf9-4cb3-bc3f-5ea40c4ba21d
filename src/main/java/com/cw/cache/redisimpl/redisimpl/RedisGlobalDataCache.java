package com.cw.cache.redisimpl.redisimpl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.cw.cache.BaseCacheData;
import com.cw.cache.GlobalCache;
import com.cw.cache.RedisTool;
import com.cw.utils.RedisKey;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.GlobalDataType;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Maps;
import com.google.common.collect.Multimap;
import com.google.common.collect.Sets;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.ScanOptions;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2022/2/13 14:06
 **/
public class RedisGlobalDataCache {

    /**
     * @param globalDataType
     * @return
     */
    public static String getRedisTableMapKey(GlobalDataType globalDataType) {
        return RedisKey.PmsGlobalTableCacheKey + globalDataType.name();
    }

    public static <T> void cleanBeforeUpd(String hotelId, GlobalDataType globalDataType, List<T> dbrecords) {
        BaseCacheData<T> baseCacheData = GlobalCache.getDataStructure().getCache(globalDataType);
        Set<String> dbKeys = Sets.newHashSet();
        for (T dbrecord : dbrecords) {
            String dbKey = baseCacheData.getRediskey(dbrecord);
            if (StrUtil.isNotBlank(dbKey)) {
                dbKeys.add(dbKey);
            }
        }
        String patternKey = hotelId + ":*";
        ScanOptions options = ScanOptions.scanOptions()
                .match(patternKey).build();
        Set<String> redisExKeys = Sets.newHashSet();
        Cursor<Map.Entry<String, String>> cursor = RedisTool.getInstance().opsForHash().
                scan(RedisGlobalDataCache.getRedisTableMapKey(globalDataType), options);
        while (cursor.hasNext()) {
            redisExKeys.add(cursor.next().getKey());
        }
        try {
            cursor.close();
        } catch (IOException e) {
        }

        int cleancount = 0;
        for (String redisExKey : redisExKeys) { //REIDS 中缓存的配置按db 中的为准
            if (!dbKeys.contains(redisExKey)) {
                RedisTool.getInstance().opsForHash().delete(RedisGlobalDataCache.getRedisTableMapKey(globalDataType), redisExKey);
                cleancount++;
            }
        }
        for (String dbKey : dbKeys) {
            if (!redisExKeys.contains(dbKey)) {
                LoggerFactory.getLogger(RedisGlobalDataCache.class).warn("{}缓存待添加数据-->{}", globalDataType, dbKey);
            }
        }
        if (cleancount > 0) {
            LoggerFactory.getLogger(RedisGlobalDataCache.class).info("清理非同步数据{} -{} 条", globalDataType, cleancount);
        }

    }


    @SafeVarargs
    public static <T> void updRecords(GlobalDataType globalDataType, T... t) {//更新redis 单条记录
        if (t != null && t.length > 0) {
            Map<String, String> maps = Maps.newHashMap();
            BaseCacheData<T> baseCacheData = GlobalCache.getDataStructure().getCache(globalDataType);
            for (T r : t) {
                String key = baseCacheData.getRediskey(r);
                if (key.isEmpty()) {
                    continue;
                }
                String json = JSON.toJSONString(r);
                maps.put(baseCacheData.getRediskey(r), json);
                List<String> indexKeys = baseCacheData.getIndexKeys(r);
                if (CollectionUtil.isNotEmpty(indexKeys)) {//有时候数据的唯一字段并不唯一.需要根据indexKey 来查找
                    for (String indexKey : indexKeys) {
                        maps.put(indexKey, json);
                    }
                }
            }
            RedisTool.getInstance().opsForHash().putAll(getRedisTableMapKey(globalDataType), maps);
        }

    }

    @SafeVarargs
    public static <T> void removeRecords(String hotelId, GlobalDataType globalDataType, T... t) {
        if (t != null && t.length > 0) {
            BaseCacheData baseCacheData = GlobalCache.getDataStructure().getCache(globalDataType);
            T r = t[0];
            String rediskey = baseCacheData.getRediskey(r);
            RedisTool.getInstance().opsForHash().delete(getRedisTableMapKey(globalDataType), rediskey);
            List<String> indexKeys = baseCacheData.getIndexKeys(r);
            if (CollectionUtil.isNotEmpty(indexKeys)) {
                for (String indexKey : indexKeys) {
                    RedisTool.getInstance().opsForHash().delete(getRedisTableMapKey(globalDataType), indexKey);
                }
            }
        }
    }


}

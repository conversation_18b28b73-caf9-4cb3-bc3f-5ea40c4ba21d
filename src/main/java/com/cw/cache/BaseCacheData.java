package com.cw.cache;


import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.cw.cache.config.RCaffeine;
import com.cw.cache.redisimpl.redisimpl.RedisGlobalDataCache;
import com.cw.utils.enums.GlobalDataType;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import org.springframework.cache.Cache;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.ScanOptions;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/12/1 10:17
 **/
public abstract class BaseCacheData<T> {
    //protected ConcurrentMap<String, ConcurrentMap<String, T>> datas = new ConcurrentHashMap<>();  //提供  <项目ID, 代码,表记录的存储结构>
    protected String defaultCodeFiled = "code";
    protected String defaultDescFiled = "desc";
    //protected ConcurrentMap<String, T> oneRecordMap = new ConcurrentHashMap<>(); //孤儿.类似 Section,Interfaceconf 这种只有一条的记录.可以用这个.
    //protected Table<String, String, List<T>> groupdatas = HashBasedTable.create();// ConcurrentMap<String, List<T>> groupdatas = new ConcurrentHashMap<>();//分组的数据.提供给 subattr 这种表用的

    //protected SystemUtil.GlobalDataType type;


    /**
     * 根据数据唯一记录获取表数据
     *
     * @param hotelId
     * @param key
     * @return
     */
    public T getRecord(String hotelId, String key) {
        //先优先从cafineCache 中获取数据.获取不到.请求redis cache 同步到本地.然后从redis cache 中获取
        ConcurrentMap<String, T> datas = fetchHotelCacheDataMap(hotelId);
        return datas.getOrDefault(key == null ? "" : key, null);
    }

    /**
     * get 开头的方法代表先从一级缓存中  获取
     *从缓存中获取该酒店全部数据的方法
     * @return
     */
    public List<T> getDataList(String hotelId) {
        ConcurrentMap<String, T> datas = fetchHotelCacheDataMap(hotelId);
        List<T> list = Lists.newArrayList(datas.values());
        sortList(list);
        return list;
    }

    /**
     * 条件获取
     *
     * @param hotelId
     * @param condition
     * @return
     */
    public List<T> getDataListWithCondition(String hotelId, Predicate<T> condition) {
        List<T> list = getDataList(hotelId).stream().filter(condition).collect(Collectors.toList());
        return list;
    }

    /**
     * 条件获取单条记录...能直接按KEY 获取到的.尽量不用这个.会扫描redis .效率没有直接按KEY获取效率高
     *
     * @param hotelId
     * @param condition
     * @return
     */
    public T getRecordWithCondition(String hotelId, Predicate<T> condition) {
        return getDataListWithCondition(hotelId, condition).stream().findFirst().orElse(null);
    }

    /**
     * 查询一二级缓存.进行数据查找
     *
     * @param hotelId
     * @return
     */
    protected ConcurrentMap<String, T> fetchHotelCacheDataMap(String hotelId) {
        Cache cache = RCaffeine.getCache(getMyType());
        Stopwatch stopwatch = Stopwatch.createStarted();
        ConcurrentMap<String, T> datas = (ConcurrentMap<String, T>) cache.get(hotelId, ConcurrentHashMap.class);
        if (datas == null) {
            List<T> redisRows = loadRedisHotelData(hotelId);
            datas = redisRows.stream().collect(Collectors.toConcurrentMap(this::getUniqueKey, Function.identity()));
            cache.put(hotelId, datas);
            //System.out.println("本地缓存中没有数据.重新加载REDIS数据." + getMyType() + hotelId + "数据量:" + datas.size() + " 耗时:" + stopwatch.stop());
            return datas;
        } else {
            //System.out.println("本地缓存中有数据.直接返回." + getMyType() + hotelId + "数据量:" + datas.size() + " 耗时:" + stopwatch.stop());
            return datas;
        }
    }

    protected T getUniqueRecord(String uniqueKey) {

        Cache cache = RCaffeine.getUniqueCache(getMyType());
        T t = (T) cache.get(
                uniqueKey,
                getCacheTableClass()
        );
        if (t == null) {
            Stopwatch stopwatch = Stopwatch.createStarted();
            t = loadRedisRecord(uniqueKey);

            if (t != null) {
                cache.put(uniqueKey, t);
                System.out.println("本地缓存中没有数据.重新加载REDIS数据." + getMyType() + uniqueKey + " 耗时:" + stopwatch.stop());
                return t;
            }
        } else {
            System.out.println("本地缓存中有数据.直接返回." + getMyType() + uniqueKey);
            return t;
        }
        return null;
    }


    private T loadRedisRecord(String uniqueKey) {
        Object o = RedisTool.getInstance().opsForHash().get(RedisGlobalDataCache.getRedisTableMapKey(getMyType()), uniqueKey);
        if (o != null) {        //按redis 直接读取数据r
            return (T) JSON.parseObject(o.toString(), getCacheTableClass());
        }
        return null;
    }

    protected void fillDataDefined(List<T> redisRow, ConcurrentMap<String, T> datas) {
        //默认什么都不做.提供一个本地缓存的MAP. 方便一些特殊的数据处理
        //比如接口配置.有的是根据appid 来跟外部接口交互的.有些时候是根据接口表的vtype 来交互
        //这种情况需要两个特征都缓存

        //简单的来说就是一个表 可以缓存sqlid 为唯一key的数据.也可以缓存hotelid+bizcode 作为唯一key 的数据

    }


    /**
     * load 开头的方法代表读取二级redis 缓存数据
     *
     * 从redis 缓存中获取该酒店全部数据的方法
     * @param hotelId
     * @return
     */
    protected List<T> loadRedisHotelData(String hotelId) {
        String patternKey = hotelId + ":*";
        ScanOptions options = ScanOptions.scanOptions()
                .match(patternKey).build();
        List<String> projectData = Lists.newArrayList();
        Cursor<Map.Entry<String, String>> cursor = RedisTool.getInstance().opsForHash().scan(RedisGlobalDataCache.getRedisTableMapKey(getMyType()), options);
        while (cursor.hasNext()) {
            projectData.add(cursor.next().getValue());
        }
        try {
            cursor.close();
        } catch (IOException e) {
        }
        List<T> ls = (List<T>) JSON.parseArray(StrUtil.wrap(StrUtil.join(",", projectData), "[", "]"), getCacheTableClass());
        return ls;
    }

    /**
     * 返回分组 list 数据
     *
     * @param key
     * @return
     */
    public List<T> getGroupList(String hotelId, String key) {
        List<T> list = null;
        return list;
    }



    /**
     * 获取数据.重新填充map.交给子类填充实现
     */
    protected void reFreshLocalCaffineCacheHotelData(String hotelId) {
        Cache cache = RCaffeine.getCache(getMyType());
        ConcurrentMap<String, T> datas = (ConcurrentMap<String, T>) cache.get(hotelId, ConcurrentHashMap.class);
        if (datas != null) {//本地包含有该表的数据缓存.才会从redis 中同步最新的.否则就跳过处理.不刷新
            datas = loadRedisHotelData(hotelId).stream().collect(Collectors.toConcurrentMap(this::getUniqueKey, Function.identity()));
            cache.put(hotelId, datas);
        }
    }

    /**
     * 返回当前缓存类缓存的表在单个酒店中的唯一key
     * @param t
     * @return
     */
    public abstract String getUniqueKey(T t);

    /**
     * 返回当前缓存类缓存的表在redis中的key
     *
     * @param t
     * @return
     */
    public abstract String getRediskey(T t);


    /**
     * 返回多个redis 内需要缓存的数据索引
     *
     * @param t
     * @return
     */
    public List<String> getIndexKeys(T t) {
        return null;
    }

    /**
     * 返回当前缓存类缓存的表
     *
     * @return
     */
    public Class<?> getCacheTableClass() {
        CacheProp cacheProp = this.getClass().getAnnotation(CacheProp.class);
        return cacheProp == null ? null : cacheProp.cacheClass();
    }

    protected GlobalDataType getMyType() {
        CacheProp cacheProp = this.getClass().getAnnotation(CacheProp.class);
        return cacheProp == null ? null : cacheProp.dataType();
    }


    /**
     * 提供给子类重载自定义排序方法
     */
    protected void sortList(List<T> list) {

    }




}

package com.cw.cache;

import cn.hutool.core.lang.ClassScanner;
import com.cw.utils.enums.GlobalDataType;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2020/10/26 17:00
 **/
@Slf4j
public class BaseDataStructure {
    protected ConcurrentHashMap<GlobalDataType, BaseCacheData> cacheMap = new ConcurrentHashMap<>();

    protected void scanPackage(String cacheUnitPath) {
        //根据 包的路径.将请求类加载到map 缓存
        ClassScanner.scanPackageByAnnotation(cacheUnitPath, CacheProp.class).forEach(clazz -> {
            GlobalDataType globalDataType = clazz.getAnnotation(CacheProp.class).dataType();
            try {
                cacheMap.put(globalDataType, (BaseCacheData) clazz.getDeclaredConstructor().newInstance());
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        log.info("{} 缓存表加载完成", cacheMap.values().size());
    }

    /**
     * 注意:这个只会刷新本地缓存
     *
     * @param type
     */
    public void refreshDataStructure(GlobalDataType type, String hotelId) {
        //刷新单表数据
        BaseCacheData cacheData = cacheMap.get(type);
        if (cacheData != null) {
            //如果当前jvm 内存有一级缓存的话.就刷新一级缓存
            cacheData.reFreshLocalCaffineCacheHotelData(hotelId);
        }
    }

    public <T extends BaseCacheData> T getCache(GlobalDataType type) {
        return (T) cacheMap.get(type);
    }

}

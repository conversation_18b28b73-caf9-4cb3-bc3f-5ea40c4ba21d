package com.cw.controller;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;


/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/9/3 11:49
 **/
@RestController
@RequestMapping(value = "/health")
public class HealthController {
    private static final String HEALTH_MSG = "OK";
    private static final String NOTREADY = "FAIL";
    public static boolean HEALTH = false;

    /**
     * 服务健康心跳检查暴露方法
     *
     * @param response
     * @return
     */
    @RequestMapping(value = "/check", method = {RequestMethod.HEAD})
    public String checkHealth(HttpServletResponse response) {
        if (!HEALTH) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            try {
                response.getWriter().write(NOTREADY);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return HEALTH_MSG;
    }


}

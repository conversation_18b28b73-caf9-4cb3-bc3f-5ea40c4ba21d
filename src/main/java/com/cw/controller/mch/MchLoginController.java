package com.cw.controller.mch;

import cn.dev33.satoken.stp.StpUtil;
import com.cw.config.satoken.MchStpUtil;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.pojo.dto.mch.req.MchLoginOutReq;
import com.cw.pojo.dto.mch.req.MchUserHotelInfoReq;
import com.cw.pojo.dto.mch.req.MchUserSwitchHotelReq;
import com.cw.pojo.dto.mch.res.MchUserHotelInfo;
import com.cw.pojo.dto.mch.res.MchUserResult;
import com.cw.pojo.dto.pms.req.mch.MchUserLoginReq;
import com.cw.service.mch.MchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Leung
 * @Descripstion
 * @Create 2025/6/24 16:20
 **/
@Slf4j
@RestController
@RequestMapping(value = "/pmsmchapi/user", method = RequestMethod.POST)
@Api(tags = "商家端集团登录操作入口")
public class MchLoginController {


    @Autowired
    MchService mchService;


    @ApiOperation(value = "获取商家集团运营关联酒店列表", notes = "获取商家集团运营关联酒店列表")
    @RequestMapping(value = "/getUserHotelList")
    public ResultJson<List<MchUserHotelInfo>> getUserHotelList(@Valid @RequestBody MchUserHotelInfoReq req) {
        List<MchUserHotelInfo> mchUserHotelInfos = new ArrayList<>();
        mchUserHotelInfos = mchService.getUserHotelList(req);
        return ResultJson.ok().data(mchUserHotelInfos);
    }


    @ApiOperation(value = "商家集团运营手机号密码登录", notes = "用户手机号密码登录，返回token，token携带门店")
    @RequestMapping(value = "/login")
    public ResultJson<MchUserResult> login(@Valid @RequestBody MchUserLoginReq mchUserLoginReq) {
        MchUserResult userResult = mchService.loginMchUser(mchUserLoginReq);
        return ResultJson.ok().data(userResult);
    }


    @ApiOperation(value = "商家集团运营切换门店")
    @RequestMapping(value = "/login_switch_hotel")
    public ResultJson<MchUserResult> login(@Valid @RequestBody MchUserSwitchHotelReq req) {
        MchUserResult userResult = mchService.loginSwitchHotel(req);
        return ResultJson.ok().data(userResult);

    }


    // @ApiOperation(value = "商家集团运营每日统计")
    // @RequestMapping(value = "/daily_stat")
    // public ResultJson<MchUserResult> dailyStat(@Valid @RequestBody MchUserSwitchHotelReq req) {
    //     MchUserResult userResult = mchService.getDailyStat(req);
    //     return ResultJson.ok().data(userResult);
    //
    // }
    //
    //
    // @ApiOperation(value = "商家集团运营数据概括")
    // @RequestMapping(value = "/stat_")
    // public ResultJson<MchUserResult> dailyStat(@Valid @RequestBody MchUserSwitchHotelReq req) {
    //     MchUserResult userResult = mchService.getDailyStat(req);
    //     return ResultJson.ok().data(userResult);
    //
    // }


    @ApiOperation(value = "集团运营用户登出")
    @RequestMapping(value = "/logout")
    public ResultJson<Common_response> logout(@Valid @RequestBody MchLoginOutReq req) {
        if (req.getType() == 0) {
            MchStpUtil.logout();
        } else {
            StpUtil.logout();
        }

        return ResultJson.ok().data(new Common_response());
    }

}

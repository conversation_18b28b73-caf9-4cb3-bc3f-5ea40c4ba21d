package com.cw.controller.mch;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.app.res.statistics.StatDataBoardRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2025/6/25 14:08
 */
@Slf4j
@RestController
@RequestMapping(value = "/pmsmchapi/stat", method = RequestMethod.POST)
@Api(tags = "集团运营统计查看入口")
public class MchOpController {

    @ApiOperation(value = "查询统计数据看板")
    @PostMapping(value = "/data-board")
    public ResultJson<StatDataBoardRes> listDataBoard() {
        // StatDataBoardRes statisticsRes = statisticsService.listDataBoard();
        return ResultJson.ok();
    }
}

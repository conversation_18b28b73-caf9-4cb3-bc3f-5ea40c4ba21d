package com.cw.controller.open;

import com.cw.core.vendor.msg.wxcp.WxCpVendor;
import com.cw.exception.DefinedException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Descripstion 企业微信授权登录回调Controller
 * @Create 2025/5/20 09:00
 **/
@RestController
@RequestMapping("/wxcp/auth")
public class WxCpAuthController {

    @Autowired
    private WxCpVendor wxCpVendor;

    /**
     * 企业微信静默授权回调接口
     *
     * @param code  授权码
     * @param state 附加状态参数
     * @return 重定向到前端H5页面或返回数据
     */
    @GetMapping("/callback")
    public String handleAuthCallback(@RequestParam("code") String code, @RequestParam(value = "state", required = false) String state) {
        String userId = null;
        try {
            // TODO: 从请求或其他地方获取 hotelId
            String hotelId = "YOUR_HOTEL_ID"; // 需要替换成实际获取 hotelId 的逻辑

            // 1. 根据code获取企业微信用户ID
            userId = wxCpVendor.getUserIdByCode(hotelId, code);
            System.out.println("成功获取企业微信用户ID: " + userId);

            // 2. TODO: 根据企业微信 userId 查找内部员工信息
            // 例如: Employee employee = employeeService.findEmployeeByWxCpUserId(userId);

            // 3. TODO: 根据员工信息查询工单数据
            // 例如: List<Order> orders = orderService.getOrdersByEmployee(employee);

            // 4. TODO: 将员工和工单信息传递给前端H5页面，或者生成token并重定向
            // 例如: return "redirect:/path/to/your/h5?userId=" + userId; // 简单重定向示例

            return "企业微信用户ID: " + userId + ", 授权成功，请实现后续业务逻辑。"; // 临时返回信息

        } catch (DefinedException e) {
            // TODO: 记录错误日志，并返回错误信息或重定向到错误页面
            e.printStackTrace();
            return "授权失败: " + e.getMessage();
        } catch (Exception e) {
            // TODO: 处理其他潜在异常
            e.printStackTrace();
            return "发生未知错误: " + e.getMessage();
        }
    }

    // TODO: 可能需要其他接口，例如一个生成授权URL的接口供前端调用
    // @GetMapping("/generateAuthUrl")
    // public String generateAuthUrl(@RequestParam("redirectUri") String redirectUri, @RequestParam(value = "state", required = false) String state) {
    //     try {
    //         String hotelId = "YOUR_HOTEL_ID"; // 需要替换成实际获取 hotelId 的逻辑
    //         return wxCpVendor.buildSilentAuthUrl(hotelId, redirectUri, state);
    //     } catch (DefinedException e) {
    //         e.printStackTrace();
    //         return "生成授权URL失败: " + e.getMessage();
    //     } catch (Exception e) {
    //         e.printStackTrace();
    //         return "生成授权URL失败: " + e.getMessage();
    //     }
    // }
} 
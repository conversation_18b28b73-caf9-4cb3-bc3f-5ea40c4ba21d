package com.cw.controller.open;

import com.alibaba.fastjson.JSON;
import com.cw.core.platform.wechat.wxpay.WxNotifyResult;
import com.cw.core.vendor.pay.ali.AliPayVendor;
import com.cw.pojo.notify.ali.AliAntMsg;
import com.cw.service.app.AppPayService;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyResponse;
import com.github.binarywang.wxpay.exception.WxPayException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/11/4 10:53
 **/
@RestController
@RequestMapping(value = "/pay/notify", method = RequestMethod.POST)
public class PayNotifyController {
    @Autowired
    AppPayService userPayService;


    /**
     * 微信支付结果通知
     *
     * @param appid
     * @param v3NotifyData
     * @param request
     * @return
     * @throws WxPayException
     */
    @PostMapping("/jsapi/order/{appid}")
    public WxNotifyResult parseOrderNotifyResult(@PathVariable String appid, @RequestBody String v3NotifyData, HttpServletRequest request) throws WxPayException {
        WxNotifyResult notifyResult = userPayService.handleWxJsApiOrderPayNotify(appid, v3NotifyData, request);
        //支付结果通知
        return notifyResult;
    }

    /**
     * 微信退款结果通知
     *
     * @param appid
     * @param v3NotifyData
     * @param request
     * @return
     * @throws WxPayException
     */
    @PostMapping("/jsapi/refund/{appid}")
    public WxNotifyResult parseRefundNotifyResult(@PathVariable String appid, @RequestBody String v3NotifyData, HttpServletRequest request) throws WxPayException {
//        final WxPayRefundNotifyResult result = this.wxService.parseRefundNotifyResult(xmlData);
        WxNotifyResult notifyResult = userPayService.handleWxJsApiRefundNotify(appid, v3NotifyData, request);
        //退款结果通知
        return notifyResult;
    }


    @PostMapping("/alipay/order/{appid}")
    public String parseAliPayNotifyResult(@PathVariable String appid, @RequestBody String body, HttpServletRequest request) throws WxPayException {
        LoggerFactory.getLogger(this.getClass()).info("收到支付宝支付成功通知: {}", body);

        userPayService.handlerAliPayNotify(appid, body, request);
        return "success";
    }

    @PostMapping("/alipay/passby/{appid}")
    public String parseAliPayPassByNotifyResult(@PathVariable String appid, @RequestBody String body, HttpServletRequest request) throws WxPayException {
        LoggerFactory.getLogger(this.getClass()).info("收到支付宝路人支付成功通知: {}", body);

        //userPayService.handlerPassbyAliPayNotify(appid, body, request);
        return "success";
    }

    @PostMapping("/alipay/passbyrefund/{appid}")
    public String parseAliPassByRefundNotifyResult(@PathVariable String appid, @RequestBody String body, HttpServletRequest request) throws WxPayException {
        LoggerFactory.getLogger(this.getClass()).info("收到支付宝路人退款成功通知: {}", body);

        //userPayService.handleAliPassByRefundNotify(appid, body, request);
        return "success";
    }

    /**
     * 支付宝退款结果通知
     *
     * @param appid
     * @param body
     * @param request
     * @return
     * @throws WxPayException
     */
    @PostMapping("/alipay/ant/{appid}")
    public String parseAliPayRefundNotifyResult(@PathVariable String appid, @RequestBody String body, HttpServletRequest request) throws WxPayException {
        LoggerFactory.getLogger(this.getClass()).info("收到支付宝蚂蚁通知: {}", body);

        Map<String, String> params = AliPayVendor.convertRequestParamsToMap(request);
        String paramsJson = JSON.toJSONString(params);
        AliAntMsg aliAntMsg = JSON.parseObject(paramsJson, AliAntMsg.class);

        String method = aliAntMsg.getMsg_method();
        if (method.equals(AliAntMsg.REFUNDMETHOD)) {
            userPayService.handleAliPayRefundNotify(appid, body, request);
        }


        return "success";
    }

    @PostMapping("/wxscanpay")
    public String parseScanPayNotifyResult(String xmlData) throws WxPayException {
//        final WxScanPayNotifyResult result = this.wxService.parseScanPayNotifyResult(xmlData);
        // TODO 根据自己业务场景需要构造返回对象
        return WxPayNotifyResponse.success("成功");
    }

    @GetMapping("/wxopenvalidate/{appid}")
    public String wxopenvalidate(@PathVariable String appid, String signature, String timestamp, String nonce, String echostr, HttpServletRequest request) throws WxPayException {
//        final WxScanPayNotifyResult result = this.wxService.parseScanPayNotifyResult(xmlData);
        Logger logger = LoggerFactory.getLogger(this.getClass());
        logger.info("接收微信公众号验证:{},{},{},{},{}", appid, signature, timestamp, nonce, echostr);

        return echostr;
    }


}

package com.cw.controller.app;

import com.cw.core.CoreAssign;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.pms.req.room.QueryLiveInRoomReq;
import com.cw.pojo.dto.pms.req.room.QueryRegisterRoomReq;
import com.cw.pojo.dto.pms.res.room.LiveInRoomListRes;
import com.cw.pojo.dto.pms.res.room.RoomListRes;
import com.cw.service.config.room.RoomService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @Description: 前台接待-房间控制器
 * @Author: michael.pan
 * @Date: 2024/5/30 23:13
 */
@RestController
@RequestMapping(value = "/app-api/room")
@Api(tags = "前台接待-房间")
public class RoomMsgController {
    @Resource
    private RoomService roomService;

    @Resource
    private CoreAssign coreAssign;

    @ApiOperation(value = "查询可预定的房间列表")
    @PostMapping(value = "/list")
    public ResultJson<RoomListRes> listRegisterRooms(@Valid @RequestBody QueryRegisterRoomReq queryRoomReq) {
        RoomListRes roomListRes = coreAssign.listRegisterRooms(queryRoomReq);
        return ResultJson.ok().data(roomListRes);
    }

    @ApiOperation(value = "查询在住的房间列表")
    @PostMapping(value = "/list-locc")
    public ResultJson<LiveInRoomListRes> listLoccRooms(@Valid @RequestBody QueryLiveInRoomReq queryLiveInRoomReq) {
        LiveInRoomListRes liveInRoomListRes = roomService.listLiveInRooms(queryLiveInRoomReq);
        return ResultJson.ok().data(liveInRoomListRes);
    }
}

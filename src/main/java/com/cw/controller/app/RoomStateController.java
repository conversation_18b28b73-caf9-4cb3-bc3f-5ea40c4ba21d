package com.cw.controller.app;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.pms.req.room.*;
import com.cw.pojo.dto.pms.res.room.*;
import com.cw.service.config.room.RoomService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @Description: 房态控制器
 * @Author: michael.pan
 * @Date: 2024/5/25 13:36
 */
@RestController
@RequestMapping(value = "/app-api/room-state")
@Api(tags = "前台接待-房态")
public class RoomStateController {
    @Resource
    private RoomService roomService;

    @ApiOperation(value = "实时房态列表")
    @PostMapping(value = "/list")
    public ResultJson<RoomListRes> listRoomStates(@Valid @RequestBody QueryRoomStateReq queryRoomStateReq) {
        RoomListRes roomListRes = roomService.listRoomStates(queryRoomStateReq);
        // 切换到结合Rmstat的查询方式
//        RoomListRes roomListRes = roomService.listRoomStatesAndRmstat(queryRoomStateReq);
        return ResultJson.ok().data(roomListRes);
    }

    @ApiOperation(value = "置净房态列表")
    @PostMapping(value = "/setclean-list")
    public ResultJson<RoomListRes> listOperationRoomStates(@Valid @RequestBody QueryOperationRoomStateReq queryOperationRoomStateReq) {
        RoomListRes roomListRes = roomService.listOperationRoomStates(queryOperationRoomStateReq);
        return ResultJson.ok().data(roomListRes);
    }

    @ApiOperation(value = "实时房态总数列表")
    @PostMapping(value = "/data")
    public ResultJson<CurrentRoomStateRes> listRoomStates(@Valid @RequestBody QueryOperationRoomStatedataReq queryOperationRoomStatedataReq) {
        CurrentRoomStateRes roomStatusRes = roomService.listCurrentRoomStatesData(queryOperationRoomStatedataReq);
        return ResultJson.ok().data(roomStatusRes);
    }

    @ApiOperation(value = "远期房态可卖表")
    @PostMapping(value = "/future-avlstatus")
    public ResultJson<FutureRoomStateRes> listFutureNoPickupRoomStates(@Valid @RequestBody QueryFutureRoomStateReq queryFutureRoomStateReq) {
        FutureRoomStateRes roomListRes = roomService.listFutureRoomStates(queryFutureRoomStateReq);
        return ResultJson.ok().data(roomListRes);
    }

    @ApiOperation(value = "房态操作")
    @PostMapping(value = "/operate")
    public ResultJson operateRoomState(@Valid @RequestBody OperateRoomStateReq roomStatue) {
        roomService.operateRoomState(roomStatue);
        return ResultJson.ok().data(null);
    }
}

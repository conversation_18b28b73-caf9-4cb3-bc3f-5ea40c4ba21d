package com.cw.controller.app;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.app.req.statistics.QueryStatGuestReq;
import com.cw.pojo.dto.app.req.statistics.QueryStatIncomeReq;
import com.cw.pojo.dto.app.req.statistics.QueryStatRoomReq;
import com.cw.pojo.dto.app.req.statistics.QueryStatStandardGroupReq;
import com.cw.pojo.dto.app.res.statistics.*;
import com.cw.service.config.statistics.StatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Date 2024/6/16 10:05
 * @Description 统计
 **/
@RestController
@RequestMapping(value = "/app-api/statistics")
@Api(tags = "前台接待-统计")
public class StatisticsController {

    @Resource
    private StatisticsService statisticsService;

    @ApiOperation(value = "查询统计数据看板")
    @PostMapping(value = "/data-board")
    public ResultJson<StatDataBoardRes> listDataBoard() {
        StatDataBoardRes statisticsRes = statisticsService.listDataBoard();
        return ResultJson.ok().data(statisticsRes);
    }

    @ApiOperation(value = "当日接待客人")
    @PostMapping(value = "/today-guests")
    public ResultJson<StatGuestListRes> listStatGuest(@Valid @RequestBody QueryStatGuestReq queryAccountReq) {
        StatGuestListRes accountListRes = statisticsService.listStatGuests(queryAccountReq);
        return ResultJson.ok().data(accountListRes);
    }

    @ApiOperation(value = "当日收入明细")
    @PostMapping(value = "/today_income")
    public ResultJson<StatIncomesListRes> listIncome(@Valid @RequestBody QueryStatIncomeReq queryStatIncomeReq) {
        StatIncomesListRes statIncomesRes = statisticsService.listIncome(queryStatIncomeReq);
        return ResultJson.ok().data(statIncomesRes);
    }

    @ApiOperation(value = "当日脏房管理")
    @PostMapping(value = "/today-rooms")
    public ResultJson<StatRoomListRes> listStatRoom(@Valid @RequestBody QueryStatRoomReq queryStatRoomReq) {
        StatRoomListRes roomRes = statisticsService.listStatRooms(queryStatRoomReq);
        return ResultJson.ok().data(roomRes);
    }

    @ApiOperation(value = "当日接待团队")
    @PostMapping(value = "/today-groups")
    public ResultJson<StatStandardGroupListRes> listStatStandardGroup(@Valid @RequestBody QueryStatStandardGroupReq queryStatStandardGroupReq) {
        StatStandardGroupListRes statStandardGroupRes = statisticsService.listStatStandardGroup(queryStatStandardGroupReq);
        return ResultJson.ok().data(statStandardGroupRes);
    }

}

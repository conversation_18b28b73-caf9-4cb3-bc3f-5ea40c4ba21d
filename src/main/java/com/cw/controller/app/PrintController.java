package com.cw.controller.app;

import cn.hutool.core.date.DateUtil;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.CommonQueryReq;
import com.cw.pojo.dto.common.req.CommonRpPrintReq;
import com.cw.pojo.dto.pms.res.report.ReportListRes;
import com.cw.report.enums.ExportType;
import com.cw.service.config.print.PrintService;
import com.cw.service.config.print.folio.service.FolioService;
import com.cw.service.config.print.impl.*;
import com.cw.service.config.print.report.service.ReportService;
import com.cw.service.config.print.report.service.ReportServiceManager;
import com.cw.service.context.GlobalContext;
import com.cw.utils.CalculateDate;
import com.cw.utils.SpringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * @Classname PrintController
 * @Description 打印控制器
 * @Date 2024-12-16 19:31
 * <AUTHOR> sancho.shen
 */
@RestController
@RequestMapping(value = "/app-api/print")
@Api(tags = "前台接待-打印")
public class PrintController {

    @Resource
    private PrintService printService;

    @Resource
    private FolioService folioService;

    @Resource
    private ReportServiceManager reportServiceManager;

    @Resource
    private PrintGuestBillingWTImpl printGuestBillingWT;

    @Resource
    private PrintRCWTImpl printRCWT;

    @ApiOperation(value = "打印入住登记表")
    @GetMapping(value = "/excel/checkin")
    public ResponseEntity<InputStreamResource> printCheckin(@RequestParam String reservationNumber) {
        // 生成 Excel 文件
        byte[] excelBytes = printService.printCheckin(reservationNumber);

        // 创建 InputStreamResource
        InputStreamResource resource = new InputStreamResource(new ByteArrayInputStream(excelBytes));

        // 设置响应头
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=入住登记表.xlsx");

        // 返回响应实体
        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                .body(resource);
    }

    @ApiOperation(value = "打印宾客账单表")
    @GetMapping(value = "/excel/bill")
    public ResponseEntity<byte[]> printBill(@RequestParam String reservationNumber) throws Exception {
        // 生成 Excel 文件


        //reservationNumber="2024120207005";

        ExportType exportType = ExportType.PDF;
        String fileName = "宾客账单" + reservationNumber;
        byte[] reportData;
//		byte[] reportData = SpringUtil.getBean(PrintGuestBillingJasperImpl.class).print("2024112553005");// generateReport(format);
//		byte[] reportData = SpringUtil.getBean(PrintGuestBillingDetailJasperImpl.class).printDetail(new GuestBillingDetailReq().setExportFormat(format).setHedgingOnly(true));
        // PringTBalanceReportImpl, PringDailyReportImpl
//		byte[] reportData = SpringUtil.getBean(PringPkgReportImpl.class).print(format, DateUtil.offsetDay(DateUtil.date(), -1));//
//		byte[] reportData = SpringUtil.getBean(PringTBalanceReportImpl.class).print(format, LocalDate.now().plusDays(-1));//
        {
            reportData = folioService.getBytesPrint(reservationNumber, exportType);//

        }
        HttpHeaders headers = new HttpHeaders();
        if (exportType == ExportType.PDF) {
            headers.setContentType(MediaType.APPLICATION_PDF);
        } else if (exportType == ExportType.HTML) {
            headers.setContentType(MediaType.TEXT_HTML);
        } else if (exportType == ExportType.XLS) {
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        }
        headers.setContentDispositionFormData("attachment", URLEncoder.encode(fileName, "utf-8") + "." + exportType);
        return ResponseEntity.ok().headers(headers).body(reportData);
    }

    @ApiOperation(value = "获取程序化报表列表")
    @PostMapping(value = "/program/list")
    public ResultJson<ReportListRes> getReportList(@RequestBody CommonQueryReq queryReq) {
        ReportListRes reportListRes = new ReportListRes();
        reportListRes.setGroups(new ArrayList<>());
        ReportListRes.ReportGroup reportGroup = new ReportListRes.ReportGroup();
        reportGroup.setGroupName("全部报表");
        reportGroup.setReports(new ArrayList<>());
        ReportListRes.ReportItem reportItem = new ReportListRes.ReportItem("1", "账项分类明细表", "xlsx");
        ReportListRes.ReportItem reportItem2 = new ReportListRes.ReportItem("2", "收款明细表", "xlsx");
        ReportListRes.ReportItem reportItem3 = new ReportListRes.ReportItem("3", "日营业报表", "xlsx");
        ReportListRes.ReportItem reportItem4 = new ReportListRes.ReportItem("4", "稽核试算平衡表", "xlsx");
        ReportListRes.ReportItem reportItem5 = new ReportListRes.ReportItem("5", "早餐报表", "xlsx");
        ReportListRes.ReportItem reportItem6 = new ReportListRes.ReportItem("6", "稽核底表(日报)", "xlsx");
        //浙江新增
        ReportListRes.ReportItem reportItem7 = new ReportListRes.ReportItem("7", "前台收款明细表", "xlsx");
        ReportListRes.ReportItem reportItem8 = new ReportListRes.ReportItem("8", "前台收款汇总表", "xlsx");
        ReportListRes.ReportItem reportItem9 = new ReportListRes.ReportItem("9", "入账汇总报表", "xlsx");
        ReportListRes.ReportItem reportItem10 = new ReportListRes.ReportItem("10", "销售员业绩汇总表", "xlsx");



        reportGroup.getReports().add(reportItem);
        reportGroup.getReports().add(reportItem2);
        reportGroup.getReports().add(reportItem3);
        reportGroup.getReports().add(reportItem4);
        reportGroup.getReports().add(reportItem5);
        reportGroup.getReports().add(reportItem6);
        reportGroup.getReports().add(reportItem7);
        reportGroup.getReports().add(reportItem8);
        reportGroup.getReports().add(reportItem9);
        reportGroup.getReports().add(reportItem10);


        reportListRes.getGroups().add(reportGroup);
        return ResultJson.ok().data(reportListRes);
    }


    @ApiOperation(value = "程序化报表输出")
    @PostMapping(value = "/program/ouput/file")
    public ResponseEntity<InputStreamResource> programReport(@RequestBody CommonRpPrintReq printReq) throws Exception {
        String format = "xlsx";
        String reportIndex = printReq.getRpId();

        Integer idx = Integer.parseInt(reportIndex);  //临时方案
        byte[] reportData = null;
        String rpName = "报表";

        switch (idx) {
            case 1: //
                reportData = SpringUtil.getBean(PrintGuestBillingDetailJasperImpl.class).printDetail(format,
                        printReq.getStartDate(), printReq.getEndDate());
                rpName = "账项分类明细表";
                break;
            case 2: //
                reportData = SpringUtil.getBean(PrintGuestBillingDetailJasperImpl.class).printCreditDetail(format,
                        printReq.getStartDate(), printReq.getEndDate());
                rpName = "收款明细表";
                break;
            case 3: //
                reportData = SpringUtil.getBean(PringDailyReportImpl.class).print(format, printReq.getStartDate());
                rpName = "营业日报表";
                break;
            case 4: //
                reportData = SpringUtil.getBean(PringTBalanceReportImpl.class).print(format, printReq.getStartDate());
                rpName = "稽核试算平衡表";
                break;
            case 5: //
                reportData = SpringUtil.getBean(PringPkgReportImpl.class).print(format, CalculateDate.asUtilDate(printReq.getStartDate()));
                rpName = "早餐报表";
                break;
            case 6: // 稽核底表(日报) - DailyAuditChecklistReportService
                reportData = generateReportServiceData("DailyAuditChecklist", printReq);
                rpName = "稽核底表(日报)";
                break;
            case 7: // 收款明细报表 - PaymentDetailReportService
                reportData = generateReportServiceData("payment-detail", printReq);
                rpName = "收款明细报表";
                break;
            case 8: // 收款汇总报表 - PaymentSummaryReportService
                reportData = generateReportServiceData("payment-summary", printReq);
                rpName = "收款汇总报表";
                break;
            case 9: // 入账汇总报表 - PostingSummaryReportService
                reportData = generateReportServiceData("posting-summary", printReq);
                rpName = "入账汇总报表";
                break;
            case 10:  // 销售员业绩汇总表 - SalesmanPerformanceSummaryReportService
                reportData = generateReportServiceData("SalesmanPerformanceSummary", printReq);
                rpName = "销售员业绩汇总表";
                break;
            default:
                break;
        }
        rpName += DateUtil.format(LocalDateTime.now(), "yyyyMMddHHmmss");
        //后期要是太慢.就丢到OSS 下载吧

        // 修改这里：对文件名进行 URL 编码
        String encodedFileName = java.net.URLEncoder.encode(rpName + ".xlsx", "UTF-8").replaceAll("\\+", "%20");

        // 创建 InputStreamResource
        InputStreamResource resource = new InputStreamResource(new ByteArrayInputStream(reportData));
        // 设置响应头
        // HttpHeaders headers = new HttpHeaders();
        // headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + rpName + ".xlsx");

        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename*=UTF-8''" + encodedFileName);

        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                .body(resource);
    }




    /**
     * 打印账单(Word版本)
     */
    @ApiOperation(value = "打印宾客账单表(Word版本)")
    @GetMapping("/doc/billing")
    public void printGuestBillingDoc(@RequestParam String reservationNumber, HttpServletResponse response) throws Exception {
        byte[] content = printGuestBillingWT.print(reservationNumber);
        response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        response.setHeader("Content-Disposition", "attachment;filename=guest_billing.docx");
        response.getOutputStream().write(content);
    }

    /**
     * 打印登记卡(Word版本)
     */
    @ApiOperation(value = "打印登记卡(Word版本)")
    @GetMapping("/doc/rc")
    public void printRegistrationCardDoc(@RequestParam String reservationNumber, HttpServletResponse response) throws Exception {
        byte[] content = printRCWT.print(reservationNumber);
        response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        response.setHeader("Content-Disposition", "attachment;filename=registration_card.docx");
        response.getOutputStream().write(content);
    }

    /**
     * 使用ReportServiceManager生成报表数据
     *
     * @param reportName 报表服务名称
     * @param printReq   打印请求参数
     * @return 报表字节数组
     * @throws Exception 生成报表时的异常
     */
    private byte[] generateReportServiceData(String reportName, CommonRpPrintReq printReq) throws Exception {
        ReportService<?> service = reportServiceManager.getReportService(reportName);
        if (service == null) {
            throw new IllegalArgumentException("Report service not found: " + reportName);
        }

        // 构建参数Map
        Map<String, String> rawParameters = new HashMap<>();
        if (printReq.getStartDate() != null) {
            rawParameters.put("fromDate", printReq.getStartDate().toString());
        }
        if (printReq.getEndDate() != null) {
            rawParameters.put("toDate", printReq.getEndDate().toString());
        }
        // 添加默认收银员参数，某些报表需要
        rawParameters.put("cashierId", printReq.getOption());

        // 解析参数
        Map<String, Object> parameters = service.parseParameters(rawParameters);

        // 使用ByteArrayOutputStream生成Excel字节数组
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            // 尝试使用EXCEL作为导出类型，如果不存在则使用PDF
            try {
                service.generateReport(ExportType.XLS, outputStream, parameters);
            } catch (Exception e) {
                // 如果EXCEL不存在，尝试使用XLSX
                try {
                    service.generateReport(com.cw.report.enums.ExportType.PDF, outputStream, parameters);
                } catch (Exception e2) {
                    // 如果都不存在，使用PDF作为后备

                }
            }
            return outputStream.toByteArray();
        }
    }
}

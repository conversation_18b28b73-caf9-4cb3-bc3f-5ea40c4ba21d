package com.cw.controller.app;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.CommonDelReq;
import com.cw.pojo.dto.pms.req.invoice.InvoiceManageReq;
import com.cw.pojo.dto.pms.req.invoice.QueryInvoiceManageReq;
import com.cw.pojo.dto.pms.req.invoice.UpdateInvoiceManageReq;
import com.cw.pojo.dto.pms.res.invoice.InvoiceManageListRes;
import com.cw.service.config.invoice.InvoiceManageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @Description: 发票管理控制器
 * @Author: michael.pan
 * @Date: 2024/3/30 18:07
 */
@RestController
@RequestMapping(value = "/app-api/invoice-management")
@Api(tags = "前台接待-发票管理")
public class InvoiceManageController {

    @Resource
    private InvoiceManageService invoiceManageService;

    @ApiOperation(value = "添加发票")
    @PostMapping(value = "/add")
    public ResultJson addInvoiceManage(@Valid @RequestBody InvoiceManageReq invoiceManageReq) {
        invoiceManageService.addInvoiceManage(invoiceManageReq);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "修改发票")
    @PostMapping(value = "/update")
    public ResultJson updateInvoiceManage(@Valid @RequestBody UpdateInvoiceManageReq updateInvoiceManageReq) {
        invoiceManageService.updateInvoiceManage(updateInvoiceManageReq);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "发票列表")
    @PostMapping(value = "/list")
    public ResultJson<InvoiceManageListRes> listInvoiceManage(@Valid @RequestBody QueryInvoiceManageReq queryInvoiceManageReq) {
        InvoiceManageListRes list = invoiceManageService.listInvoiceManage(queryInvoiceManageReq);
        return ResultJson.ok().data(list);
    }

    @ApiOperation(value = "删除发票")
    @PostMapping(value = "/delete")
    public ResultJson deleteInvoiceManage(@RequestBody CommonDelReq req) {
        invoiceManageService.deleteInvoiceManage(req.getId());
        return ResultJson.ok().data(null);
    }

}

package com.cw.controller.app;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.app.req.AppOnlinePayReq;
import com.cw.pojo.dto.app.res.AppQrScanPayRes;
import com.cw.pojo.dto.app.res.AppQueryPayRes;
import com.cw.pojo.dto.common.req.CommonDelReq;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.pojo.dto.pms.req.accountitem.AppAccountItemReq;
import com.cw.pojo.dto.pms.req.guest.*;
import com.cw.pojo.dto.pms.req.reservation.RoomRsLoadReq;
import com.cw.pojo.dto.pms.res.accountitem.AccountItemListRes;
import com.cw.pojo.dto.pms.res.guest.AccountTotalRes;
import com.cw.pojo.dto.pms.res.guest.ArAccTransferRes;
import com.cw.pojo.dto.pms.res.guest.GuestAccountListRes;
import com.cw.pojo.dto.pms.res.guest.AccountRes;
import com.cw.service.app.AppPayService;
import com.cw.service.config.accountitem.AccountItemService;
import com.cw.service.config.cashier.CashierRoomRateService;
import com.cw.service.config.cashier.CashierService;
import com.cw.service.config.guest.GuestAccountService;
import com.cw.service.context.GlobalContext;
import com.cw.utils.annotion.LockOp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 客人账目控制器
 * @Author: michael.pan
 * @Date: 2024/3/27 22:19
 */
@RestController
@RequestMapping(value = "/app-api/billing")
@Api(tags = "前台接待-客人账目")
public class GuestAccountController {
    @Resource
    private GuestAccountService guestAccountService;

    @Resource
    private AccountItemService accountItemService;

    @Resource
    private CashierService cashierService;

	@Resource
	private CashierRoomRateService cashierRoomRateService;

    @Resource
    private AppPayService appPayService;

    @ApiOperation(value = "客人账目列表")
    @PostMapping(value = "/list")
    public ResultJson<GuestAccountListRes> listGuestAccount(@Valid @RequestBody QueryGuestAccountReq queryGuestAccountReq) {
        GuestAccountListRes list = guestAccountService.listGuestAccount(queryGuestAccountReq);
        return ResultJson.ok().data(list);
    }

    @ApiOperation(value = "消费明细")
    @PostMapping(value = "/consumption_detail")
    public ResultJson<GuestAccountListRes> consumptionDetail(@RequestBody QueryConsumptionDetailReq req) {
        GuestAccountListRes guestAccountListRes = guestAccountService.consumptionDetail(req);
        return ResultJson.ok().data(guestAccountListRes);
    }

    @ApiOperation(value = "收款明细")
    @PostMapping(value = "/payment_detail")
    public ResultJson<GuestAccountListRes> paymentDetail(@RequestBody QueryPaymentDetailReq req) {
        GuestAccountListRes guestAccountListRes = guestAccountService.paymentDetail(req);
        return ResultJson.ok().data(guestAccountListRes);
    }

    @ApiOperation(value = "消费入账")
    @PostMapping(value = "/consumption_account")
    public ResultJson<AccountRes> consumptionAccount(@RequestBody ConsumptionAccountReq req) {
        cashierService.post(GlobalContext.getCurrentHotelId(), req.toCashierPostReq());
        //AccountRes postingAccountRes = guestAccountService.consumptionAccount(req);
        return ResultJson.ok().data(new AccountRes());
    }

    @ApiOperation(value = "付款入账")
    @PostMapping(value = "/payment_account")
    @LockOp
    public ResultJson<AccountRes> paymentAccount(@RequestBody PaymentAccountReq req) throws NoSuchAlgorithmException {
        cashierService.post(GlobalContext.getCurrentHotelId(), req.toCashierPostReq());
        return ResultJson.ok().data(new AccountRes());
    }

    @ApiOperation(value = "撤销入账", notes = "撤销入账.只能撤销当日的账目")
    @PostMapping(value = "/cancel_post")
    public ResultJson<AccountRes> cancelPost(@RequestBody CancelPostAccountReq req) {
        cashierService.refund(GlobalContext.getCurrentHotelId(), req.getAccid(), null, req.getRemark());
        return ResultJson.ok().data(new AccountRes());
    }

    @ApiOperation(value = "查询客人账目总额相关数据")
    @PostMapping(value = "/account_total")
    public ResultJson<AccountTotalRes> queryAccountTotal(@RequestBody QueryAccountTotalReq req) {
        AccountTotalRes accountTotalRes = guestAccountService.queryAccountTotal(req);
        return ResultJson.ok().data(accountTotalRes);
    }


    @ApiOperation(value = "手动出房费")
    @PostMapping(value = "/post_roomrate")
    public ResultJson<Common_response> postRoomRateTotal(@RequestBody RoomRsLoadReq req) {
    	cashierRoomRateService.postRoomRate(GlobalContext.getCurrentHotelId(), req.getReservationNumber(), null);
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperation(value = "预定转账")
    @PostMapping(value = "/transfer")
    public ResultJson<Common_response> transfer(@RequestBody TransferAccountReq req) {
        int count = cashierService.transfer_res(GlobalContext.getCurrentHotelId(), req.getAccids(), req.getResNo(), StrUtil.EMPTY);
        Common_response response = new Common_response();
        response.setMsg(StrUtil.format("转账成功，共转账{}笔", count));
        //转账 暂缺
        return ResultJson.ok().data(response);
    }

    @ApiOperation(value = "批量转账")
    @PostMapping(value = "/batch_transfer")
    public ResultJson<Common_response> batch_transfer(@RequestBody BatchTransferAccountReq req) {
        int count = cashierService.batch_transfer(GlobalContext.getCurrentHotelId(), req);
        Common_response response = new Common_response();
        response.setMsg(StrUtil.format("转账成功，共转账{}笔", count));
        return ResultJson.ok().data(response);
    }


    @ApiOperation(value = "微信付款码支付")
    @PostMapping(value = "/wx_scanpay")
    public ResultJson<AppQrScanPayRes> wxScanPay(@RequestBody AppOnlinePayReq req) throws Exception {
        AppQrScanPayRes res = appPayService.createWxScanPayOrder(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperation(value = "支付宝付款码支付")
    @PostMapping(value = "/ali_scanpay")
    public ResultJson aliScanPay(@RequestBody AppOnlinePayReq req) throws Exception {
        AppQrScanPayRes res = appPayService.createALiScanPayOrder(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperation(value = "支付查询")
    @PostMapping(value = "/query_onlinepay")
    public ResultJson<AppQueryPayRes> queryPay(@RequestBody AppOnlinePayReq req) throws Exception {
        AppQueryPayRes res = appPayService.queryScanPayOrder(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperation(value = "获取消费账务中对应的账项列表")
    @PostMapping(value = "/query_accountitem")
    public ResultJson<AccountItemListRes> list(@Valid @RequestBody AppAccountItemReq appAccountItemReq) {
        AccountItemListRes itemListRes = accountItemService.queryAccountItem(appAccountItemReq);
        return ResultJson.ok().data(itemListRes);
    }

    //    废弃，后续不要直接删除即可
//    @ApiOperation(value = "添加客人账目")
//    @PostMapping(value = "/add")
//    public ResultJson addGuestAccount(@Valid @RequestBody GuestAccountReq guestAccountReq) {
//        guestAccountService.addGuestAccount(guestAccountReq);
//        return ResultJson.ok().data(null);
//    }

//    @ApiOperation(value = "修改客人账目")
//    @PostMapping(value = "/update")
//    public ResultJson updateGuestAccount(@Valid @RequestBody UpdateGuestAccountReq updateGuestAccountReq) {
//        guestAccountService.updateGuestAccount(updateGuestAccountReq);
//        return ResultJson.ok().data(null);
//    }
//    @ApiOperation(value = "删除客人账目")
//    @PostMapping(value = "/delete")
//    public ResultJson deleteGuestAccount(@RequestBody CommonDelReq req) {
//        guestAccountService.deleteGuestAccount(req.getId());
//        return ResultJson.ok().data(null);
//    }
}

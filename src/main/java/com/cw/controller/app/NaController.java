package com.cw.controller.app;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.pms.res.na.res.NaProgress_Res;
import com.cw.service.config.nightaudit.NightAuditService;
import com.cw.service.context.GlobalContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2024/9/11 12:19
 */
@RestController
@RequestMapping(value = "/app-api/na")
@Api(tags = "前台夜审")
public class NaController {

    @Autowired
    NightAuditService nightAuditService;

    @ApiOperation(value = "获取夜审信息", notes = "获取夜审信息")
    @RequestMapping(value = "/naInfo", method = RequestMethod.POST)
    public ResultJson<NaProgress_Res> naInfo() {
        String hotelId = GlobalContext.getCurrentHotelId();
        return ResultJson.ok().data(nightAuditService.getNaRunInfoAndProgress(hotelId));
    }

    @ApiOperation(value = "夜审设置 - 重设夜审", notes = "重设夜审")
    @RequestMapping(value = "/refresh_naStatus", method = RequestMethod.POST)
    public ResultJson refresh_naStatus() {
        nightAuditService.resetDailyTask();
        return ResultJson.ok();
    }
}

package com.cw.controller.app;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.CommonDelReq;
import com.cw.pojo.dto.pms.req.profile.ProfileReq;
import com.cw.pojo.dto.pms.req.profile.QueryProfileListReq;
import com.cw.pojo.dto.pms.req.profile.QueryProfileReq;
import com.cw.pojo.dto.pms.req.profile.UpdateProfileReq;
import com.cw.pojo.dto.pms.req.reservation.RsHisListReq;
import com.cw.pojo.dto.pms.res.profile.ProfileListRes;
import com.cw.pojo.dto.pms.res.profile.ProfileRes;
import com.cw.pojo.dto.pms.res.reservation.ReservationHisListRes;
import com.cw.service.config.profile.ProfileService;
import com.cw.service.context.GlobalContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @Classname ProfileController
 * @Description 档案控制器
 * @Date 2024-03-27 20:50
 * <AUTHOR> sancho.shen
 */
@RestController
@RequestMapping(value = "/app-api/profile")
@Api(tags = "前台接待-档案")
public class ProfileController {

    @Resource
    private ProfileService profileService;

   /* @ApiOperation(value = "添加档案")
    @PostMapping(value = "/add")
    public ResultJson<ProfileRes> addProfile(@Valid @RequestBody ProfileReq profileReq) {
        ProfileRes profileRes = profileService.addProfile(profileReq);
        return ResultJson.ok().data(profileRes);
    }*/

    @ApiOperation(value = "添加档案/支持批量,只返回了主入住人的档案信息,集合中第一条数据认为是主入住人")
    @PostMapping(value = "/add")
    public ResultJson<ProfileRes> addProfile(@Valid @RequestBody List<ProfileReq> profileReqList) {
        ProfileRes profileRes = profileService.addProfile(profileReqList, GlobalContext.getCurrentHotelId());
        return ResultJson.ok().data(profileRes);
    }

    @ApiOperation(value = "修改档案")
    @PostMapping(value = "/update")
    public ResultJson updateProfile(@Valid @RequestBody UpdateProfileReq updateIncludeRateReq) {
        profileService.updateProfile(updateIncludeRateReq);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "查询某个档案")
    @PostMapping(value = "/find-profile")
    public ResultJson<ProfileRes> findProfile(@Valid @RequestBody QueryProfileReq queryProfileReq) {
        ProfileRes res = profileService.findProfile(queryProfileReq);
        return ResultJson.ok().data(res);
    }

    @ApiOperation(value = "查询档案列表")
    @PostMapping(value = "/list")
    public ResultJson<ProfileListRes> listProfile(@Valid @RequestBody QueryProfileListReq queryProfileListReq) {
        return ResultJson.ok().data(profileService.listProfile(queryProfileListReq));
    }

    @ApiOperation(value = "删除档案")
    @PostMapping(value = "/delete")
    public ResultJson deleteProfile(@RequestBody CommonDelReq req) {
        profileService.deleteProfile(req.getId());
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "查询历史预订列表")
    @PostMapping(value = "/query-his-list")
    public ResultJson<ReservationHisListRes> queryHisList(@RequestBody RsHisListReq req) {
        return ResultJson.ok().data(profileService.queryHisList(req));
    }

}

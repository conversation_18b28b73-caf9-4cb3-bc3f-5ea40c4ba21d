package com.cw.controller;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import com.alibaba.fastjson.JSONObject;
import com.cw.service.config.base.son.DoorlockServiceImpl;
import com.cw.service.config.base.son.FloorServiceImpl;
import com.cw.service.config.base.son.SalerServiceImpl;

import com.cw.service.mq.MqNameUtils;
import com.cw.service.mq.MsgNotifyer;
import com.cw.service.mq.notifyer.PmsMsgNotifyer;
import com.cw.utils.ProdType;
import com.cw.utils.SystemUtil;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.OpRoleCache;
import com.cw.cache.impl.RoomTypeCache;
import com.cw.cache.impl.VendorConfigCache;
import com.cw.core.CoreAvl;
import com.cw.entity.OpRole;
import com.cw.entity.RoomType;
import com.cw.entity.Vendorconfig;
import com.cw.pojo.common.ResultJson;
import com.cw.utils.CalculateDate;
import com.cw.utils.SpringUtil;
import com.cw.utils.enums.GlobalDataType;
import com.cw.utils.enums.VendorType;
import com.google.common.base.Stopwatch;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/3/12 16:20
 **/
@Slf4j
@RestController
@RequestMapping(value = "/cache", method = RequestMethod.POST)
public class TestCacheController {




    @RequestMapping(value = "/refreshAlltoRedis", method = RequestMethod.GET)
    public ResultJson test() {

        //ResultJson result =
        List<String> hotelids = Arrays.asList(SystemUtil.CONSOLEHOTELID);
        GlobalDataType[] typs = GlobalDataType.values();
        for (String hotelid : hotelids) {
            for (GlobalDataType typ : typs) {
                GlobalCache.getInstance().refreshAndNotify(typ, hotelid);
            }
        }
        log.info("成功刷新缓存");

        return ResultJson.ok();
    }

    @RequestMapping(value = "/testRead", method = RequestMethod.GET)
    public ResultJson read() {

        OpRoleCache opRoleCache = GlobalCache.getDataStructure().getCache(GlobalDataType.USER_ROLE);

        Stopwatch stopwatch = Stopwatch.createStarted();

        OpRole opRole = opRoleCache.getRecord("001", "test12");
        log.info("读取单条记录:  {}  {}", JSON.toJSONString(opRole), stopwatch.stop());


        stopwatch.start();

        List<OpRole> opRoleList = opRoleCache.getDataList("001");
        log.info("读取多条记录: 数据大小{} 耗时{}", opRoleList.size(), stopwatch.stop());


        return ResultJson.ok();
    }

    /**
     * 发起测试请求.获取支付二维码
     *
     * @return
     */
    @RequestMapping(value = "/testpay", method = RequestMethod.GET)
    public ResultJson testPay() {
        //初始化支付服务

        String hotelId = "001";

        RoomTypeCache roomTypeCache = GlobalCache.getDataStructure().getCache(GlobalDataType.ROOMTYPE);
        List<String> rmtypes = roomTypeCache.getDataList(hotelId).stream().map(RoomType::getRoomType).collect(Collectors.toList());
        CoreAvl coreAvl = SpringUtil.getBean(CoreAvl.class);
        try {
            //coreAvl.generateRrooms(hotelId,rmtypes, CalculateDate.stringToDate("2024-06-01"),CalculateDate.stringToDate("2025-12-31"));

            coreAvl.fixAllHotelRoom(hotelId, rmtypes, CalculateDate.stringToDate("2024-06-01"), CalculateDate.stringToDate("2024-12-31"));

        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }

        return ResultJson.ok();
    }

    /**
     * 测试配置缓存
     *
     * @return
     */
    @RequestMapping(value = "/testConfig", method = RequestMethod.GET)
    public ResultJson testConfig() {
        VendorConfigCache cache = GlobalCache.getDataStructure().getCache(GlobalDataType.VENDORCONFIG);

        //Vendorconfig vendorconfig = cache.getRecord("001", "test");
        //log.info("读取单条记录:  {}  {}", JSON.toJSONString(vendorconfig));

        Vendorconfig vendorconfig = cache.getVendorConfig("001", VendorType.ADE_DL);
        log.info("读取单条记录:  {}  {}", JSON.toJSONString(vendorconfig));


        Vendorconfig signalrecord = cache.getRecordWithCondition("001", v -> v.getActive() && v.getVtype().equals(VendorType.ADE_DL.name()));
        log.info("条件读取读取单条记录:  {}  {}", JSON.toJSONString(signalrecord));

        return ResultJson.ok();
    }

    @RequestMapping(value = "/testBaseService", method = RequestMethod.GET)
    public ResultJson testBaseService() {
        FloorServiceImpl floorService = SpringUtil.getBean(FloorServiceImpl.class);
        DoorlockServiceImpl doorlockService = SpringUtil.getBean(DoorlockServiceImpl.class);

        floorService.test();
        doorlockService.test();

        return ResultJson.ok();
    }

    @RequestMapping(value = "/testMq", method = RequestMethod.GET)
    public ResultJson testMq() {
        PmsMsgNotifyer notifyer = PmsMsgNotifyer.getInstance();
        Date start = new Date();
        Date end = CalculateDate.reckonDay(start, 5, 1);
        String roomtype = "TJDRF";
        String hotelId = "001";

        notifyer.notify_RoomRate_ByMq(hotelId, ProdType.ROOM.name(), "SKJ", start, end, roomtype);
        notifyer.notify_Grid_ByMq(hotelId, "", "", ProdType.ROOM.name(), start, end, roomtype);


        return ResultJson.ok();
    }


}

package com.cw.controller.ota;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.pojo.dto.ota.req.*;
import com.cw.pojo.dto.ota.res.*;
import com.cw.pojo.dto.pms.res.building.BuildingListRes;
import com.cw.pojo.dto.pms.res.rate.RoomRateDetailRes;
import com.cw.pojo.dto.pms.res.reservation.RsOrderResult;
import com.cw.pojo.dto.pms.res.roomtype.RoomTypeListRes;
import com.cw.service.config.ota.Pms_OtaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@RequestMapping("/ota-api")
@Api(tags = "OTA对接服务接口")
public class Pms_OtaController {

    @Resource
    private Pms_OtaService otaService;

    @ApiOperation(value = "创建订单")
    @PostMapping(value = "/create-order")
    public ResultJson<RsOrderResult> createOrder(@Valid @RequestBody OtaCreateOrderReq req) throws Exception {
        //RsOrderResult orderResult = otaService.createOrder(req);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "取消订单")
    @PostMapping(value = "/cancel-order")
    public ResultJson<Common_response> cancelOrder(@Valid @RequestBody OtaCancelOrderReq req) throws Exception {
        otaService.cancelOrder(req);
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperation(value = "查询订单")
    @PostMapping(value = "/query-order")
    public ResultJson<RsOrderResult> queryOrder(@Valid @RequestBody OtaQueryOrderReq req) {
        RsOrderResult orderResult = otaService.queryOrder(req);
        return ResultJson.ok().data(orderResult);
    }

    @ApiOperation(value = "支付订单")
    @PostMapping(value = "/pay-order")
    public ResultJson<Common_response> payOrder(@Valid @RequestBody OtaPayOrderReq req) {
        otaService.payOrder(req);
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperation(value = "退款订单")
    @PostMapping(value = "/refund-order")
    public ResultJson<Common_response> refundOrder(@Valid @RequestBody OtaRefundOrderReq req) {
        otaService.refundOrder(req);
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperation(value = "查询楼栋列表")
    @PostMapping(value = "/buildings")
    public ResultJson<BuildingListRes> queryBuildings(@Valid @RequestBody OtaQueryBuildingReq req) {
        BuildingListRes buildingList = otaService.queryBuildings(req);
        return ResultJson.ok().data(buildingList);
    }

    @ApiOperation(value = "查询楼栋下房型")
    @PostMapping(value = "/building-roomtypes")
    public ResultJson<RoomTypeListRes> queryBuildingRoomTypes(@Valid @RequestBody OtaQueryRoomTypeReq req) {
        RoomTypeListRes roomTypeList = otaService.queryBuildingRoomTypes(req);
        return ResultJson.ok().data(roomTypeList);
    }

    @ApiOperation(value = "查询指定日期范围房价")
    @PostMapping(value = "/query-room-rates")
    public ResultJson<RoomRateDetailRes> queryRoomRates(@Valid @RequestBody OtaQueryRateReq req) {
        RoomRateDetailRes rateDetail = otaService.queryRoomRates(req);
        return ResultJson.ok().data(rateDetail);
    }

    @ApiOperation(value = "查询指定日期范围库存")
    @PostMapping(value = "/query-inventory")
    public ResultJson<RoomRateDetailRes> queryInventory(@Valid @RequestBody OtaQueryInventoryReq req) {
        RoomRateDetailRes inventory = otaService.queryInventory(req);
        return ResultJson.ok().data(inventory);
    }

    @ApiOperation(value = "查询房间在住订单")
    @PostMapping(value = "/query-room-orders")
    public ResultJson<OtaQueryOrderRes> queryRoomOrders(@Valid @RequestBody OtaQueryRoomOrderReq req) {
        OtaQueryOrderRes res = otaService.queryRoomOrders(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperation(value = "查询可挂帐应收账户")
    @PostMapping(value = "/query-receivable-accounts")
    public ResultJson<OtaQueryArAccountRes> queryReceivableAccounts(@Valid @RequestBody OtaQueryReceivableAccountReq req) {
        OtaQueryArAccountRes res = otaService.queryReceivableAccounts(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperation(value = "挂应收")
    @PostMapping(value = "/order-ar-pay")
    public ResultJson<OtaChargeRes> arPay(@Valid @RequestBody OtaOrderArPayReq req) {
        OtaChargeRes chargeRes = otaService.arPay(req);
        return ResultJson.ok().data(chargeRes);
    }

    @ApiOperation(value = "挂预订消费")
    @PostMapping(value = "/order-room-consume")
    public ResultJson<OtaChargeRes> orderConsumption(@Valid @RequestBody OtaOrderConsumptionReq req) {
        OtaChargeRes chargeRes = otaService.orderConsumption(req);
        return ResultJson.ok().data(chargeRes);
    }

    @ApiOperation(value = "撤销消费")
    @PostMapping(value = "/cancel-charge")
    public ResultJson<OtaChargeRes> cancelCharge(@Valid @RequestBody OtaCancelChargeReq req) {
        OtaChargeRes chargeRes = otaService.cancelCharge(req);
        return ResultJson.ok().data(chargeRes);
    }
} 
package com.cw.controller.pms;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.CommonDelReq;
import com.cw.pojo.dto.common.req.CommonOpReq;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.pojo.dto.console.req.HotelOperationReq;
import com.cw.pojo.dto.pms.req.room.QueryRoomReq;
import com.cw.pojo.dto.pms.req.room.RoomInfoReq;
import com.cw.pojo.dto.pms.req.room.UpdateRoomInfoReq;
import com.cw.pojo.dto.pms.res.room.RoomListRes;
import com.cw.service.config.op.OpSysService;
import com.cw.service.config.room.RoomService;
import com.cw.service.context.GlobalContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@RequestMapping("/pmsapi/operate")
@Api(tags = "系统设置-运维工具")
public class OperateSysController {

    @Resource
    private OpSysService opSysService;

    @PostMapping("/fix-sellable-rooms")
    @ApiOperation("修复可卖房")
    public ResultJson<Common_response> fixSellableRooms(@RequestBody HotelOperationReq req) throws Exception {
        opSysService.fixAvlRooms(req);
        return ResultJson.ok().data(new Common_response());
    }

    @PostMapping("/refresh-cache")
    @ApiOperation("刷新缓存")
    public ResultJson<Common_response> refreshCache(@RequestBody CommonOpReq req) {
        opSysService.refreshCache(GlobalContext.getCurrentHotelId());
        return ResultJson.ok().data(new Common_response());
    }

    @PostMapping("/upload-room-rates")
    @ApiOperation("上传酒店房价")
    public ResultJson<Common_response> uploadRoomRates(@RequestBody HotelOperationReq req) {
        opSysService.uploadRoomRates(req);
        return ResultJson.ok().data(new Common_response());
    }

    @PostMapping("/upload-room-inventory")
    @ApiOperation("上传酒店房量")
    public ResultJson<Common_response> uploadRoomInventory(@RequestBody HotelOperationReq req) {
        opSysService.uploadRoomInventory(req);
        return ResultJson.ok();
    }

    @PostMapping("/sync-orders")
    @ApiOperation("订单同步")
    public ResultJson<Common_response> syncOrders(@RequestBody HotelOperationReq req) {
        opSysService.syncOrders(req);
        return ResultJson.ok();
    }

}

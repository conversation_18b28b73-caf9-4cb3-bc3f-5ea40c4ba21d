package com.cw.controller.pms;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2024-04-07
 */

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.log.req.UserLogListReq;
import com.cw.pojo.log.res.UserLogListRes;
import com.cw.service.log.UserLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "前台接待-操作日志")
@RestController
@RequestMapping(value = "pmsapi/log", method = RequestMethod.POST)
public class UserLogController {
    @Autowired
    private UserLogService userLogService;


    @ApiOperation(value = "用户日志 - 用户日志列表", notes = "获取用户日志列表")
    @RequestMapping(value = "/userLog_list")
    public ResultJson<UserLogListRes> userLog_list(@RequestBody UserLogListReq _userLog_List_req) {
        return ResultJson.ok().data(userLogService.queryList(_userLog_List_req));
    }
}

package com.cw.controller.pms;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.CommonSelectReq;
import com.cw.pojo.dto.common.res.Common_Select_Res;
import com.cw.pojo.dto.pms.req.accountitem.AccountItemEntity;
import com.cw.pojo.dto.pms.req.option.OptionReq;
import com.cw.pojo.dto.pms.req.option.res.OptionListRes;
import com.cw.service.config.common.CustomDataService;
import com.cw.service.config.options.OptionEntity;
import com.cw.service.config.options.OptionSwitchService;
import com.cw.service.context.GlobalContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/11
 **/
@Api(tags = "酒店参数")
@RestController
@RequestMapping(value = "/pmsapi/option", method = RequestMethod.POST)
public class OptionController {

    @Autowired
    OptionSwitchService optionSwitchService;

    @ApiOperation(value = "酒店参数数据", notes = "获取酒店参数数据")
    @RequestMapping(value = "/query_data")
    public ResultJson<List<OptionListRes>> query_data(@RequestBody OptionReq req) {
        return ResultJson.ok().data(optionSwitchService.getOptionData(req));
    }

    @ApiOperation(value = "添加酒店参数")
    @PostMapping(value = "/save")
    public ResultJson save(@Valid @RequestBody OptionEntity entity) {
        optionSwitchService.save(entity);
        return ResultJson.ok().data(null);
    }
}

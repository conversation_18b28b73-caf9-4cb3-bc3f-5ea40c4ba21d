package com.cw.controller.pms;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.CommonDelReq;
import com.cw.pojo.dto.pms.req.building.BuildingReq;
import com.cw.pojo.dto.pms.req.building.QueryBuildingReq;
import com.cw.pojo.dto.pms.req.building.UpdateBuildingReq;
import com.cw.pojo.dto.pms.res.building.BuildingListRes;
import com.cw.service.config.building.BuildingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @Description: 楼栋设置控制器
 * @Author: michael.pan
 * @Date: 2024/3/24 10:46
 */
@RestController
@RequestMapping(value = "/pmsapi/building")
@Api(tags = "系统设置-楼栋设置")
public class BuildingController {

    @Resource
    private BuildingService buildingService;

    @ApiOperation(value = "添加楼栋")
    @PostMapping(value = "/add")
    private ResultJson addBuilding(@Valid @RequestBody BuildingReq buildingReq) {
        buildingService.addBuilding(buildingReq);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "修改楼栋")
    @PostMapping(value = "/update")
    private ResultJson updateBuilding(@Valid @RequestBody UpdateBuildingReq buildingReq) {
        buildingService.updateBuilding(buildingReq);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "楼栋列表")
    @PostMapping(value = "/list")
    private ResultJson listBuilding(@Valid @RequestBody QueryBuildingReq queryBuildingReq) {
        BuildingListRes buildingListRes = buildingService.listBuilding(queryBuildingReq);
        return ResultJson.ok().data(buildingListRes);
    }

    @ApiOperation(value = "删除楼栋")
    @PostMapping(value = "/delete")
    public ResultJson deleteBuilding(@RequestBody CommonDelReq req) {
        buildingService.deleteBuilding(req.getId());
        return ResultJson.ok().data(null);
    }
}

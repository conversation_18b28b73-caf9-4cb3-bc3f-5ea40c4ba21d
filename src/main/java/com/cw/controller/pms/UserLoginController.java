package com.cw.controller.pms;

import com.cw.mapper.OpUserMapper;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.pms.req.users.UserSignReq;
import com.cw.pojo.dto.pms.res.users.UserInfo;
import com.cw.pojo.dto.pms.res.users.UserResult;
import com.cw.service.config.user.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/3/13 14:24
 **/
@Slf4j
@RestController
@RequestMapping(value = "/pmsapi/user", method = RequestMethod.POST)
@Api(tags = "后台设置-用户登陆注销")
public class UserLoginController {

    @Autowired
    UserService userService;
    @Autowired
    OpUserMapper opUserMapper;


    @ApiOperation(value = "获取用户姓名头像信息")
    @RequestMapping(value = "/info")
    public ResultJson<UserInfo> info() {

        UserInfo userInfo = userService.getInfo();

        return ResultJson.ok().data(userInfo);
    }


    @ApiOperation(value = "用户登陆")
    @RequestMapping(value = "/login")
    public ResultJson<UserResult> login(@Valid @RequestBody UserSignReq userSignReq) {
        //String testuserid = "admin";
        //String testpwd = "654321";
        //String hotelid = "001";
        //
        //LoginJwtForm form = new LoginJwtForm();
        //form.setUserId(userSignReq.getUserid());
        //form.setEntranceType(EntranceType.CONSOLEWEB);
        //form.setHotelId(userSignReq.getHotelid());
        //
        //
        //
        //
        //UserResult userResult=new UserResult() ;
        //StpUtil.login(form);
        //String token = StpUtil.getTokenInfo().getTokenValue();
        //userResult.setToken(token);

        UserResult result = userService.login(userSignReq);

        return ResultJson.ok().data(result);
    }



}

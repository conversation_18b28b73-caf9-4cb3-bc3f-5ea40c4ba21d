package com.cw.controller.pms;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.CommonDelReq;
import com.cw.pojo.dto.pms.req.brand.BrandReq;
import com.cw.pojo.dto.pms.req.brand.QueryBrandReq;
import com.cw.pojo.dto.pms.req.brand.UpdateBrandReq;
import com.cw.pojo.dto.pms.res.brand.BrandListRes;
import com.cw.service.config.brand.BrandService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @Classname BrandController
 * @Description 酒店品牌控制器
 * @Date 2024-03-16 22:00
 * <AUTHOR> sancho.shen
 */
@RestController
@RequestMapping(value = "/pmsapi/brand")
@Api(tags = "系统设置-酒店品牌管理")
public class BrandController {

    @Resource
    private BrandService brandService;

    @ApiOperation(value = "添加品牌")
    @PostMapping(value = "/add")
    public ResultJson addBrand(@Valid @RequestBody BrandReq brandReq) {
        brandService.addBrand(brandReq);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "修改品牌")
    @PostMapping(value = "/update")
    public ResultJson updateBrand(@Valid @RequestBody UpdateBrandReq updateBrandReq) {
        brandService.updateBrand(updateBrandReq);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "品牌列表")
    @PostMapping(value = "/list")
    public ResultJson<BrandListRes> listBrand(@Valid @RequestBody QueryBrandReq queryBrandReq) {
        BrandListRes list = brandService.listBrand(queryBrandReq);
        return ResultJson.ok().data(list);
    }

    @ApiOperation(value = "删除品牌")
    @PostMapping(value = "/delete")
    public ResultJson deleteBrand(@RequestBody CommonDelReq req) {
        brandService.deleteBrand(req.getId());
        return ResultJson.ok().data(null);
    }

}

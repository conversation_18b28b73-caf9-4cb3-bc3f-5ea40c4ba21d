package com.cw.controller.pms;

import cn.hutool.core.bean.BeanUtil;
import com.cw.entity.NaParam;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.CommonDelReq;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.pms.res.na.req.NaConfig_Req;
import com.cw.pojo.dto.pms.res.na.req.NaConfig_Save_Req;
import com.cw.pojo.dto.pms.res.na.req.NaDailyLog_Req;
import com.cw.pojo.dto.pms.res.na.res.NaConfig_Res;
import com.cw.pojo.dto.pms.res.na.res.NaDailyLog_Res;
import com.cw.pojo.dto.pms.res.na.res.NaParamName_Res;
import com.cw.pojo.entity.NaConfigEntity;
import com.cw.pojo.entity.NaParamEntity;
import com.cw.pojo.entity.NaRunInfoEntity;
import com.cw.service.config.nightaudit.NightAuditService;
import com.cw.utils.na.NaParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe 夜审设置
 * <AUTHOR> Tony Leung
 * @Create on 2024-03-18
 */
@RestController
@RequestMapping(value = "/pmsapi/nightAudit", method = RequestMethod.POST)
@Api(tags = "系统设置-夜审设置")
public class NightAuditController {
    @Autowired
    NightAuditService nightAuditService;

    @ApiOperation(value = "夜审设置 - 获取夜审配置和参数列表", notes = "获取夜审配置和参数列表")
    @RequestMapping(value = "/naParam_list", method = RequestMethod.POST)
    public ResultJson<NaConfig_Res> naParam_list(@RequestBody NaConfig_Req req) {
        return ResultJson.ok().data(nightAuditService.queryParamList(req));
    }

    @ApiOperation(value = "夜审设置 - 保存夜审配置", notes = "保存夜审配置")
    @RequestMapping(value = "/save_naConfig", method = RequestMethod.POST)
    public ResultJson<NaConfigEntity> save_naConfig(@RequestBody NaConfig_Save_Req req) {
        return ResultJson.ok().data(nightAuditService.saveConfig(req.getRuntime()));
    }

    @ApiOperation(value = "夜审设置 - 获取夜审参数对象", notes = "获取夜审参数对象")
    @RequestMapping(value = "/load_naParam", method = RequestMethod.POST)
    public ResultJson<NaParamEntity> load_naParam(@RequestBody Common_Load_Req req) {
        NaParam naparam = null;
        if (req.getId() > 0L) {
            naparam = nightAuditService.loadNaParam(req.getId());
        } else {
            naparam = new NaParam();
        }
        NaParamEntity entity = new NaParamEntity();
        BeanUtil.copyProperties(naparam, entity);
        return ResultJson.ok().data(entity);
    }


    @ApiOperation(value = "夜审设置 - 保存夜审参数", notes = "保存夜审参数")
    @RequestMapping(value = "/save_naParam", method = RequestMethod.POST)
    public ResultJson<NaParamEntity> save_naParam(@RequestBody NaParamEntity entity) {
        return ResultJson.ok().data(nightAuditService.saveParams(entity));
    }

    @ApiOperation(value = "夜审设置 - 删除夜审参数", notes = "删除审参数")
    @RequestMapping(value = "/delete_naParam", method = RequestMethod.POST)
    public ResultJson delete_naParam(@RequestBody CommonDelReq req) {
        nightAuditService.deleteNaParam(req.getId());
        return ResultJson.ok();
    }

    @ApiOperation(value = "夜审设置 - 获取参数名列表", notes = "获取参数名列表")
    @RequestMapping(value = "/paramName_list", method = RequestMethod.POST)
    public ResultJson<NaParamName_Res> paramName_list() {
        NaParamName_Res res = new NaParamName_Res();
        List<NaParamName_Res.NaParamName> names = new ArrayList<>();
        NaParamName_Res.NaParamName name;
        NaParams[] naParams = NaParams.values();
        for (NaParams naParam : naParams) {
            name = new NaParamName_Res.NaParamName();
            name.setParamName(naParam.name());
            name.setParamDesc(naParam.getDesc());
            names.add(name);
        }
        res.setNames(names);
        return ResultJson.ok().data(res);
    }

    @ApiOperation(value = "夜审设置 - 获取夜审状态", notes = "获取夜审状态")
    @RequestMapping(value = "/load_naRunInfo", method = RequestMethod.POST)
    public ResultJson<NaRunInfoEntity> load_naRunInfo() {
        NaRunInfoEntity entity = nightAuditService.getNaRunInfo();
        return ResultJson.ok().data(entity);
    }


    @ApiOperation(value = "夜审设置 - 重设夜审", notes = "重设夜审")
    @RequestMapping(value = "/refresh_naStatus", method = RequestMethod.POST)
    public ResultJson refresh_naStatus() {
        nightAuditService.resetDailyTask();
        return ResultJson.ok();
    }

    @ApiOperation(value = "夜审设置 - 获取夜审日志", notes = "获取夜审日志")
    @RequestMapping(value = "/load_naLog", method = RequestMethod.POST)
    public ResultJson<NaDailyLog_Res> load_naLog(@RequestBody NaDailyLog_Req req) {
        NaDailyLog_Res naLog = nightAuditService.queryNaLog(req);
        return ResultJson.ok().data(naLog);
    }
}

package com.cw.controller.pms;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.CommonDelReq;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.pms.req.accountitem.AccountItemEntity;
import com.cw.pojo.dto.pms.req.accountitem.QueryAccountItemReq;
import com.cw.pojo.dto.pms.req.accountitem.UpdateAccountItemEntity;
import com.cw.pojo.dto.pms.res.accountitem.AccountItemListRes;
import com.cw.service.config.accountitem.AccountItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @Description: 账项代码控制器
 * @Author: michael.pan
 * @Date: 2024/3/21 23:51
 */
@RestController
@RequestMapping(value = "/pmsapi/account-item-code", method = RequestMethod.POST)
@Api(tags = "系统设置-账项代码")
public class AccountItemController {

    @Resource
    private AccountItemService accountItemService;

    @ApiOperation(value = "添加账项代码")
    @PostMapping(value = "/add")
    public ResultJson addAccountItem(@Valid @RequestBody AccountItemEntity entity) {
        accountItemService.addAccountItem(entity);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "修改账项代码")
    @PostMapping(value = "/update")
    public ResultJson updateAccountItem(@Valid @RequestBody AccountItemEntity accountItemReq) {
        accountItemService.updateAccountItem(accountItemReq);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "/加载账项代码")
    @PostMapping(value = "/load")
    public ResultJson<AccountItemEntity> load(@Valid @RequestBody Common_Load_Req req) {
        AccountItemEntity entity = accountItemService.loadAccountItem(req);
        return ResultJson.ok().data(entity);
    }



    @ApiOperation(value = "账项代码列表")
    @PostMapping(value = "/list")
    public ResultJson<AccountItemListRes> list(@Valid @RequestBody QueryAccountItemReq queryAccountItemReq) {
        AccountItemListRes itemListRes = accountItemService.listAccountItem(queryAccountItemReq);
        return ResultJson.ok().data(itemListRes);
    }

    @ApiOperation(value = "删除账项代码")
    @PostMapping(value = "/delete")
    public ResultJson deleteAccountItem(@RequestBody CommonDelReq req) {
        accountItemService.deleteAccountItem(req.getId());
        return ResultJson.ok().data(null);
    }
}

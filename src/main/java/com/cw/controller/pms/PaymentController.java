package com.cw.controller.pms;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.CommonDelReq;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.pms.req.payment.PaymentEntity;
import com.cw.pojo.dto.pms.req.payment.QueryPaymentReq;
import com.cw.pojo.dto.pms.req.payment.UpdatePaymentEntity;
import com.cw.pojo.dto.pms.res.payment.PaymentListRes;
import com.cw.service.config.payment.PaymentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @Description: 付款方式控制器
 * @Author: michael.pan
 * @Date: 2024/3/24 23:40
 */
@RestController
@RequestMapping(value = "/pmsapi/payment")
@Api(tags = "系统设置-付款方式")
public class PaymentController {

    @Resource
    private PaymentService paymentService;

    @ApiOperation(value = "添加付款方式")
    @PostMapping(value = "/add")
    public ResultJson addPayment(@Valid @RequestBody PaymentEntity paymentEntity) {
        paymentService.addPayment(paymentEntity);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "修改付款方式")
    @PostMapping(value = "/update")
    public ResultJson updatePayment(@Valid @RequestBody PaymentEntity paymentReq) {
        paymentService.updatePayment(paymentReq);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "加载付款方式")
    @PostMapping(value = "/load")
    public ResultJson<PaymentEntity> load(@Valid @RequestBody Common_Load_Req req) {
        PaymentEntity paymentEntity = paymentService.load(req);
        return ResultJson.ok().data(paymentEntity);
    }

    @ApiOperation(value = "付款方式列表")
    @PostMapping(value = "/list")
    public ResultJson<PaymentListRes> list(@Valid @RequestBody QueryPaymentReq queryPaymentReq) {
        PaymentListRes paymentListRes = paymentService.listPayment(queryPaymentReq);
        return ResultJson.ok().data(paymentListRes);
    }

    @ApiOperation(value = "删除付款方式")
    @PostMapping(value = "/delete")
    public ResultJson deletePayment(@RequestBody CommonDelReq req) {
        paymentService.delete(req.getId());
        return ResultJson.ok().data(null);
    }
}

package com.cw.controller.pms;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.CommonDelReq;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.pms.req.roomtype.QueryRoomTypeReq;
import com.cw.pojo.dto.pms.req.roomtype.RoomTypeEntity;
import com.cw.pojo.dto.pms.req.roomtype.UpdateRoomTypeInfoReq;
import com.cw.pojo.dto.pms.res.roomtype.RoomTypeListRes;
import com.cw.service.config.roomtype.RoomTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @Description: 房型信息控制器
 * @Author: michael.pan
 * @Date: 2024/3/18 22:39
 */
@RestController
@RequestMapping(value = "/pmsapi/room-type", method = RequestMethod.POST)
@Api(tags = "系统设置-酒店房型")
public class RoomTypeController {
    @Resource
    private RoomTypeService roomTypeService;

    @ApiOperation(value = "添加房型")
    @PostMapping(value = "/add")
    public ResultJson addRoomType(@Valid @RequestBody com.cw.pojo.dto.pms.req.roomtype.RoomTypeEntity hotelInfoRequest) {
        roomTypeService.addRoomType(hotelInfoRequest);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "修改房型")
    @PostMapping(value = "/update")
    public ResultJson updateRoomType(@Valid @RequestBody UpdateRoomTypeInfoReq roomTypeInfoReq) {
        roomTypeService.updateRoomType(roomTypeInfoReq);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "读取房型")
    @PostMapping(value = "/load")
    public ResultJson<RoomTypeEntity> load(@Valid @RequestBody Common_Load_Req req) {
        RoomTypeEntity roomTypeEntity = roomTypeService.load(req);
        return ResultJson.ok().data(roomTypeEntity);
    }

    @ApiOperation(value = "房型列表")
    @PostMapping(value = "/list")
    public ResultJson<RoomTypeListRes> list(@Valid @RequestBody QueryRoomTypeReq queryRoomTypeReq) {
        RoomTypeListRes roomTypeListRes = roomTypeService.listRoomType(queryRoomTypeReq);
        return ResultJson.ok().data(roomTypeListRes);
    }

    @ApiOperation(value = "删除房型")
    @PostMapping(value = "/delete")
    public ResultJson deleteRoomType(@RequestBody CommonDelReq req) {
        roomTypeService.delete(req.getId());
        return ResultJson.ok().data(null);
    }
}

package com.cw.controller.pms;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.CommonDelReq;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.pms.req.pkgdet.PkgdetReq;
import com.cw.pojo.dto.pms.req.pkgdet.QueryPkgdetReq;
import com.cw.pojo.dto.pms.req.pkgdet.UpdatePkgdetReq;
import com.cw.pojo.dto.pms.res.pkgdet.PkgdetListRes;
import com.cw.pojo.dto.pms.res.pkgdet.PkgdetRes;
import com.cw.service.config.pkgdet.PkgdetService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 包价详情控制器
 * <AUTHOR>
 * @date 2024-07-30
 */
@RestController
@RequestMapping(value = "/pmsapi/pkgdet")
@Api(tags = "系统设置-包价详情管理")
public class PkgdetController {

    @Resource
    private PkgdetService pkgdetService;

    @ApiOperation(value = "添加包价详情")
    @PostMapping(value = "/add")
    public ResultJson addPkgdet(@Valid @RequestBody PkgdetReq pkgdetReq) {
        pkgdetService.addPkgdet(pkgdetReq);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "修改包价详情")
    @PostMapping(value = "/update")
    public ResultJson updatePkgdet(@Valid @RequestBody UpdatePkgdetReq updatePkgdetReq) {
        pkgdetService.updatePkgdet(updatePkgdetReq);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "删除包价详情")
    @PostMapping(value = "/delete")
    public ResultJson deletePkgdet(@Valid @RequestBody CommonDelReq req) {
        pkgdetService.deletePkgdet(req.getId());
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "包价详情列表")
    @PostMapping(value = "/list")
    public ResultJson<PkgdetListRes> listPkgdet(@Valid @RequestBody QueryPkgdetReq queryPkgdetReq) {
        PkgdetListRes list = pkgdetService.listPkgdet(queryPkgdetReq);
        return ResultJson.ok().data(list);
    }

    @ApiOperation(value = "根据ID查询包价详情")
    @PostMapping(value = "/load")
    public ResultJson<PkgdetRes> loadPkgdet(@Valid @RequestBody Common_Load_Req req) {
        PkgdetRes pkgdetRes = pkgdetService.findPkgdet(req.getId());
        return ResultJson.ok().data(pkgdetRes);
    }
}

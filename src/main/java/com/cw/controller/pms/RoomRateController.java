package com.cw.controller.pms;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.CommonDelReq;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.pms.req.rate.QueryRoomRateReq;
import com.cw.pojo.dto.pms.req.rate.RoomRateEntity;
import com.cw.pojo.dto.pms.req.rate.UpdateRoomRateEntity;
import com.cw.pojo.dto.pms.res.rate.RoomRateListRes;
import com.cw.service.config.rate.RoomRateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @Classname RoomRateController
 * @Description 房价控制器
 * @Date 2024-03-18 23:21
 * <AUTHOR> sancho.shen
 */
@RestController
@RequestMapping(value = "/pmsapi/room-rate")
@Api(tags = "系统设置-房价管理")
public class RoomRateController {

    @Resource
    private RoomRateService roomRateService;

    @ApiOperation(value = "添加房价")
    @PostMapping(value = "/add")
    public ResultJson addRoomRate(@Valid @RequestBody RoomRateEntity roomRateEntity) {
        roomRateService.addRoomRate(roomRateEntity);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "修改房价")
    @PostMapping(value = "/update")
    public ResultJson updateRoomRate(@Valid @RequestBody RoomRateEntity updateRoomRateReq) {
        roomRateService.updateRoomRate(updateRoomRateReq);
        return ResultJson.ok().data(null);
    }


    @ApiOperation(value = "加载房价记录")
    @PostMapping(value = "/load")
    public ResultJson<RoomRateEntity> load(@Valid @RequestBody Common_Load_Req req) {
        RoomRateEntity roomRateEntity = roomRateService.load(req);
        return ResultJson.ok().data(roomRateEntity);
    }

    @ApiOperation(value = "房价列表")
    @PostMapping(value = "/list")
    public ResultJson<RoomRateListRes> listRoomRate( @Valid @RequestBody QueryRoomRateReq queryRoomRateReq) {
        RoomRateListRes list = roomRateService.listRoomRate( queryRoomRateReq);
        return ResultJson.ok().data(list);
    }

    @ApiOperation(value = "删除房价")
    @PostMapping(value = "/delete")
    public ResultJson deleteRoomRate(@RequestBody CommonDelReq req) {
        roomRateService.deleteRoomRate(req.getId());
        return ResultJson.ok().data(null);
    }
}

package com.cw.controller.pms;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.CommonDelReq;
import com.cw.pojo.dto.pms.req.channel.ChannelReq;
import com.cw.pojo.dto.pms.req.channel.QueryChannelReq;
import com.cw.pojo.dto.pms.req.channel.UpdateChannelReq;
import com.cw.pojo.dto.pms.res.channel.ChannelListRes;
import com.cw.service.config.channel.ChannelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @Description: 渠道控制器
 * @Author: michael.pan
 * @Date: 2024/3/24 10:46
 */
@RestController
@RequestMapping(value = "/pmsapi/channel")
@Api(tags = "系统设置-渠道管理")
public class ChannelController {

    @Resource
    private ChannelService channelService;

    @ApiOperation(value = "添加渠道")
    @PostMapping(value = "/add")
    private ResultJson addChannel(@Valid @RequestBody ChannelReq channelReq) {
        channelService.addChannel(channelReq);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "修改渠道")
    @PostMapping(value = "/update")
    private ResultJson updateChannel(@Valid @RequestBody UpdateChannelReq channelReq) {
        channelService.updateChannel(channelReq);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "查询渠道")
    @PostMapping(value = "/list")
    private ResultJson listChannel(@Valid @RequestBody QueryChannelReq queryChannelReq) {
        ChannelListRes marketListRes = channelService.listChannel(queryChannelReq);
        return ResultJson.ok().data(marketListRes);
    }

    @ApiOperation(value = "删除渠道")
    @PostMapping(value = "/delete")
    public ResultJson deleteChannel(@RequestBody CommonDelReq req) {
        channelService.deleteChannel(req.getId());
        return ResultJson.ok().data(null);
    }
}

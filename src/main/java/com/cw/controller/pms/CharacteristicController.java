package com.cw.controller.pms;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.CommonDelReq;
import com.cw.pojo.dto.pms.req.characteristic.CharacteristicReq;
import com.cw.pojo.dto.pms.req.characteristic.QueryCharacteristicReq;
import com.cw.pojo.dto.pms.req.characteristic.UpdateCharacteristicReq;
import com.cw.pojo.dto.pms.res.characteristic.CharacteristicListRes;
import com.cw.service.config.characteristic.CharacteristicService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @Description: 特性设置控制器
 * @Author: michael.pan
 * @Date: 2024/3/28 10:46
 */
@RestController
@RequestMapping(value = "/pmsapi/characteristic")
@Api(tags = "系统设置-特性设置")
public class CharacteristicController {

    @Resource
    private CharacteristicService characteristicService;

    @ApiOperation(value = "添加特性设置")
    @PostMapping(value = "/add")
    private ResultJson addCharacteristic(@Valid @RequestBody CharacteristicReq characteristicReq) {
        characteristicService.addCharacteristic(characteristicReq);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "修改特性设置")
    @PostMapping(value = "/update")
    private ResultJson updateCharacteristic(@Valid @RequestBody UpdateCharacteristicReq characteristicReq) {
        characteristicService.updateCharacteristic(characteristicReq);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "查询特性设置")
    @PostMapping(value = "/list")
    private ResultJson listCharacteristic(@Valid @RequestBody QueryCharacteristicReq queryCharacteristicReq) {
        CharacteristicListRes characteristicListRes = characteristicService.listCharacteristic(queryCharacteristicReq);
        return ResultJson.ok().data(characteristicListRes);
    }

    @ApiOperation(value = "删除特性设置")
    @PostMapping(value = "/delete")
    public ResultJson deleteCharacteristic(@RequestBody CommonDelReq req) {
        characteristicService.deleteCharacteristic(req.getId());
        return ResultJson.ok().data(null);
    }
}

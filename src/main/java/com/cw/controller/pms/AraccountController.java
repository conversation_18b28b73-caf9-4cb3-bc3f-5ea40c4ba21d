package com.cw.controller.pms;

import cn.hutool.core.util.StrUtil;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.CommonDelReq;
import com.cw.pojo.dto.common.req.CommonQueryPageReq;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.pojo.dto.pms.req.ar.ArAccountEntiy;
import com.cw.pojo.dto.pms.req.cashier.ArConsoleWriteOffReq;
import com.cw.pojo.dto.pms.req.cashier.ArWriteOffQueryReq;
import com.cw.pojo.dto.pms.req.cashier.ArWriteOffReq;
import com.cw.pojo.dto.pms.req.guest.*;
import com.cw.pojo.dto.pms.res.ar.ArAccLogListRes;
import com.cw.pojo.dto.pms.res.ar.ArDetailListRes;
import com.cw.pojo.dto.pms.res.ar.ArWriteOffQueryRes;
import com.cw.pojo.dto.pms.res.ar.AraccountListRes;
import com.cw.pojo.dto.pms.res.guest.ARWrittenOffDetailListRes;
import com.cw.pojo.dto.pms.res.guest.ArAccTransferRes;
import com.cw.service.config.accountitem.ArAccountService;
import com.cw.service.config.cashier.CashierService;
import com.cw.service.config.guest.GuestAccountService;
import com.cw.service.context.GlobalContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @Description: Ar账户管理
 * @Author: just
 * @Date: 2024/10/23 23:40
 */
@RestController
@RequestMapping(value = "/pmsapi/araccount")
@Api(tags = "系统设置-AR应收账户管理")
public class AraccountController {

    @Resource
    private ArAccountService arAccountService;

    @Resource
    private CashierService cashierService;

    @Resource
    private GuestAccountService guestAccountService;

    @ApiOperation(value = "保存AR应收账户")
    @PostMapping(value = "/save")
    public ResultJson<Common_response> save(@Valid @RequestBody ArAccountEntiy req) {
        arAccountService.saveAraccount(req);
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperation(value = "读取AR应收账户")
    @PostMapping(value = "/load")
    public ResultJson<ArAccountEntiy> load(@Valid @RequestBody Common_Load_Req req) {
        ArAccountEntiy arAccountEntiy = arAccountService.loadAraccount(req);
        return ResultJson.ok().data(arAccountEntiy);
    }

    @ApiOperation(value = "查询单次核销金额")
    @PostMapping(value = "/query_writeoff_amount")
    public ResultJson<ArWriteOffQueryRes> queryWriteOffAmount(@Valid @RequestBody ArWriteOffQueryReq req) {
        ArWriteOffQueryRes res = arAccountService.queryWriteOffAmount(req);
        return ResultJson.ok().data(res);
    }


    @ApiOperation(value = "AR应收账户列表")
    @PostMapping(value = "/list")
    public ResultJson<AraccountListRes> list(@Valid @RequestBody CommonQueryPageReq req) {
        AraccountListRes res = arAccountService.list(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperation(value = "删除AR应收账户")
    @PostMapping(value = "/delete")
    public ResultJson del(@RequestBody CommonDelReq req) {
        arAccountService.deleteAraccount(req);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "核销帐")
    @PostMapping(value = "/writeoff")
    public ResultJson<Common_response> writeoff(@Valid @RequestBody ArConsoleWriteOffReq req) {
        cashierService.ar_write_off(GlobalContext.getCurrentHotelId(), req.toArWriteOffReq());
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperation(value = "账户充值")
    @PostMapping(value = "/deposit")
    public ResultJson depoit(@Valid @RequestBody ArAccDepositReq req) {
        String hotelId = GlobalContext.getCurrentHotelId();
        cashierService.post(hotelId, req.toDepositReq(hotelId));
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "撤销充值")
    @PostMapping(value = "/refund_deposit")
    public ResultJson refundDeposit(@Valid @RequestBody ArAccDepositReq req) {
        String hotelId = GlobalContext.getCurrentHotelId();
        cashierService.arBalanceRefund(hotelId, req.toCancelDepositReq(hotelId));
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "撤销核销帐")
    @PostMapping(value = "/cancel_writeoff")
    public ResultJson arCancelWriteOff(@Valid @RequestBody CancelARWriteOffReq req) {
        String hotelId = GlobalContext.getCurrentHotelId();
        cashierService.arCancelWriteOff(hotelId, req.getAcc_id(), req.getRemark());
        return ResultJson.ok().data(null);
    }



    @ApiOperation(value = "查看充值退款流水日志")
    @PostMapping(value = "/query_deposit_logs")
    public ResultJson<ArDetailListRes> listArAccLogs(@Valid @RequestBody QueryARAccLogReq req) {
        ArDetailListRes res = guestAccountService.listARGuestAccountDetail(req.toQueryARDetailReq());

        //ArAccLogListRes
        return ResultJson.ok().data(res);
    }


    @ApiOperation(value = "应收未核销账目明细列表")
    @PostMapping(value = "/araccount_details")
    public ResultJson<ArDetailListRes> listARPaidDetails(@Valid @RequestBody QueryARDetailReq req) {
        return ResultJson.ok().data(guestAccountService.listARGuestAccountDetail(req));
    }

    @ApiOperation(value = "应收核销明细列表")
    @PostMapping(value = "/araccount_writeoff_details")
    public ResultJson<ARWrittenOffDetailListRes> listARWriteOffDetails(@Valid @RequestBody QueryARDetailReq req) {
        return ResultJson.ok().data(guestAccountService.listARWrittenOffDetails(req));
    }

    @ApiOperation(value = "应收明细转账-将待核销的账目转账到指定账户")
    @PostMapping(value = "/trans_ardetials")
    public ResultJson<Common_response> transfer_ar(@RequestBody TransferArReq req) {
        ArAccTransferRes rsp = cashierService.transferArAccDetails(GlobalContext.getCurrentHotelId(), req.getAccids(), req.getArNo(), StrUtil.EMPTY);
        Common_response response = new Common_response();
        response.setMsg(StrUtil.format("转账成功，共转账成功{}笔，失败{}笔", rsp.getSuccess_ids().size(), rsp.getFailed_msg().size()));
        return ResultJson.ok().data(response);
    }

    @ApiOperation(value = "应收账户余额转移-余额没用完.转移到其他账户")
    @PostMapping(value = "/transfer_ardeposit")
    public ResultJson<Common_response> transfer_arbalance(@RequestBody ArAccDepositTransferReq req) {
        ArAccTransferRes rsp = cashierService.transferArAccBalance(GlobalContext.getCurrentHotelId(), req);
        Common_response response = new Common_response();
        response.setMsg(StrUtil.format("转账成功，共转账成功{}笔，失败{}笔", rsp.getSuccess_ids().size(), rsp.getFailed_msg().size()));
        return ResultJson.ok().data(response);
    }

    @ApiOperation(value = "保存签名照地址")
    @PostMapping(value = "/savesignature")
    public ResultJson saveSignature(@RequestBody ArSignatureReq req)  {
        arAccountService.saveSignature(req);
        return ResultJson.ok().data(new Common_response());
    }
}

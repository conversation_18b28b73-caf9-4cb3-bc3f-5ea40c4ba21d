package com.cw.controller.pms;

import com.cw.entity.Preauth;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.pms.preauth.PreauthCancelReq;
import com.cw.pojo.dto.pms.preauth.PreauthCompleteReq;
import com.cw.pojo.dto.pms.preauth.PreauthInitiateReq;
import com.cw.pojo.dto.pms.preauth.PreauthQueryReq;
import com.cw.service.PreauthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * @Description: 预授权管理API
 * @Author: Just
 * @Date: 2025-06-27
 */
@RestController
@RequestMapping("/api/pms/preauth")
@Api(tags = "PMS - 预授权管理")
public class PreauthController {

    @Autowired
    private PreauthService preauthService;

    @PostMapping("/query")
    @ApiOperation("查询预授权记录")
    public ResultJson<List<Preauth>> query(@Valid @RequestBody PreauthQueryReq req) {
        List<Preauth> result = preauthService.queryPreauths(req);
        return ResultJson.ok().data(result);
    }

    @PostMapping("/initiate")
    @ApiOperation("发起一笔新的预授权")
    public ResultJson<Preauth> initiate(@Valid @RequestBody PreauthInitiateReq req) {
        Preauth result = preauthService.initiatePreauth(req);
        return ResultJson.ok().data(result);
    }

    @PostMapping("/complete")
    @ApiOperation("完成预授权(扣款)")
    public ResultJson<Preauth> complete(@Valid @RequestBody PreauthCompleteReq req) {
        Preauth result = preauthService.completePreauth(req);
        return ResultJson.ok().data(result);
    }

    @PostMapping("/cancel")
    @ApiOperation("撤销预授权")
    public ResultJson<Preauth> cancel(@Valid @RequestBody PreauthCancelReq req) {
        Preauth result = preauthService.cancelPreauth(req);
        return ResultJson.ok().data(result);
    }
} 
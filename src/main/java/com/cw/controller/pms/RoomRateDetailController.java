package com.cw.controller.pms;

import com.cw.exception.DefinedException;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.pagedata.SelectDataNode;
import com.cw.pojo.dto.common.req.CommonDelReq;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.pojo.dto.common.res.PriceNode;
import com.cw.pojo.dto.pms.req.rate.*;
import com.cw.pojo.dto.pms.res.rate.CodeRes;
import com.cw.pojo.dto.pms.res.rate.RoomRateDetailListRes;
import com.cw.pojo.dto.pms.res.rate.RoomRateDetailRes;
import com.cw.service.config.rate.RoomRateDetailService;
import com.cw.service.context.GlobalContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @Classname RoomRateDetailController
 * @Description 房价详情控制器
 * @Date 2024-03-19 22:59
 * <AUTHOR> sancho.shen
 */

@RestController
@RequestMapping(value = "/pmsapi/room-rate-detail")
@Api(tags = "系统设置-房价详情管理")
public class RoomRateDetailController {

    @Resource
    private RoomRateDetailService roomRateDetailService;

    @ApiOperation(value = "添加房价详情")
    @PostMapping(value = "/add")
    public ResultJson addRoomRateDetail(@Valid @RequestBody RoomRateDetailReq roomRateDetailReq) {
        roomRateDetailService.addRoomRateDetail(roomRateDetailReq);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "修改房价详情")
    @PostMapping(value = "/update")
    public ResultJson updateRoomRateDetail(@Valid @RequestBody UpdateRoomRateDetailReq updateRoomRateDetailReq) {
        roomRateDetailService.updateRoomRateDetail(updateRoomRateDetailReq);
        return ResultJson.ok().data(null);
    }


    @ApiOperation(value = "批量房价详情列表")
    @PostMapping(value = "/list-batch")
    public ResultJson<List<SelectDataNode>> listRoomRateDetailBatch(@Valid @RequestBody QueryRateDetailBatchReq queryRateDetailBatchReq) {
        List<SelectDataNode> roomRateDetails = roomRateDetailService.listRoomRateDetailBatch(queryRateDetailBatchReq);
        return ResultJson.ok().data(roomRateDetails);
    }

    @ApiOperation(value = "预览修改房价详情")
    @PostMapping(value = "/show-rateupd-preview")
    public ResultJson<List<PriceNode>> showUpdPreview(@Valid @RequestBody UpdateRateDetailBatchReq req) throws DefinedException {
        List<PriceNode> nodes = roomRateDetailService.showUpdPreview(req);
        return ResultJson.ok().data(nodes);
    }

    @ApiOperation(value = "批量修改房价详情")
    @PostMapping(value = "/update-batch")
    public ResultJson<Common_response> updateRoomRateDetailBatch(@Valid @RequestBody UpdateRateDetailBatchReq updateRateDetailBatchReq) {
        String hotelId = GlobalContext.getCurrentHotelId();
        roomRateDetailService.updateRoomRateDetailBatch(hotelId, updateRateDetailBatchReq);
        return ResultJson.ok().data(new Common_response());
    }


    @ApiOperation(value = "房价详情列表")
    @PostMapping(value = "/list")
    public ResultJson<RoomRateDetailListRes> listRoomRateDetail(@Valid @RequestBody QueryRoomRateDetailReq queryRoomRateDetailReq) {
        RoomRateDetailListRes list = roomRateDetailService.listRoomRateDetail(queryRoomRateDetailReq);
        return ResultJson.ok().data(list);
    }

    @ApiOperation(value = "删除房价详情")
    @PostMapping(value = "/delete")
    public ResultJson deleteRoomRateDetail(@Valid @RequestBody CommonDelReq req) {
        roomRateDetailService.deleteRoomRateDetail(req.getId());
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "根据房价码查询关联的房价详情中的房型代码")
    @PostMapping(value = "/find-room-type")
    public ResultJson<List<CodeRes>> findAllRoomTypeByCode(@RequestBody QueryRateDetailByCodeReq queryRateDetailByCodeReq) {
        List<CodeRes> allRoomTypeByCode = roomRateDetailService.findAllRoomTypeByCode(queryRateDetailByCodeReq.getCode());
        return ResultJson.ok().data(allRoomTypeByCode);
    }

    @ApiOperation(value = "根据ID查询房价详情")
    @PostMapping(value = "/find")
    public ResultJson<RoomRateDetailRes> findRoomRateDetail(@Valid @RequestBody CommonDelReq req) {
        RoomRateDetailRes roomRateDetailRes = roomRateDetailService.findRoomRateDetail(req.getId());
        return ResultJson.ok().data(roomRateDetailRes);
    }
}

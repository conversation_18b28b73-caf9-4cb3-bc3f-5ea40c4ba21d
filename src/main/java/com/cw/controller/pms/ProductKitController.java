package com.cw.controller.pms;

import com.cw.config.exception.CustomException;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.Common_Del_Req;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.pms.ProductKitListReq;
import com.cw.pojo.dto.pms.ProductKitInfoForm;
import com.cw.pojo.dto.pms.ProductKitUpdateStatusReq;
import com.cw.service.pms.ProductKitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Arrays;

@Api(tags = "后台-套餐产品设置")
@RestController
@RequestMapping("/pms/productkit")
@RequiredArgsConstructor
public class ProductKitController {

    private final ProductKitService productKitService;

    @ApiOperation("查询套餐列表")
    @PostMapping("/list")
    public ResultJson list(@RequestBody ProductKitListReq req) {
        return ResultJson.ok().data(productKitService.list(req));
    }

    @ApiOperation("加载单个套餐信息")
    @PostMapping("/load")
    public ResultJson load(@RequestBody Common_Load_Req req) {
        return ResultJson.ok().data(productKitService.load(req));
    }

    @ApiOperation("保存套餐")
    @PostMapping("/save")
    public ResultJson save(@Valid @RequestBody ProductKitInfoForm req) {
        if (req.getKit() == null) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR));
        }
        return ResultJson.ok().data(productKitService.save(req));
    }

    @ApiOperation("删除套餐")
    @PostMapping("/delete")
    public ResultJson delete(@RequestBody Common_Del_Req req) {
        // Common_Del_Req's field is sqlid (Long), but the doc says it should be a string of ids.
        // Assuming it's a single Long id for now.
        productKitService.delete(Arrays.asList(req.getSqlid()));
        return ResultJson.ok();
    }

    @ApiOperation("更新套餐状态")
    @PostMapping("/update_status")
    public ResultJson updateStatus(@Valid @RequestBody ProductKitUpdateStatusReq req) {
        productKitService.updateStatus(req.getIds(), req.getStatus());
        return ResultJson.ok();
    }
}

package com.cw.controller.pms;

import com.cw.core.CoreAvl;
import com.cw.core.CoreCache;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.CommonDelReq;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.pojo.dto.console.req.FixAvlReq;
import com.cw.pojo.dto.console.req.RefreshCahceReq;
import com.cw.pojo.dto.pms.req.hotel.HotelInfoReq;
import com.cw.pojo.dto.pms.req.hotel.QueryHotelReq;
import com.cw.pojo.dto.pms.req.hotel.UpdateHotelInfoReq;
import com.cw.pojo.dto.pms.res.hotel.HotelListRes;
import com.cw.pojo.entity.HotelInfoEntity;
import com.cw.service.config.hotel.HotelService;
import com.cw.service.context.GlobalContext;
import com.cw.utils.SpringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @Classname HotelInfoController
 * @Description 酒店基本信息控制器
 * @Date 2024-03-15 22:46
 * <AUTHOR> sancho.shen
 */
@RestController
@RequestMapping(value = "/pmsapi/hotel")
@Api(tags = "系统设置-酒店信息管理")
public class HotelInfoController {

    @Resource
    private HotelService hotelService;

    @ApiOperation(value = "添加酒店")
    @PostMapping(value = "/add")
    public ResultJson addHotel(@Valid @RequestBody HotelInfoEntity hotelInfoRequest) {
        hotelService.addHotel(hotelInfoRequest);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "修改酒店")
    @PostMapping(value = "/update")
    public ResultJson updateHotel(@Valid @RequestBody HotelInfoEntity updateHotelInfoReq) {
        hotelService.updateHotel(updateHotelInfoReq);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "酒店列表")
    @PostMapping(value = "/list")
    public ResultJson<HotelListRes> listHotel(@Valid @RequestBody QueryHotelReq queryHotelReq) {
        HotelListRes list = hotelService.listHotel(queryHotelReq);
        return ResultJson.ok().data(list);
    }

    @ApiOperation(value = "删除酒店")
    @PostMapping(value = "/delete")
    public ResultJson deleteHotel(@RequestBody CommonDelReq req) {
        hotelService.delete(req.getId());
        return ResultJson.ok().data(null);
    }


    @ApiOperation(value = "获取酒店信息")
    @PostMapping(value = "/hotel-info")
    public ResultJson<HotelInfoEntity> findHotelInfo() {
        return ResultJson.ok().data(hotelService.findHotelInfo());
    }

   /* @ApiOperation(value = "修复可卖房")
    @PostMapping(value = "/fixAvl")
    public ResultJson<Common_response> fixAvl(@RequestBody FixAvlReq req) throws Exception {
        CoreAvl coreAvl = SpringUtil.getBean(CoreAvl.class);
        String hotelId = GlobalContext.getCurrentHotelId();
        coreAvl.fixAllHotelRoom(hotelId, req.getRmtypes(), req.getStartDate(), req.getEndDate());


        coreAvl.repResRevs(hotelId);

        return ResultJson.ok().data(null);
    }


    @ApiOperation(value = "刷新当前数据缓存")
    @PostMapping(value = "refreshCahce")
    public ResultJson<Common_response> refreshCache(@RequestBody RefreshCahceReq req) {
        CoreCache coreCache = SpringUtil.getBean(CoreCache.class);


        return ResultJson.ok().data(null);
    }*/


}

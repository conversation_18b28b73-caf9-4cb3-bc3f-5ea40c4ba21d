package com.cw.controller.pms;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.CommonDelReq;
import com.cw.pojo.dto.pms.req.market.MarketReq;
import com.cw.pojo.dto.pms.req.market.QueryMarketReq;
import com.cw.pojo.dto.pms.req.market.UpdateMarketReq;
import com.cw.pojo.dto.pms.res.market.MarketListRes;
import com.cw.service.config.market.MarketService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @Description: 市场控制器
 * @Author: michael.pan
 * @Date: 2024/3/24 10:46
 */
@RestController
@RequestMapping(value = "/pmsapi/market")
@Api(tags = "系统设置-市场管理")
public class MarketController {

    @Resource
    private MarketService marketService;

    @ApiOperation(value = "添加市场")
    @PostMapping(value = "/add")
    private ResultJson addMarket(@Valid @RequestBody MarketReq marketReq) {
        marketService.addMarket(marketReq);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "修改市场")
    @PostMapping(value = "/update")
    private ResultJson updateMarket(@Valid @RequestBody UpdateMarketReq marketReq) {
        marketService.updateMarket(marketReq);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "查询市场")
    @PostMapping(value = "/list")
    private ResultJson listMarket(@Valid @RequestBody QueryMarketReq queryMarketReq) {
        MarketListRes marketListRes = marketService.listMarket(queryMarketReq);
        return ResultJson.ok().data(marketListRes);
    }

    @ApiOperation(value = "删除市场")
    @PostMapping(value = "/delete")
    public ResultJson deleteMarket(@RequestBody CommonDelReq req) {
        marketService.deleteMarket(req.getId());
        return ResultJson.ok().data(null);
    }
}

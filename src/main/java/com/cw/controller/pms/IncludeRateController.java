package com.cw.controller.pms;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.CommonDelReq;
import com.cw.pojo.dto.pms.req.rate.IncludeRateReq;
import com.cw.pojo.dto.pms.req.rate.QueryIncludeRateReq;
import com.cw.pojo.dto.pms.req.rate.UpdateIncludeRateReq;
import com.cw.pojo.dto.pms.res.rate.CodeRes;
import com.cw.pojo.dto.pms.res.rate.IncludeRateListRes;
import com.cw.service.config.rate.IncludeRateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @Classname IncludeRateController
 * @Description 包价控制器
 * @Date 2024-03-19 20:39
 * <AUTHOR> sancho.shen
 */

@RestController
@RequestMapping(value = "/pmsapi/include-rate")
@Api(tags = "系统设置-包价管理")
public class IncludeRateController {

    @Resource
    private IncludeRateService includeRateService;

    @ApiOperation(value = "添加包价")
    @PostMapping(value = "/add")
    public ResultJson addIncludeRate(@Valid @RequestBody IncludeRateReq includeRateReq) {
        includeRateService.addIncludeRate(includeRateReq);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "修改包价")
    @PostMapping(value = "/update")
    public ResultJson updateIncludeRate(@Valid @RequestBody UpdateIncludeRateReq updateIncludeRateReq) {
        includeRateService.updateIncludeRate(updateIncludeRateReq);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "包价列表")
    @PostMapping(value = "/list")
    public ResultJson<IncludeRateListRes> listIncludeRate(@Valid @RequestBody QueryIncludeRateReq queryIncludeRateReq) {
        IncludeRateListRes list = includeRateService.listIncludeRate(queryIncludeRateReq);
        return ResultJson.ok().data(list);
    }

    @ApiOperation(value = "删除包价")
    @PostMapping(value = "/delete")
    public ResultJson deleteIncludeRate(@RequestBody CommonDelReq req) {
        includeRateService.deleteIncludeRate(req.getId());
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "查询该酒店下的所有包价代码")
    @PostMapping(value = "/find-all-code")
    public ResultJson<List<CodeRes>> findAllCode() {
        List<CodeRes> allRoomTypeByCode = includeRateService.findAllCode();
        return ResultJson.ok().data(allRoomTypeByCode);
    }
}

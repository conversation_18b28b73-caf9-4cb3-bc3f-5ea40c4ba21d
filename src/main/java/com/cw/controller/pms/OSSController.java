package com.cw.controller.pms;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.pms.req.oss.OssFileInfoReq;
import com.cw.pojo.dto.pms.req.oss.OssRemoveFileReq;
import com.cw.pojo.dto.pms.req.oss.OssRenameFileReq;
import com.cw.pojo.dto.pms.req.oss.OssUploadFileReq;
import com.cw.pojo.dto.pms.res.oss.OssObjectInfo;
import com.cw.pojo.dto.pms.res.oss.OssUploadFileRes;
import com.cw.service.context.GlobalContext;
import com.cw.service.oss.OssService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * @Describe
 * <AUTHOR> Tony Leung
 * @Create on 2021/9/23 0023
 */
@Api(tags = "OSS文件上传")
@RestController
@RequestMapping(value = "api/oss", method = RequestMethod.POST)
public class OSSController {

    @Autowired
    OssService ossService;


    @ApiOperation(value = "OSS设置 - 上传文件", notes = "OSS上传文件")
    @RequestMapping(value = "/upload_file", method = RequestMethod.POST)
    @ApiImplicitParam(name = "file", value = "上传文件", required = true, dataType = "__File")
    public ResultJson<OssUploadFileRes> upload_file(@RequestPart MultipartFile file, OssUploadFileReq req) {
        String hotelId = GlobalContext.getCurrentHotelId();
        Map<String, String> userMetadataMap = null;
        if (StringUtils.isNotBlank(req.getUserMetadata())) {
            userMetadataMap = JSON.parseObject(req.getUserMetadata(), new TypeReference<HashMap<String, String>>() {
            });
        }
        return ResultJson.ok().data(ossService.upLoadFile(file, req.getFileType(),  hotelId, userMetadataMap));
    }


    @ApiOperation(value = "OSS设置 - 删除文件", notes = "OSS删除文件")
    @RequestMapping(value = "/delete_file", method = RequestMethod.POST)
    public ResultJson delete_file(OssRemoveFileReq req) {
        return ResultJson.ok().data(ossService.removeFileByUrl(req.getOssFileUrl()));
    }




    //@ApiOperation(value = "OSS设置 - 下载项目所有文件到本地目录", notes = "下载项目所有文件到本地目录")
    //@RequestMapping(value = "/download", method = RequestMethod.POST)
    //public ResultJson<OssObjectList> download() throws IOException {
    //    String hotelId = GlobalContext.getCurrentHotelId();
    //    ossService.downloadProjectFile(hotelId);
    //    return ResultJson.ok();
    //}


    @ApiOperation(value = "OSS设置 - 获取OSS文件自定义信息", notes = "获取OSS文件自定义信息")
    @RequestMapping(value = "/fileInfo", method = RequestMethod.POST)
    public ResultJson<OssObjectInfo> fileInfo(@RequestBody OssFileInfoReq req) throws IOException {
        OssObjectInfo info = ossService.getOSSObjectInfo(req.getFileUrl());
        return ResultJson.ok().data(info);
    }

    //@ApiOperation(value = "OSS设置 - 获取素材中心所有目录", notes = "获取素材中心所有目录")
    //@RequestMapping(value = "/get_all_dir", method = RequestMethod.POST)
    //public ResultJson<List<SelectDataNode>> getAllDir(String type) {
    //    String hotelId = GlobalContext.getCurrentHotelId();
    //    List<SelectDataNode> list = ossService.getAllDir(hotelId, type);
    //    return ResultJson.ok().data(list);
    //}
    //
    //@ApiOperation(value = "OSS设置 - 获取当前目录下所有文件夹和文件", notes = "获取当前目录下所有文件夹和文件")
    //@RequestMapping(value = "/list", method = RequestMethod.POST)
    //public ResultJson<OssObjectList> fileList(OssQueryReq req) {
    //    String hotelId = GlobalContext.getCurrentHotelId();
    //    return ResultJson.ok().data(ossService.getAllFileUrl(req, hotelId));
    //}
    //
    //@ApiOperation(value = "OSS设置 - 新建目录", notes = "新建目录")
    //@RequestMapping(value = "/mkdir", method = RequestMethod.POST)
    //public ResultJson<Ossdir_Entity> mkdir(@RequestBody OssMkdirReq req) throws IOException {
    //    Ossdir ossdir = ossService.mkdir(req);
    //    Ossdir_Entity entity = new Ossdir_Entity();
    //    BeanUtil.copyProperties(ossdir, entity);
    //    return ResultJson.ok().data(entity);
    //}
    //
    //@ApiOperation(value = "OSS设置 - 重命名目录", notes = "重命名目录")
    //@RequestMapping(value = "/rename_dir", method = RequestMethod.POST)
    //public ResultJson<Ossdir_Entity> rename_dir(@RequestBody OssRenameDirReq req) throws IOException {
    //    Ossdir ossdir = ossService.renameDir(req.getPath(), req.getNewName());
    //    Ossdir_Entity entity = new Ossdir_Entity();
    //    BeanUtil.copyProperties(ossdir, entity);
    //    return ResultJson.ok().data(entity);
    //}
    //
    //@ApiOperation(value = "OSS设置 - 删除目录及其文件", notes = "删除目录及其文件")
    //@RequestMapping(value = "/delete_dir", method = RequestMethod.POST)
    //public ResultJson delete_dir(@RequestBody OssDeleteDirAndFilesReq req) throws IOException {
    //    ossService.deleteDirAndFile(req.getPath());
    //    return ResultJson.ok();
    //}


    @ApiOperation(value = "OSS设置 - 文件重命名", notes = "文件重命名")
    @RequestMapping(value = "/rename_file", method = RequestMethod.POST)
    public ResultJson rename_file(@RequestBody OssRenameFileReq req) throws IOException {
        ossService.renameFile(req.getOldUrl(), req.getNewName());
        return ResultJson.ok();
    }

}

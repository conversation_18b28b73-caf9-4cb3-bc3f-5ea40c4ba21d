package com.cw.controller.pms;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.CommonDelReq;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.pms.req.room.QueryRoomReq;
import com.cw.pojo.dto.pms.req.room.RoomInfoReq;
import com.cw.pojo.dto.pms.req.room.UpdateRoomInfoReq;
import com.cw.pojo.dto.pms.res.room.RoomListRes;
import com.cw.service.config.room.RoomService;
import com.cw.utils.annotion.LockOp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.cw.pojo.dto.pms.res.room.RoomRes;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 房间控制器
 * @Author: michael.pan
 * @Date: 2024/3/20 23:19
 */
@RestController
@RequestMapping("/pmsapi/room")
@Api(tags = "系统设置-房间管理")
public class RoomController {

    @Resource
    private RoomService roomService;

    @ApiOperation(value = "添加房间")
    @PostMapping(value = "/add")
    @LockOp
    public ResultJson addRoom(@Valid @RequestBody RoomInfoReq roomInfoReq){ // 接口的修饰符必须是public,JDK 动态代理：基于接口实现，只能代理 public 方法
        roomService.addRoom(roomInfoReq);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "修改房间")
    @PostMapping(value = "/update")
    public ResultJson updateRoom(@Valid @RequestBody UpdateRoomInfoReq updateRoomInfoReq) {
        roomService.updateRoom(updateRoomInfoReq);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "加载房间")
    @PostMapping(value = "/load")
    public ResultJson<RoomRes> load(@RequestBody Common_Load_Req req) {
        RoomRes roomRes = roomService.load(req);
        return ResultJson.ok().data(roomRes);
    }

    @ApiOperation(value = "房间列表")
    @PostMapping(value = "/list")
    public ResultJson<RoomListRes> listRooms(@Valid @RequestBody QueryRoomReq queryRoomReq) {
        RoomListRes roomListRes = roomService.listRooms(queryRoomReq);
        return ResultJson.ok().data(roomListRes);
    }

    @ApiOperation(value = "删除房间")
    @PostMapping(value = "/delete")
    public ResultJson deleteRoom(@RequestBody CommonDelReq req) {
        roomService.deleteRoom(req.getId());
        return ResultJson.ok().data(null);
    }


}

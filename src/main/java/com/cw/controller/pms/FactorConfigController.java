package com.cw.controller.pms;

import com.cw.entity.Factor;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.CommonDelReq;
import com.cw.pojo.dto.common.req.CommonSeqReq;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.pms.req.factor.FactorHeaderReq;
import com.cw.pojo.dto.pms.req.factor.FactorListReq;
import com.cw.pojo.dto.pms.res.factor.FactorListRes;
import com.cw.pojo.entity.FactorDataEntity;
import com.cw.pojo.entity.FactorEntity;
import com.cw.service.config.factor.FactorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Describe
 * <AUTHOR> Tony Leung
 * @Create on 2021/9/23 0023
 */
@Api(tags = "基础配置")
@RestController
@RequestMapping(value = "api/factor", method = RequestMethod.POST)
public class FactorConfigController {

    @Autowired
    private FactorService factorService;


    @ApiOperation(value = "公共数据设置 - 公共数据列表", notes = "获取公共数据列表数据")
    @RequestMapping(value = "/factor_list")
    public ResultJson<FactorListRes> factor_list(@RequestBody FactorListReq req) {
        return ResultJson.ok().data(factorService.queryFactorList(req));
    }

    @ApiOperation(value = "公共数据设置 - 获取公共数据对象", notes = "根据唯一id获取公共数据对象,id=0返回带默认值未保存对象")
    @RequestMapping(value = "/load_factor")
    public ResultJson<FactorEntity> load_factor(@RequestBody Common_Load_Req req) {
        Factor factor = null;
        if (req.getId() != null && req.getId() > 0) {
            factor = factorService.loadFactor(req.getId());
        } else {
            factor = new Factor();
        }
        FactorEntity entity = new FactorEntity();
        BeanUtils.copyProperties(factor, entity);
        //entity.setHeader(factorService.tansToHeaderName(entity.getHeader(), entity.getType()));//转中文
        return ResultJson.ok().data(entity);
    }

    @ApiOperation(value = "公共数据设置 - 保存公共数据", notes = "保存公共数据")
    @RequestMapping(value = "/save_factor")
    public ResultJson<FactorEntity> save_factor(@RequestBody FactorEntity req) {
        FactorEntity entity = factorService.saveFactor(req);
        return ResultJson.ok().data(entity);
    }

    @ApiOperation(value = "公共数据设置 - 删除公共数据", notes = "删除公共数据")
    @RequestMapping(value = "/delete_factor")
    public ResultJson delete_factor(@RequestBody CommonDelReq req) {
        factorService.deleteFactor(req.getId());
        return ResultJson.ok();
    }


    @ApiOperation(value = "公共数据设置 - 更新当前设置状态，当前状态取反", notes = "更新状态")
    @RequestMapping(value = "/change_status")
    public ResultJson change_status(@RequestBody Common_Load_Req req) {
        factorService.updateStatus(req.getId());
        return ResultJson.ok();
    }

    @ApiOperation(value = "公共数据设置 - 更新当前排序", notes = "更新排序")
    @RequestMapping(value = "/change_seq")
    public ResultJson change_seq(@RequestBody CommonSeqReq req) {
        factorService.updateSeq(req);
        return ResultJson.ok();
    }

    @ApiOperation(value = "公共数据设置 - 获取指定Header及其子项所有数据", notes = "获取指定Header及其子项所有数据")
    @RequestMapping(value = "/header_list")
    public ResultJson<List<FactorDataEntity>> header_list(@Validated @RequestBody FactorHeaderReq req) {
        return ResultJson.ok().data(factorService.getFactorAndChildListData(req.getSearchKey()));
    }


}

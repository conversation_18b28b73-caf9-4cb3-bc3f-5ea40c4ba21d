package com.cw.controller.pms;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.CommonDelReq;
import com.cw.pojo.dto.pms.req.reservationtype.QueryReservationTypeReq;
import com.cw.pojo.dto.pms.req.reservationtype.ReservationTypeReq;
import com.cw.pojo.dto.pms.req.reservationtype.UpdateReservationTypeReq;
import com.cw.pojo.dto.pms.res.reservationtype.ReservationTypeListRes;
import com.cw.service.config.reservationtype.ReservationTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @Description: 预定类型控制器
 * @Author: michael.pan
 * @Date: 2024/3/24 10:46
 */
@RestController
@RequestMapping(value = "/pmsapi/reservation-type")
@Api(tags = "系统设置-预定类型")
public class ReservationTypeController {

    @Resource
    private ReservationTypeService ReservationTypeService;

    @ApiOperation(value = "添加预定类型")
    @PostMapping(value = "/add")
    private ResultJson addReservationType(@Valid @RequestBody ReservationTypeReq reservationTypeReq) {
        ReservationTypeService.addReservationType(reservationTypeReq);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "修改预定类型")
    @PostMapping(value = "/update")
    private ResultJson updateReservationType(@Valid @RequestBody UpdateReservationTypeReq reservationTypeReq) {
        ReservationTypeService.updateReservationType(reservationTypeReq);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "查询预定类型")
    @PostMapping(value = "/list")
    private ResultJson listReservationType(@Valid @RequestBody QueryReservationTypeReq queryReservationTypeReq) {
        ReservationTypeListRes marketListRes = ReservationTypeService.listReservationType(queryReservationTypeReq);
        return ResultJson.ok().data(marketListRes);
    }

    @ApiOperation(value = "删除预定类型")
    @PostMapping(value = "/delete")
    public ResultJson deleteReservationType(@RequestBody CommonDelReq req) {
        ReservationTypeService.deleteReservationType(req.getId());
        return ResultJson.ok().data(null);
    }
}

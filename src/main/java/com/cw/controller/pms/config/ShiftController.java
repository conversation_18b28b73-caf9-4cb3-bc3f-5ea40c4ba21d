package com.cw.controller.pms.config;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.CommonQueryPageReq;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.pojo.dto.pms.req.others.ShiftEntity;
import com.cw.pojo.dto.pms.req.others.res.ShiftListRes;
import com.cw.service.config.base.son.roomstaff.ShiftServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/5/14
 **/
@RestController
@Api(tags = "班次表管理")
@RequestMapping(value = "/pmsapi/shift")
public class ShiftController {


    @Resource
    private ShiftServiceImpl service;

    @ApiOperation(value = "保存班次表")
    @PostMapping(value = "/save")
    public ResultJson<ShiftEntity> save(@RequestBody ShiftEntity entity) {
        ShiftEntity savedEntity = service.save(entity);
        return ResultJson.ok().data(savedEntity);
    }


    @ApiOperation(value = "加载班次表")
    @PostMapping(value = "/load")
    public ResultJson<ShiftEntity> load(@RequestBody Common_Load_Req req) {
        ShiftEntity entity = service.findById(req.getId());
        return ResultJson.ok().data(entity);
    }

    @ApiOperation(value = "删除班次表")
    @PostMapping(value = "/delete")
    public ResultJson<Common_response> delete(@RequestBody Common_Load_Req req) {
        service.deleteById(req.getId());
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperation(value = "分页查询班次表")
    @PostMapping(value = "/list")
    public ResultJson<ShiftListRes> list(@RequestBody CommonQueryPageReq req) {
        return ResultJson.ok().data(service.pagelist(req));
    }
}

package com.cw.controller.pms.config;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.CommonQueryPageReq;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.pojo.dto.pms.req.others.DoorlockEntity;
import com.cw.pojo.dto.pms.req.others.SalerEntity;
import com.cw.pojo.dto.pms.req.others.res.DoorlockListRes;
import com.cw.pojo.dto.pms.req.others.res.SalesListRes;
import com.cw.service.config.base.son.DoorlockServiceImpl;
import com.cw.service.config.base.son.SalerServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2025/1/3 23:13
 **/
@RestController
@Api(tags = "销售员表管理")
@RequestMapping(value = "/pmsapi/saler")
public class SalerController {

    @Resource
    private SalerServiceImpl service;

    @ApiOperation(value = "保存销售员")
    @PostMapping(value = "/save")
    public ResultJson<SalerEntity> save(@RequestBody SalerEntity entity) {
        SalerEntity savedEntity = service.save(entity);
        return ResultJson.ok().data(savedEntity);
    }


    @ApiOperation(value = "加载销售员信息")
    @PostMapping(value = "/load")
    public ResultJson<SalerEntity> load(@RequestBody Common_Load_Req req) {
        SalerEntity entity = service.findById(req.getId());
        return ResultJson.ok().data(entity);
    }

    @ApiOperation(value = "删除销售员")
    @PostMapping(value = "/delete")
    public ResultJson<Common_response> delete(@RequestBody Common_Load_Req req) {
        service.deleteById(req.getId());
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperation(value = "分页查询销售员列表")
    @PostMapping(value = "/list")
    public ResultJson<SalesListRes> list(@RequestBody CommonQueryPageReq req) {
        return ResultJson.ok().data(service.pagelist(req));
    }

    @ApiOperation(value = "改变启用状态")
    @PostMapping(value = "/changestatus")
    public ResultJson<SalesListRes> changestatus(@RequestBody Common_Load_Req req) {
        service.changeStatus(req.getId());
        return ResultJson.ok().data(new Common_response());
    }
}

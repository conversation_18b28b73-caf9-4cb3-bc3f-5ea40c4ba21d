package com.cw.controller.pms.config;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.CommonQueryPageReq;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.pojo.dto.pms.req.others.DoorlockEntity;
import com.cw.pojo.dto.pms.req.others.FloorEntity;
import com.cw.pojo.dto.pms.req.others.res.DoorlockListRes;
import com.cw.pojo.dto.pms.req.others.res.FloorListRes;
import com.cw.service.config.base.son.DoorlockServiceImpl;
import com.cw.service.config.base.son.FloorServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2025/1/3 23:13
 **/
@RestController
@Api(tags = "楼层表管理")
@RequestMapping(value = "/pmsapi/floor")
public class FloorController {

    @Resource
    private FloorServiceImpl service;

    @ApiOperation(value = "保存楼层")
    @PostMapping(value = "/save")
    public ResultJson<FloorEntity> save(@RequestBody FloorEntity entity) {
        FloorEntity savedEntity = service.save(entity);
        return ResultJson.ok().data(savedEntity);
    }


    @ApiOperation(value = "根据id查询楼层信息")
    @PostMapping(value = "/load")
    public ResultJson<FloorEntity> load(@RequestBody Common_Load_Req req) {
        FloorEntity entity = service.findById(req.getId());
        return ResultJson.ok().data(entity);
    }

    @ApiOperation(value = "删除楼层信息")
    @PostMapping(value = "/delete")
    public ResultJson<Common_response> delete(@RequestBody Common_Load_Req req) {
        service.deleteById(req.getId());
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperation(value = "分页查询楼层信息")
    @PostMapping(value = "/list")
    public ResultJson<FloorListRes> list(@RequestBody CommonQueryPageReq req) {
        return ResultJson.ok().data(service.pagelist(req));
    }

}

package com.cw.controller.pms.config;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.CommonQueryPageReq;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.pojo.dto.pms.req.others.RoomStaffEntity;
import com.cw.pojo.dto.pms.req.others.RoomStaffShiftEntity;
import com.cw.pojo.dto.pms.req.others.ShiftEntity;
import com.cw.pojo.dto.pms.req.others.res.RoomStaffShiftRes;
import com.cw.pojo.dto.pms.req.others.res.ShiftListRes;
import com.cw.service.config.base.son.roomstaff.RoomStaffServiceImpl;
import com.cw.service.config.base.son.roomstaff.RoomStaffShistServiceImpl;
import com.cw.service.config.base.son.roomstaff.ShiftServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/5/15
 **/

@RestController
@Api(tags = "房务人员排班表管理")
@RequestMapping(value = "/pmsapi/staffshift")
public class RoomStaffShiftController {

    @Resource
    private RoomStaffShistServiceImpl service;

    @ApiOperation(value = "保存房务人员排班表")
    @PostMapping(value = "/save")
    public ResultJson<RoomStaffShiftEntity> save(@RequestBody RoomStaffShiftEntity entity) {
        RoomStaffShiftEntity savedEntity = service.save(entity);
        return ResultJson.ok().data(savedEntity);
    }


    @ApiOperation(value = "加载房务人员排班表")
    @PostMapping(value = "/load")
    public ResultJson<RoomStaffShiftEntity> load(@RequestBody Common_Load_Req req) {
        RoomStaffShiftEntity entity = service.findById(req.getId());
        return ResultJson.ok().data(entity);
    }

    @ApiOperation(value = "删除房务人员排班表")
    @PostMapping(value = "/delete")
    public ResultJson<Common_response> delete(@RequestBody Common_Load_Req req) {
        service.deleteById(req.getId());
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperation(value = "分页查询房务人员排班表")
    @PostMapping(value = "/list")
    public ResultJson<RoomStaffShiftRes> list(@RequestBody CommonQueryPageReq req) {
        return ResultJson.ok().data(service.pagelist(req));
    }

    @ApiOperation(value = "改变排班表状态(ACTIVE->CLOSE,CLOSE->ACTIVE)")
    @PostMapping(value = "/change")
    public ResultJson<RoomStaffShiftEntity> change(@RequestBody Common_Load_Req req) {
        service.changeStatus(req.getId());
        return ResultJson.ok().data(new Common_response());
    }
}

package com.cw.controller.pms.config;

import com.cw.exception.DefinedException;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.app.res.AppDlPasswordRes;
import com.cw.pojo.dto.common.req.CommonQueryPageReq;
import com.cw.pojo.dto.common.req.CommonQueryReq;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.pojo.dto.pms.req.others.DoorlockEntity;
import com.cw.pojo.dto.pms.req.others.res.DoorlockListRes;
import com.cw.pojo.dto.pms.req.others.res.DoorLockPasswordDeleteRes;
import com.cw.pojo.dto.pms.req.others.res.DoorLockPasswordQueryRes;
import com.cw.pojo.dto.pms.req.others.res.DoorLockPasswordRes;
import com.cw.pojo.dto.pms.req.room.*;
import com.cw.pojo.dto.pms.res.reservation.ReservationRes;
import com.cw.service.config.base.son.DoorlockServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2025/1/3 23:13
 **/
@Slf4j
@RestController
@Api(tags = "门锁管理")
@RequestMapping(value = "/pmsapi/doorlock")
public class DoorlockController {

    @Resource
    private DoorlockServiceImpl service;

    @ApiOperation(value = "保存门锁信息")
    @PostMapping(value = "/save")
    public ResultJson<DoorlockEntity> save(@RequestBody DoorlockEntity entity) {
        DoorlockEntity savedEntity = service.save(entity);
        return ResultJson.ok().data(savedEntity);
    }


    @ApiOperation(value = "根据id 或者房间号查询门锁信息")
    @PostMapping(value = "/load")
    public ResultJson<DoorlockEntity> load(@RequestBody Common_Load_Req req) {
        DoorlockEntity entity = service.find(req);
        return ResultJson.ok().data(entity);
    }


    @ApiOperation(value = "根据预订号查询当前电子密码")
    @PostMapping(value = "/loadSecrect")
    public ResultJson<Common_response> loadSecrect(@RequestBody Common_Load_Req req) {
        Common_response entity = service.loadSecret(req);
        return ResultJson.ok().data(entity);
    }

    @ApiOperation(value = "删除门锁信息")
    @PostMapping(value = "/delete")
    public ResultJson<Common_response> delete(@RequestBody Common_Load_Req req) {
        service.deleteById(req.getId());
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperation(value = "分页查询门锁信息")
    @PostMapping(value = "/list")
    public ResultJson<DoorlockListRes> list(@RequestBody DoorlockPageReq req) {
        return ResultJson.ok().data(service.pagelist(req));
    }

    @ApiOperation(value = "根据门锁号反查预订订单号")
    @PostMapping(value = "/locknoSearch")
    public ResultJson<ReservationRes> searchRs(@RequestBody CommonQueryReq req) {
        ReservationRes res = new ReservationRes();
        String rsNo = service.locknoSearch(req);
        res.setReservationNumber(rsNo);
        return ResultJson.ok().data(res);
    }

    // ==================== 门锁密码功能 API ====================

    @ApiOperation(value = "生成房间电子密码")
    @PostMapping(value = "/password/generate")
    public ResultJson<DoorLockPasswordRes> generatePassword(@RequestBody DoorLockPasswordGenerateReq req) throws Exception {
        DoorLockPasswordRes result = service.generateRoomPassword(req);
        return ResultJson.ok().data(result);

    }

 /*   @ApiOperation(value = "删除房间电子密码")
    @PostMapping(value = "/password/delete")
    public ResultJson<DoorLockPasswordDeleteRes> deletePassword(@RequestBody DoorLockPasswordDeleteReq req) {
            DoorLockPasswordDeleteRes result = service.deleteRoomPassword(req);
            return ResultJson.ok().data(result);
    }

    @ApiOperation(value = "查询房间密码列表")
    @PostMapping(value = "/password/query")
    public ResultJson<DoorLockPasswordQueryRes> queryPasswordList(@RequestBody DoorLockPasswordQueryReq req) {
        try {
            DoorLockPasswordQueryRes result = service.queryRoomPasswordList(req);
            if (result.isSuccess()) {
                return ResultJson.ok().data(result).msg("查询成功");
            } else {
                return ResultJson.failure().data(result).msg(result.getErrorMsg());
            }
        } catch (DefinedException e) {
            log.error("查询房间密码列表失败：{}", e.getMessage(), e);
            return ResultJson.failure().data(DoorLockPasswordQueryRes.failure(e.getMessage())).msg(e.getMessage());
        } catch (Exception e) {
            log.error("查询房间密码列表异常：{}", e.getMessage(), e);
            return ResultJson.failure().data(DoorLockPasswordQueryRes.failure("查询异常：" + e.getMessage())).msg("查询异常：" + e.getMessage());
        }
    }

    @ApiOperation(value = "生成入住密码")
    @PostMapping(value = "/password/checkin")
    public ResultJson<DoorLockPasswordRes> generateCheckInPassword(@RequestBody DoorLockCheckInPasswordReq req) {
        try {
            DoorLockPasswordRes result = service.generateCheckInPassword(req);
            if (result.isSuccess()) {
                return ResultJson.ok().data(result).msg("入住密码生成成功");
            } else {
                return ResultJson.failure().data(result).msg(result.getErrorMsg());
            }
        } catch (DefinedException e) {
            log.error("生成入住密码失败：{}", e.getMessage(), e);
            return ResultJson.failure().data(DoorLockPasswordRes.failure(e.getMessage())).msg(e.getMessage());
        } catch (Exception e) {
            log.error("生成入住密码异常：{}", e.getMessage(), e);
            return ResultJson.failure().data(DoorLockPasswordRes.failure("生成入住密码异常：" + e.getMessage())).msg("生成入住密码异常：" + e.getMessage());
        }
    }

    @ApiOperation(value = "生成临时密码")
    @PostMapping(value = "/password/temp")
    public ResultJson<DoorLockPasswordRes> generateTempPassword(@RequestBody DoorLockTempPasswordReq req) {
        try {
            DoorLockPasswordRes result = service.generateTempPassword(req);
            if (result.isSuccess()) {
                return ResultJson.ok().data(result).msg("临时密码生成成功");
            } else {
                return ResultJson.failure().data(result).msg(result.getErrorMsg());
            }
        } catch (DefinedException e) {
            log.error("生成临时密码失败：{}", e.getMessage(), e);
            return ResultJson.failure().data(DoorLockPasswordRes.failure(e.getMessage())).msg(e.getMessage());
        } catch (Exception e) {
            log.error("生成临时密码异常：{}", e.getMessage(), e);
            return ResultJson.failure().data(DoorLockPasswordRes.failure("生成临时密码异常：" + e.getMessage())).msg("生成临时密码异常：" + e.getMessage());
        }
    }*/

}

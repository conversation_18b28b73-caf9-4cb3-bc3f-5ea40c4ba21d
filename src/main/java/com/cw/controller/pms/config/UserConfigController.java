package com.cw.controller.pms.config;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.CommonDelReq;
import com.cw.pojo.dto.common.req.CommonQueryPageReq;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.common.req.Common_Switch_Req;
import com.cw.pojo.dto.pms.req.users.UserUpdPwdReq;
import com.cw.pojo.dto.pms.res.users.OpRoleListRes;
import com.cw.pojo.dto.pms.res.users.OpUserListRes;
import com.cw.pojo.entity.OpRoleEntity;
import com.cw.pojo.entity.OpUserEntity;
import com.cw.service.config.user.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/9/26 18:17
 **/
@Api(tags = "用户与角色权限配置")
@RestController
@RequestMapping(value = "api/user_conf", method = RequestMethod.POST)
public class UserConfigController {

    @Autowired
    UserService userService;
    @ApiOperation(value = "用户设置 - 用户列表", notes = "获取用户列表数据")
    @RequestMapping(value = "/user_list")
    public ResultJson<OpUserListRes> fetchUserList(@RequestBody CommonQueryPageReq req) {
        return ResultJson.ok().data(userService.queryTableData(req));
    }

    @ApiOperation(value = "用户设置 - 获取用户对象", notes = "根据唯一id获取用户对象,id=0返回带默认值未保存对象")
    @RequestMapping(value = "/load_user")
    public ResultJson<OpUserEntity> loadUser(@RequestBody Common_Load_Req req) {
        OpUserEntity opUserEntity = userService.loadUser(req.getId());
        return ResultJson.ok().data(opUserEntity);
    }

    @ApiOperation(value = "用户设置 -保存用户", notes = "保存用户")
    @RequestMapping(value = "/save_user")
    public ResultJson<OpUserEntity> saveUser(@Valid @RequestBody OpUserEntity entity) {
        OpUserEntity data = userService.saveUser(entity);
        return ResultJson.ok().data(data);
    }

    @RequestMapping(value = "/del_user")
    @ApiOperation(value = "用户设置 - 删除用户", notes = "删除用户")
    public ResultJson deleteUser(@RequestBody CommonDelReq req) {
        userService.deleteUser(req.getId());
        return ResultJson.ok();
    }

    @RequestMapping(value = "/upd_pwd")
    @ApiOperation(value = "用户设置 - 更改密码", notes = "更改密码")
    public ResultJson updateUserPwd(@RequestBody UserUpdPwdReq req) {
        userService.updPassword(req);
        return ResultJson.ok();
    }

    @RequestMapping(value = "/ch_status")
    @ApiOperation(value = "用户设置 - 启用/关闭用户", notes = " 更改用户状态")
    public ResultJson chStatus(@RequestBody Common_Switch_Req req) {
        userService.updStatus(req);
        return ResultJson.ok();
    }



    @RequestMapping(value = "/role_list")
    @ApiOperation(value = "用户设置 - 角色列表", notes = "获取角色列表数据")
    public ResultJson<OpRoleListRes> fetchRoleList(@RequestBody CommonQueryPageReq req) {
        return ResultJson.ok().data(userService.queryRoleTableData(req));
    }

    @ApiOperation(value = "用户设置 - 获取角色对象", notes = "根据唯一id获取角色对象,id=0返回带默认值未保存对象")
    @RequestMapping(value = "/load_role")
    public ResultJson<OpRoleEntity> loadRole(@RequestBody Common_Load_Req req) {
        OpRoleEntity opRole = userService.loadRole(req.getId());

        return ResultJson.ok().data(opRole);
    }

    @ApiOperation(value = "用户设置 - 保存角色", notes = "保存角色")
    @RequestMapping(value = "/save_role")
    public ResultJson<OpRoleEntity> saveRole(@Valid @RequestBody OpRoleEntity entity) {
        OpRoleEntity opRole = userService.saveRole(entity);
        return ResultJson.ok().data(opRole);
    }


    @ApiOperation(value = "用户设置 - 删除角色", notes = "删除角色")
    @RequestMapping(value = "/del_role")
    public ResultJson deleteRole(@RequestBody CommonDelReq req) {
        userService.deleteRole(req.getId());
        return ResultJson.ok();
    }








}

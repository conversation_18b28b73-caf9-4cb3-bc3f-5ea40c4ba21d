package com.cw.mapper;

import com.cw.entity.Factor;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/9/23 0023
 */
public interface FactorMapper extends JpaRepository<Factor, Long>, JpaSpecificationExecutor<Factor> {

    @Query(value = "select * from Factor where code=?1 and hotelid=?2", nativeQuery = true)
    Factor countByCode(String code, String hotelId);

    @Query(value = "select count(sqlid) from Factor where  code=?1 and hotelid=?2", nativeQuery = true)
    long countCodeAndHotelId(String code, String hotelId);

    @Query(value = "select count(sqlid) from Factor where `type`=?1 and code=?2 and  header=?3 and hotelid=?4", nativeQuery = true)
    long countByTypeAndCodeAndHeader(String type, String code, String header, String hotelId);

    /**
     * 查找用户管理下指定header分组代码且状态开启的元素集合
     *
     * @param header
     * @param hotelId
     * @return
     */
    @Query(value = "select * from Factor where header=?1  and hotelid=?2 and status = 1  order by seq", nativeQuery = true)
    List<Factor> getAllHeaderAndChild(String header, String hotelId);
}

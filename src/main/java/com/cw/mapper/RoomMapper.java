package com.cw.mapper;

import com.cw.entity.Reservation;
import com.cw.entity.Room;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @Description: 房间数据映射
 * @Author: michael.pan
 * @Date: 2024/3/20 22:08
 */
public interface RoomMapper extends JpaRepository<Room, Long>, JpaSpecificationExecutor<Room> {
    int countByHotelIdAndRoomStatus(String hotelId, String roomStatus);

    int countByHotelIdAndLocc(String hotelId, int locc);

    int countByHotelIdAndCharacteristic(String hotelId, String characterName);

    int countByHotelIdAndRoomNo(String hotelId, String roomNo);

    int countByHotelIdAndRoomType(String hotelId, String roomType);

    int countAllByHotelId(String hotelId);

    List<Room> findAllByHotelIdAndRoomStatus(String hotelId, String roomStatus);

    List<Room> findAllByHotelIdAndLocc(String hotelId, int locc);

    Room findRoomByHotelIdAndRoomNo(String hotelId, String roomNo);

    List<Room> findAllByHotelIdAndRoomType(String hotelId, String roomType);

    List<Room> findAllByHotelIdAndCharacteristicAndRoomNoBetween(String hotelId, String characteristic, String roomNoStart, String roomNoEnd);

    List<Room> findAllByHotelId(String hotelId);

    /**
     * 更新对应房间为占用状态
     *
     * @param roomNo  房间号
     * @param hotelId 酒店id
     */
    @Transactional
    @Modifying
    @Query("UPDATE Room r SET r.locc = 1 WHERE r.roomNo = ?1 AND r.hotelId =?2")
    void updateRoomLocc(String roomNo, String hotelId);

    /**
     * 更新对应房间的状态
     *
     * @param locc         0表示空闲、1表示占用
     * @param roomStatus   房间状态
     * @param modifiedTime 修改时间
     * @param modifiedBy   修改人
     * @param roomNo       房间好
     * @param hotelId      酒店id
     */
    @Transactional
    @Modifying
    @Query("UPDATE Room r SET r.locc = ?1, r.roomStatus = ?2,r.modifiedTime = ?3,r.modifiedBy = ?4 WHERE r.roomNo = ?5 AND r.hotelId =?6")
    void updateRoomLoccAndDirty(int locc, String roomStatus, Date modifiedTime, String modifiedBy, String roomNo, String hotelId);


    /**
     * 房型对应房间号 房态状态更新为脏房
     *
     * @param roomType
     * @param roomNo
     * @param modifiedTime
     * @param modifiedfBy
     */
    @Transactional
    @Modifying
    @Query("UPDATE Room r SET r.roomStatus = 'DI',r.modifiedTime = ?4,r.modifiedBy = ?5 WHERE r.roomType = ?1 AND  r.roomNo =?2 AND r.hotelId =?3")
    void updateRoomStatusDirty(String roomType, String roomNo, String hotelId, Date modifiedTime, String modifiedfBy);

    /**
     * 根据房间号和酒店id查询房间
     *
     * @param roomNos
     * @param hotelId
     */
    @Query(value = "from Room where roomNo  in(?1) and hotelId=?2")
    List<Room> findByRoomNos(List<String> roomNos, String hotelId);

    @Query(value = "from Room where roomType  in(?1) and hotelId=?2 and roomStatus=?3")
    List<Room> findAllByHotelIdAndRoomStatusAndRoomType(List<String> roomTypes, String hotelId, String roomStatus);

    @Query(value = "from Room where roomType  in(?1) and hotelId=?2 and locc=?3")
    List<Room> findAllByHotelIdAndLoccAndRoomType(List<String> roomTypes, String hotelId, int locc);

    /**
     * 查询现有套房
     */
    @Query(value = "SELECT r FROM Room r WHERE r.hotelId = ?1 AND r.suirooms IS NOT NULL AND FUNCTION('LENGTH', r.suirooms) > 0")
    List<Room> findAllByHotelIdAndSuirooms(String hotelId);

    /**
     * 查询占用房
     *
     * @param hotelId
     * @param locc
     * @return
     */
    List<Room> findByHotelIdAndLocc(String hotelId, int locc);

    @Query("SELECT COUNT(DISTINCT r.id) "
            + "FROM Room r"
            + "   LEFT JOIN RoomType rt "
            + "    ON r.roomType = rt.roomType "
            + "    AND rt.hotelId =?1"
            + " WHERE "
            + "    r.hotelId = ?1"
            + "    AND (rt.generic = false OR rt.roomType IS NULL)"
    )
    int countByHotelIdExcludingSuiteRooms(String hotelId);
}

package com.cw.mapper;

import com.cw.entity.ReservationHis;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022/1/7 0007
 */
public interface ResrvationHisMapper extends JpaRepository<ReservationHis, Long>,
        JpaSpecificationExecutor<ReservationHis> {

    ReservationHis findByHotelIdAndReservationNumber(String hotelId, String reservationNumber);

}

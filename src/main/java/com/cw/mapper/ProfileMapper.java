package com.cw.mapper;

import com.cw.entity.Profile;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Classname ProfileMapper
 * @Description 档案映射
 * @Date 2024-03-27 20:42
 * <AUTHOR> sancho.shen
 */
public interface ProfileMapper extends JpaRepository<Profile, Long>, JpaSpecificationExecutor<Profile> {

    Profile findByHotelIdAndProfileNumber(String hotelId, String profileNumber);

    Profile findByHotelIdAndIdCard(String hotelId, String idCard);

    List<Profile> findAllByHotelId(String hotelId);

    @Transactional
    @Modifying
    @Query("UPDATE Profile p SET p.avatar = '' WHERE p.profileNumber = ?1")
    void updateAvatarByProfileNumber(String profileNumber);

    @Transactional
    @Modifying
    @Query("UPDATE Profile p SET p.frontPicture = '', p.backPicture ='' WHERE p.profileNumber = ?1")
    void updatePictureByProfileNumber(String profileNumber);

    @Transactional
    @Modifying
    @Query("UPDATE Profile p SET p.checkinNum = ?2 WHERE p.profileNumber = ?1")
    void updateQuantityByArchiveNumber(String profileNumber, int checkinNum);

    @Query(value = "SELECT * FROM profile WHERE hotelid = ?1 AND id_card = ?2 AND id_type = ?3", nativeQuery = true)
    Profile findByHotelIdAndIdCardAndIdType(String hotelId, @NotBlank(message = "idCard 不能为空")String idCard, @NotNull(message = "idType 不能为 null") Integer idType);
}

package com.cw.mapper;

import com.cw.entity.RoomRateDetail;
import com.cw.pojo.sqlresult.Produce_ratesqueryPo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import javax.persistence.NamedNativeQuery;
import java.util.Date;
import java.util.List;

/**
 * @Classname RoomRateDetailMapper
 * @Description 房价详情查询
 * @Date 2024-03-19 21:01
 * <AUTHOR> sancho.shen
 */

public interface RoomRateDetailMapper extends JpaRepository<RoomRateDetail, Long>, JpaSpecificationExecutor<RoomRateDetail> {

    @Query(value = "from RoomRateDetail where rateCode = ?1 and roomType = ?2 and hotelId = ?3   and  ( (endTime between ?4 and ?5 ) or (  startTime  <= ?4 and endTime >= ?5) )")
    List<RoomRateDetail> getCalcRoomRateDetailList(String rateCode, String roomType, String hotelId, Date calcStart, Date calcEnd);


    @Query(value = "select new com.cw.pojo.sqlresult.Produce_ratesqueryPo(r.rateCode,r.roomType,r.rate1,r.weekRate1) from RoomRateDetail r where r.rateCode = ?1 and r.hotelId = ?2 and r.startTime <= ?3 and r.endTime >= ?3 ")
    List<Produce_ratesqueryPo> rateQuery(String rateCode, String hotelId, Date calcDate);


    @Query(value = "select count(*) from room_rate_detail where rate_code=?1 and id<>?2 and room_type=?3 and `hotelid`=?4 and " +
            "((start_time<=?5 and end_time>=?5) or (start_time>=?5 and start_time<=?6))", nativeQuery = true)
    int countByCodeAndSqlidNot(String rateCode, Long sqlid, String roomType, String hotelId, Date startdate, Date enddate);

}

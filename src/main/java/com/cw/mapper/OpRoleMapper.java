package com.cw.mapper;

import com.cw.entity.OpRole;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2024-03-21
 */
public interface OpRoleMapper extends JpaRepository<OpRole, Long>, JpaSpecificationExecutor<OpRole> {


    @Query(value = "select count(id) from Op_role where roleid=?1 and hotelid=?2 and id<>?3", nativeQuery = true)
    long countByRoleIdAndIdNot(String roleId, String hotelId, Long id);

    /**
     *
     * @param roleId
     * @param hotelId
     * @return 获取对应参数最新角色
     */
    @Query(value = "select * from Op_role where roleid=?1 and hotelid=?2 order by id desc limit 1", nativeQuery = true)
    OpRole findLikeRoleIdAndHotelId(String roleId, String hotelId);
}

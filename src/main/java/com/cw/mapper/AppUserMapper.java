package com.cw.mapper;

import com.cw.entity.AppUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2025/6/24 12:02
  */
public interface AppUserMapper extends JpaRepository<AppUser, Long>, JpaSpecificationExecutor<AppUser> {

    /**
     * 通过用户userid获取用户
     * @param userid
     * @return
     */
    AppUser findAppUserByUserid(String userid);

    /***
     * 通过手机号获取用户
     * @param mobileNo 手机号码
     * @return
     */
    AppUser findAppUserByMobileNo(String mobileNo);
}

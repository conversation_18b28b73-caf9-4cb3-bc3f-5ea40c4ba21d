package com.cw.mapper;

import com.cw.entity.DailyStat;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Date;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2024-06-21
 */
public interface DailyStatMapper extends JpaRepository<DailyStat, Long>, JpaSpecificationExecutor<DailyStat> {

    DailyStat findDailyStatByHotelIdAndDatum(String hotelId, Date datum);

    List<DailyStat> findDailyStatByHotelIdAndDatumBetween(String hotelId, Date startDate, Date endDate);
}

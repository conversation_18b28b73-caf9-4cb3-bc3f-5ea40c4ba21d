package com.cw.mapper;

import com.cw.entity.Channel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;


/**
 * @Description: 渠道数据表映射
 * @Author: michael.pan
 * @Date: 2024/3/23 18:55
 */
public interface ChannelMapper extends JpaRepository<Channel, Long>, JpaSpecificationExecutor<Channel> {
    @Query("SELECT min(id) FROM Channel  WHERE hotelId =?1 AND description=?2")
    Long findMinIdByDescription(String hotelId, String description);

    int countByHotelIdAndDescription(String hotelId, String description);
}

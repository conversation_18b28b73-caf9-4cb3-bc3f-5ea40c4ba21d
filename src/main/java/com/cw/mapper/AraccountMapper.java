package com.cw.mapper;

import java.math.BigDecimal;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import com.cw.entity.ArAccount;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/10/23 11:01
 **/
public interface AraccountMapper extends JpaRepository<ArAccount, Long>, JpaSpecificationExecutor<ArAccount> {

	ArAccount findByHotelIdAndCode(String hotelId, String code);

//	/**
//	 * 全量方式同步应收余额
//	 * 
//	 * @param hotelId
//	 * @param ar_no
//	 * @param balance
//	 */
//	@Transactional
//	@Modifying
//	@Query(value = "update ArAccount set noacc=:noacc, credit=:credit, adjust=:adjust, writeoff=:writeoff,balance=:balance where hotelId=:hotelId and code=:ar_no")
//	void synBalance(String hotelId, String ar_no, BigDecimal noacc, BigDecimal credit, BigDecimal adjust, BigDecimal writeoff, BigDecimal balance);

//	/**
//	 * 全量方式同步应收余额noacc（待核销）、balance（可支付余额）
//	 * 
//	 * @param hotelId
//	 * @param ar_no
//	 * @param balance
//	 */
//	@Transactional
//	@Modifying
//	@Query(value = "update ArAccount set noacc=:noacc, credit=:credit, balance=:balance where hotelId=:hotelId and code=:ar_no")
//	void synBalance(String hotelId, String ar_no, BigDecimal noacc, BigDecimal balance);

//	/**
//	 * 增量方式同步应收余额
//	 * 
//	 * @param hotelId
//	 * @param ar_no
//	 * @param noacc
//	 * @param credit
//	 * @param adjust
//	 * @param writeoff
//	 */
//	@Transactional
//	@Modifying
//	@Query(value = "update ArAccount set noacc=noacc+:noacc, credit=credit+:credit, adjust=adjust+:adjust, writeoff=writeoff+:writeoff, balance=balance+:balance where hotelId=:hotelId and code=:ar_no")
//	void incArBalance(String hotelId, String ar_no, BigDecimal noacc, BigDecimal credit, BigDecimal adjust, BigDecimal writeoff, BigDecimal balance);

	@Query(value = "select new com.cw.pojo.sqlresult.ArAmounts(noacc, balance, credit, adjust, writeoff) from ArAccount where hotelId=:hotelId and code=:ar_no")
	List<com.cw.pojo.sqlresult.ArAmounts> viewAmounts(String hotelId, String ar_no);

}

package com.cw.mapper;

import com.cw.entity.Colrs;
import com.cw.entity.Hotel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 *
 */
public interface ColrsMapper extends JpaRepository<Colrs, Long>, JpaSpecificationExecutor<Colrs> {

    Colrs findColrsByHotelIdAndCrsno(String hotelId, String crsno);
}

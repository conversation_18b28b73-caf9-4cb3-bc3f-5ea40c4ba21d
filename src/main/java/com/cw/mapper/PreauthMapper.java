package com.cw.mapper;

import com.cw.entity.Preauth;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Description: 预授权表 Mapper (JPA)
 * @Author: Just
 * @Date: 2025-06-27
 */
@Repository
public interface PreauthMapper extends JpaRepository<Preauth, Long> {

    /**
     * 根据预定号查询所有预授权记录
     */
    List<Preauth> findByRsnoAndHotelId(String rsno, String hotelId);

} 
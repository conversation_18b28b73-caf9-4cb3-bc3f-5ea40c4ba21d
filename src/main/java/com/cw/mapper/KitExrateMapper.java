package com.cw.mapper;

import com.cw.entity.Kitexrate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface KitExrateMapper extends JpaRepository<Kitexrate, Long>, JpaSpecificationExecutor<Kitexrate> {

    @Modifying
    @Query("delete from Kitexrate where kitcode = :kitcode")
    void deleteByKitCode(@Param("kitcode") String kitCode);

    @Query("from Kitexrate k where k.kitcode = :kitcode")
    List<Kitexrate> findByKitCode(@Param("kitcode") String kitCode);
} 
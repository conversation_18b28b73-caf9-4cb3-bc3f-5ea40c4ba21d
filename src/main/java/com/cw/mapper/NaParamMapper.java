package com.cw.mapper;

import com.cw.entity.NaParam;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface NaParamMapper extends JpaRepository<NaParam, Long>, JpaSpecificationExecutor<NaParam> {

    @Override
    @Query("from NaParam ")
    List<NaParam> findAll();

    @Query(value = "select count(id) from Naparam where paramname=?1", nativeQuery = true)
    long countByParamName(String paramName);

    @Query(value = "from NaParam where hotelId=?1 and paramName=?2")
    NaParam findHotelIdParamValue(String hotelId,String paramName);

}

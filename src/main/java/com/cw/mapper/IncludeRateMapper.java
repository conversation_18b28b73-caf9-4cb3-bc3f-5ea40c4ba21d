package com.cw.mapper;

import com.cw.entity.IncludeRate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * @Classname IncludeRateMapper
 * @Description 包价数据映射
 * @Date 2024-03-19 21:00
 * <AUTHOR> sancho.shen
 */
public interface IncludeRateMapper extends JpaRepository<IncludeRate, Long>, JpaSpecificationExecutor<IncludeRate> {
    int countByHotelIdAndCode(String hotelId, String code);
}

package com.cw.mapper;

import com.cw.entity.NaConfig;
import com.cw.entity.PkgDaily;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

public interface Pkg_dailyMapper extends JpaRepository<PkgDaily, Long>, JpaSpecificationExecutor<NaConfig> {


    List<PkgDaily> findByHotelIdAndReservationNumber(String hotelId, String reservationNumber);


}

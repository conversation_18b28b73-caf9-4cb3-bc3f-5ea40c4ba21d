package com.cw.mapper;

import com.cw.entity.NaConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

public interface NaConfigMapper extends JpaRepository<NaConfig, Long>, JpaSpecificationExecutor<NaConfig> {


    List<NaConfig> findAllByOrderByHotelId();

    NaConfig findByHotelId(String hotelId);
}

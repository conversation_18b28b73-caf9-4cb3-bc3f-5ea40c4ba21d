package com.cw.mapper;

import com.cw.entity.RoomType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;


/**
 * @Description: 房型类型数据映射
 * @Author: michael.panD
 * @Date: 2024/3/18 21:16
 */
public interface RoomTypeMapper extends JpaRepository<RoomType, Long>, JpaSpecificationExecutor<RoomType> {
    int countByHotelIdAndRoomType(String hotelId, String roomType);

    int countByHotelIdAndBuildingNo(String hotelId, String buildingNo);

    int countByHotelIdAndDescription(String hotelId, String description);

    RoomType findByHotelIdAndDescription(String hotelId, String description);

    RoomType findByHotelIdAndRoomType(String hotelId,String roomType);

    List<RoomType> findAllByHotelId(String hotelId);
}

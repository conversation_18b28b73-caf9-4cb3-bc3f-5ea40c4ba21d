package com.cw.mapper;

import com.cw.entity.Building;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;


/**
 * @Description: 楼栋设置数据表映射
 * @Author: michael.pan
 * @Date: 2024/3/23 18:55
 */
public interface BuildingMapper extends JpaRepository<Building,Long>, JpaSpecificationExecutor<Building> {
    int countByHotelIdAndDescription(String hotelId, String description);
}

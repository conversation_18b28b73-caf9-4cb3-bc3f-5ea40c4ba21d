package com.cw.mapper;

import com.cw.entity.Hotel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * @Classname HotelMapper
 * @Description Hotel数据表映射
 * @Date 2024-03-15 22:52
 * <AUTHOR> sancho.shen
 */
public interface HotelMapper extends JpaRepository<Hotel, Long>, JpaSpecificationExecutor<Hotel> {
    List<Hotel> findAllByHotelId(String hotelId);
}

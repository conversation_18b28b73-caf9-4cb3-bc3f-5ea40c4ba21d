package com.cw.mapper;

import com.cw.entity.OpRole;
import com.cw.entity.Rmstat;
import com.cw.entity.Room;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/16
 **/
public interface RmstatMapper extends JpaRepository<Rmstat, Long>, JpaSpecificationExecutor<Rmstat> {

    List<Rmstat> findAllByHotelIdAndStatus(String hotelId, String status);
    List<Rmstat> findRoomByHotelIdAndRoomNo(String hotelId,String roomNo);
    List<Rmstat> findAllByHotelId(String hotelId);
    @Query(value = "SELECT * FROM Rmstat " +
            "WHERE hotelid = ?1 " +
            "AND room_no IN (?2) " +
            "AND (?3 IS NULL OR ?3 = '' OR status = ?3)",
            nativeQuery = true)
    List<Rmstat> findRmstatByRoomNos(String hotelId, List<String> roomNos, String status);
}

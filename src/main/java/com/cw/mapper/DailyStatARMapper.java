package com.cw.mapper;

import java.time.LocalDate;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import com.cw.entity.DailyStat_AR;

public interface DailyStatARMapper extends JpaRepository<DailyStat_AR, Long>, JpaSpecificationExecutor<DailyStat_AR> {
	@Transactional
	@Modifying
	@Query(value = "delete from DailyStat_AR where hotelId=:hotelId and business_date=:date")
	int remove(String hotelId, LocalDate date);
}

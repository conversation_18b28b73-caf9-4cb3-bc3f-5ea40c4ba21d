package com.cw.mapper.common;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import javax.validation.constraints.NotNull;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

@Service
public interface DaoLocal<T> {

    <A> List<A> getObjectList(String jpql, Object... objects);


    /**
     * 获取配置表的
     *
     * @param <A>
     * @param t
     * @param hotelId
     * @return
     */
    <A> List<A> getHotelIdObjectList(Class<A> t, String hotelId);

    <A> List<A> getNativeObjectList(String sql, Object... objects);

    <A> A getObject(String jpql, Object... objects);


    <A> List<A> getObjectListWithLimit(String jpql, int maxSize, Object... objects);

    <A> A find(Class<A> cls, Long id);

    public <A> A findNoCache(Class<A> cls, Long id);

    <A> String getExcuteSql(Class<A> cls, Specification<A> specification);

    <T> T merge(T obj);

    void persist(Object obj);

    int batchOption(String jpql, Object... objects);

    int batchNativeOption(String sql, Object... objects);

    int batchNativeOptionWithLimit(String sql, int maxSize, Object... objects);

    /**
     * 获取查询个数
     *
     * @param jpql
     * @param objects
     * @return
     */
    int getCountOption(String jpql, Object... objects);

    <T> void removeById(T t);

    DataSource getDataSource();


    public Connection getBatchConnection() throws SQLException;

    <A> List<A> getList(String jpql, @NotNull Map<String, Object> params);

    /**
     * 分页查询单表实体
     *
     * @param jpql
     * @param params
     * @param pageable
     * @return
     */
    <A> Page<A> getListPage(String jpql, @NotNull Map<String, Object> params, Pageable pageable);

    <A> A getObject(String jpql, @NotNull Map<String, Object> params);

    int executeUpdate(String jpql, @NotNull Map<String, Object> params);

    int executeNativeUpdate(String jpql, @NotNull Map<String, Object> params);

	<A> A getNativeObject(String jpql, Map<String, Object> params);
	
	<A> List<A> getNativeList(String jpql, Map<String, Object> params);
}

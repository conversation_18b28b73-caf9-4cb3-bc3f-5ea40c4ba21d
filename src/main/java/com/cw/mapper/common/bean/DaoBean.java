package com.cw.mapper.common.bean;

import cn.hutool.core.bean.BeanUtil;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.sql.DataSource;
import javax.transaction.Transactional;
import java.sql.Connection;
import java.sql.SQLException;
import java.text.MessageFormat;
import java.util.List;
import java.util.Map;

@Repository
public class DaoBean<T> implements DaoLocal<T> {

    @PersistenceContext
    private EntityManager manager;  //还要考虑多数据源的情况

    private static final int BATCH_SIZE = 50;


    @Override
    public <A> List<A> getObjectList(String jpql, Object... objects) {
        Query query = manager.createQuery(jpql);
        if (objects != null && objects.length > 0) {
            for (int i = 0; i < objects.length; i++) {
                query.setParameter(i + 1, objects[i]);
            }
        }
        List<A> list = null;
        try {
            list = query.getResultList();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;
    }

    @Override
    public <A> List<A> getObjectListWithLimit(String jpql, int maxSize, Object... objects) {
        Query query = manager.createQuery(jpql);
        if (objects != null && objects.length > 0) {
            for (int i = 0; i < objects.length; i++) {
                query.setParameter(i + 1, objects[i]);
            }
        }
        query.setMaxResults(maxSize);
        List<A> list = null;
        try {
            list = query.getResultList();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;
    }

    @Override
    public <A> List<A> getHotelIdObjectList(Class<A> t, String hotelId) {
        String jpql = "from {0} where hotelid=?1 ";
        jpql = MessageFormat.format(jpql, t.getSimpleName());
        return getObjectList(jpql, hotelId);
    }

    @Override
    public <A> List<A> getNativeObjectList(String sql, Object... objects) {
        List<A> list = null;
        try {
            Query query = manager.createNativeQuery(sql);
            if (objects != null && objects.length > 0) {
                for (int i = 0; i < objects.length; i++) {
                    query.setParameter(i + 1, objects[i]);
                }
            }
            list = query.getResultList();
        } catch (Exception e) {
            System.err.println("错误的SQL: " + sql);
            e.printStackTrace();
        }
        return list;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public <T> T merge(T obj) {
        T t = manager.merge(obj);
        manager.flush();
        manager.clear();
        return t;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public void persist(Object obj) {
        manager.persist(obj);
        manager.flush();
        manager.clear();
    }

    @Override
    public <A> A find(Class<A> cls, Long id) {
        return manager.find(cls, id);
    }

    @Override
    public <A> A findNoCache(Class<A> cls, Long id) {
        manager.clear();
        return manager.find(cls, id);
    }

    @Override
    public <A> String getExcuteSql(Class<A> cls, Specification<A> specification) {
        CriteriaBuilder builder = manager.getCriteriaBuilder();
        CriteriaQuery<A> query = builder.createQuery(cls);
        Root<A> root = query.from(cls);

        // 应用 specification
        Predicate predicate = specification.toPredicate(root, query, builder);
        query.where(predicate);

        // 获取 SQL
        TypedQuery<A> typedQuery = manager.createQuery(query);
        String sql = typedQuery.unwrap(org.hibernate.query.Query.class).getQueryString();
        return sql;
    }

    @Override
    public <A> A getObject(String jpql, Object... objects) {
        Query query = manager.createQuery(jpql);
        query.setMaxResults(1);
        if (objects != null && objects.length > 0) {
            for (int i = 0; i < objects.length; i++) {
                query.setParameter(i + 1, objects[i]);
            }
        }
        List list = query.getResultList();
        if (list == null || list.isEmpty()) {
            return null;
        }
        return (A) list.get(0);
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public int batchOption(String jpql, Object... objects) {
        Query query = manager.createQuery(jpql);
        if (objects != null && objects.length > 0) {
            for (int i = 0; i < objects.length; i++) {
                query.setParameter(i + 1, objects[i]);
            }
        }
        int result = query.executeUpdate();
        manager.flush();
        manager.clear();
        return result;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public int batchNativeOption(String sql, Object... objects) {
        Query query = manager.createNativeQuery(sql);
        if (objects != null && objects.length > 0) {
            for (int i = 0; i < objects.length; i++) {
                query.setParameter(i + 1, objects[i]);
            }
        }
        int result = query.executeUpdate();
        return result;
    }

    @Override
    public int batchNativeOptionWithLimit(String sql, int maxSize, Object... objects) {
        Query query = manager.createNativeQuery(sql);
        if (objects != null && objects.length > 0) {
            for (int i = 0; i < objects.length; i++) {
                query.setParameter(i + 1, objects[i]);
            }
        }
        query.setMaxResults(maxSize);
        int result = query.executeUpdate();
        return result;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public <T> void removeById(T t) {
        Class c = t.getClass();
        Long id = 0L;
        id = (Long) BeanUtil.getFieldValue(t, "id");
        Query q = manager.createQuery(String.format("delete from %s where id= %d", c.getSimpleName(), id));
        q.executeUpdate();
    }

    @Override
    public int getCountOption(String jpql, Object... objects) {
        Query query = manager.createQuery(jpql);
        if (objects != null && objects.length > 0) {
            for (int i = 0; i < objects.length; i++) {
                query.setParameter(i + 1, objects[i]);
            }
        }
        Object count = query.getSingleResult();
        int result = 0;
        if (count != null) {
            result = Integer.parseInt(count.toString());
        }
        return result;
    }

    @Override
    public DataSource getDataSource() {
        DataSource dataSource = SpringUtil.getBean(DataSource.class);
        return dataSource;
    }

    @Override
    public Connection getBatchConnection() throws SQLException {
        return getDataSource().getConnection();
    }

    @Override
    public <A> List<A> getList(String jpql, Map<String, Object> params) {
        Query query = manager.createQuery(jpql);
        params.forEach(query::setParameter);
        return query.getResultList();
    }

    @Override
    public <A> Page<A> getListPage(String jpql, Map<String, Object> params, Pageable pageable) {
        String countJpql = "select count(*) " + jpql.substring(jpql.toLowerCase().indexOf("from"));
        Query countQuery = manager.createQuery(countJpql);
        Query dataQuery = manager.createQuery(jpql);
        params.forEach((key, value) -> {
            countQuery.setParameter(key, value);
            dataQuery.setParameter(key, value);
        });

        int total = ((Number) countQuery.getSingleResult()).intValue();
        dataQuery.setFirstResult((int) pageable.getOffset());
        dataQuery.setMaxResults(pageable.getPageSize());

        List<A> list = dataQuery.getResultList();
        return new PageImpl<>(list, pageable, total);
    }



    @Override
    public <A> A getObject(String jpql, Map<String, Object> params) {
        Query query = manager.createQuery(jpql);
        query.setMaxResults(1);
        params.forEach(query::setParameter);
        List list = query.getResultList();
        if (list == null || list.isEmpty()) {
            return null;
        }
        return (A) list.get(0);
    }

    @Override
    public <A> A getNativeObject(String jpql, Map<String, Object> params) {
        Query query = manager.createNativeQuery(jpql);
        query.setMaxResults(1);
        params.forEach(query::setParameter);
        List list = query.getResultList();
        if (list == null || list.isEmpty()) {
            return null;
        }
        return (A) list.get(0);
    }

    @Override
    public <A> List<A> getNativeList(String jpql, Map<String, Object> params) {
        Query query = manager.createNativeQuery(jpql);
        params.forEach(query::setParameter);
        return query.getResultList();
    }


    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public int executeUpdate(String jpql, Map<String, Object> params) {
        Query query = manager.createQuery(jpql);
        params.forEach(query::setParameter);
        return query.executeUpdate();
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public int executeNativeUpdate(String jpql, Map<String, Object> params) {
        Query query = manager.createNativeQuery(jpql);
        params.forEach(query::setParameter);
        return query.executeUpdate();
    }
}

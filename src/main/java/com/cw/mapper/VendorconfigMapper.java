package com.cw.mapper;

import com.cw.entity.Vendorconfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface VendorconfigMapper extends JpaRepository<Vendorconfig, Long>, JpaSpecificationExecutor<Vendorconfig> {

    /**
     * 统计是否有重复 厂商
     *
     * @param code
     * @return
     */
    @Query(value = "select count(id) from Vendorconfig where vendorconfig.vtype=?1  and  hotelid=?2", nativeQuery = true)
    long countByCodeAndProjectIdNot(String code, String projectId);


    Vendorconfig getVendorconfigById(Long id);



    @Query(value = "select *  from Vendorconfig where hotelid=?1 and vtype=?2 and sqlid<>?3 and active=true limit 1", nativeQuery = true)
    Vendorconfig getOtherSameTypeVendorconfig(String hotelid, String vtype, Integer sqlid);


    List<Vendorconfig> getVendorconfigsByHotelId(String hotelId);

}

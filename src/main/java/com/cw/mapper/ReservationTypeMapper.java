package com.cw.mapper;

import com.cw.entity.ReservationType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;


/**
 * @Description: 预定类型数据表映射
 * @Author: michael.pan
 * @Date: 2024/3/23 18:55
 */
public interface ReservationTypeMapper extends JpaRepository<ReservationType, Long>, JpaSpecificationExecutor<ReservationType> {
    int countByHotelIdAndDescription(String hotelId, String description);
}

package com.cw.mapper;

import com.cw.entity.Market;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;


/**
 * @Description: 市场数据表映射
 * @Author: michael.pan
 * @Date: 2024/3/23 18:55
 */
public interface MarketMapper extends JpaRepository<Market, Long>, JpaSpecificationExecutor<Market> {
    int countByHotelIdAndDescription(String hotelId,String description);
}

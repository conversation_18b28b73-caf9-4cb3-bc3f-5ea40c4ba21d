package com.cw.mapper;

import com.cw.entity.RoomRate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * @Classname RoomRateMapper
 * @Description 房价数据映射
 * @Date 2024-03-18 23:46
 * <AUTHOR> sancho.shen
 */
public interface RoomRateMapper extends JpaRepository<RoomRate, Long>, JpaSpecificationExecutor<RoomRate> {
    int countByHotelIdAndCode(String hotelId,String code);
}

package com.cw.mapper;

import com.cw.entity.AccountItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 账项代码数据表映射
 * @Author: michael.pan
 * @Date: 2024/3/21 23:38
 */
public interface AccountItemMapper extends JpaRepository<AccountItem, Long>, JpaSpecificationExecutor<AccountItem> {
    List<AccountItem> findAllByHotelId(String hotelId);

    AccountItem findByHotelIdAndCode(String hotelId, String code);

    @Query("SELECT min(id) FROM AccountItem  WHERE hotelId =?1 AND description=?2")
	Long findMinIdByDescription(String hotelId, String description);

	/**
	 * 增量方式更新收入
	 *
	 * @param hotelId
	 * @param code
	 * @param amount
	 * @return
	 */
	@Transactional
	@Modifying
	@Query("UPDATE AccountItem set incomeToday=incomeToday+:amt, monthlyTotal=monthlyTotal+:amt, yearlyTotal=yearlyTotal+:amt WHERE hotelId=:hotelId and code=:code")
	int addTodayAmount(@Param("hotelId") String hotelId, @Param("code") String code, @Param("amt") BigDecimal amount);

	/**
	 * 清空月累计
	 *
	 * @param hotelId
	 * @return
	 */
	@Transactional
	@Modifying
	@Query("UPDATE AccountItem set incomeToday=0, monthlyTotal=0 WHERE hotelId=:hotelId")
	int clearMonthTotal(@Param("hotelId") String hotelId);

	/**
	 * 清空年月累计
	 *
	 * @param hotelId
	 * @return
	 */
	@Transactional
	@Modifying
	@Query("UPDATE AccountItem set incomeToday=0, monthlyTotal=0, yearlyTotal=0 WHERE hotelId=:hotelId")
	int clearMonthNYearTotal(@Param("hotelId") String hotelId);
}

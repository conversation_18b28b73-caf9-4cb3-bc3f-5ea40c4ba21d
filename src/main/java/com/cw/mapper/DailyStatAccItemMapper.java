package com.cw.mapper;

import java.time.LocalDate;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import com.cw.entity.DailyStat_AccItem;

public interface DailyStatAccItemMapper extends JpaRepository<DailyStat_AccItem, Long>, JpaSpecificationExecutor<DailyStat_AccItem> {
	@Transactional
	@Modifying
	@Query(value = "delete from DailyStat_AccItem where hotelId=:hotelId and business_date=:date")
	int remove(String hotelId, LocalDate date);
}

package com.cw.mapper;

import com.cw.entity.Optionswitch;
import com.cw.entity.Room;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/14
 **/
public interface OptionsMapper extends JpaRepository<Optionswitch, Long>, JpaSpecificationExecutor<Optionswitch> {
//    int countByHotelIdAndRoomStatus(String hotelId,String roomStatus);
//    int countByHotelIdAndLocc(String hotelId,int locc);
//    int countByHotelIdAndCharacteristic(String hotelId,String characterName);
//    int countByHotelIdAndRoomNo(String hotelId,String roomNo);
//    int countByHotelIdAndRoomType(String hotelId,String roomType);
//    int countAllByHotelId(String hotelId);
//    List<Room> findAllByHotelIdAndRoomStatus(String hotelId, String roomStatus);
//    List<Room> findAllByHotelIdAndLocc(String hotelId,int locc);
//    Room findRoomByHotelIdAndRoomNo(String hotelId,String roomNo);
//    List<Room> findAllByHotelIdAndRoomType(String hotelId,String roomType);

    Optionswitch findAllByHotelIdAndGroupAndOption(String hotelId,String Group,String option);
//    List<Room> findAllByHotelId(String hotelId);

}

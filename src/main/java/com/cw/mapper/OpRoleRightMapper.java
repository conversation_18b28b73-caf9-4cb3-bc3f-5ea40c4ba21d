package com.cw.mapper;

import com.cw.entity.OpRoleRight;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2024-03-21
 */
public interface OpRoleRightMapper extends JpaRepository<OpRoleRight, Long>, JpaSpecificationExecutor<OpRoleRight> {

    @Query(value = "select * from Op_role_right where roleid=?1 and hotelid=?2 ", nativeQuery = true)
    List<OpRoleRight> getAllRightsByRoleIdAndHotelId(String roleId, String hotelId);


    @Transactional
    @Modifying
    @Query(value = "delete from Op_role_right where roleid=?1 and hotelid=?2", nativeQuery = true)
    void deleteByRoleId(String roleId, String hotelId);

    /**
     * 返回角色的所有操作权限
     *
     * @param hotelId
     * @return
     */
    @Query(value = "select * from Op_role_right where   `type` = 'OP' and hotelid=?1 ", nativeQuery = true)
    List<OpRoleRight> getHotelIdRoleOpRights(String hotelId);

    //@Query(value = "select * from Op_role_right where roleid=?1 and hotelid=?2 ", nativeQuery = true)
    //List<OpRoleRight> getAllRightsByRoleidAndHotelid(String roleId, String hotelId);



    //List<OpRoleRight> getAllByHotelId(String hotelId);


}

package com.cw.mapper;


import com.cw.entity.NaDailyLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Created on 2020/12/31.
 */
public interface NaDailyLogMapper extends JpaRepository<NaDailyLog, Long>, JpaSpecificationExecutor<NaDailyLog> {

    /**
     * @param hotelId
     * @param date
     * @return 按照倒序返回步骤为1的夜审步骤记录集合
     */
    @Query(value = "select * from Nadailylog where hotelid=?1 and nadate=?2 and  progressid='1' order by id desc", nativeQuery = true)
    List<NaDailyLog> getNaDailyLog(String hotelId, Date date);


    /**
     * @param hotelId
     * @param date
     * @return 返回夜审日期 按步骤顺序排序，最新记录排序
     */
    @Query(value = "select * from Nadailylog where hotelid=?1 and nadate=?2    order by progressid, id desc", nativeQuery = true)
    List<NaDailyLog> getMaxNaDailyLog(String hotelId, Date date);

    /**
     * 返回最新的夜审步骤记录集合,id值大于等于最新第一步骤id
     * 避免重复 取最新数据
     *
     * @param hotelId 酒店id
     * @param naDate  夜审日期
     * @param id      第一步骤id
     * @return
     */
    @Query(value = "select N.* from Nadailylog N JOIN (select MAX(id) as id,progressid from Nadailylog where hotelid=?1 and nadate=?2 and " +
            "  id>=?3 GROUP BY progressid order by id asc) N2 On N.progressid=N2.progressid and N.id=N2.id", nativeQuery = true)
    List<NaDailyLog> getLastNaDailyLogs(String hotelId, Date naDate, Long id);

    /**
     * 获取最后一次的夜审日期
     *
     * @param hotelId
     * @param progressId
     * @param status
     * @return
     */
    @Query(value = "select MAX(naDate) from NaDailyLog where hotelId=?1 and progressId=?2 and status=?3")
    Date getLastNaDate(String hotelId, String progressId, String status);


    @Query(value = "select MAX(nadate) from Nadailylog where hotelid=?1 ", nativeQuery = true)
    Date getMaxNaDailyLogNaDate(String hotelId);
}

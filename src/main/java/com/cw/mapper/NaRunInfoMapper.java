package com.cw.mapper;


import com.cw.entity.NaRunInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

public interface NaRunInfoMapper extends JpaRepository<NaRunInfo, Long>, JpaSpecificationExecutor<NaRunInfo> {


    @Override
    List<NaRunInfo> findAll();

    /**
     * 通过项目ID获取
     *
     * @param hotelId
     * @return
     */
    NaRunInfo findTopByHotelIdOrderByIdDesc(String hotelId);

}

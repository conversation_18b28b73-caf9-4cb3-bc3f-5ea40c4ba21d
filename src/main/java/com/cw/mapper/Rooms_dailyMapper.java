package com.cw.mapper;

import com.cw.entity.RoomsDaily;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-07
 */
public interface Rooms_dailyMapper extends JpaRepository<RoomsDaily, Long>, JpaSpecificationExecutor<RoomsDaily> {

    @Query(value = "select * from Rooms_daily where reservation_number=?1  and hotelid=?2  order by datum asc", nativeQuery = true)
    public List<RoomsDaily> findRooms_dailiesByRegno(String regno, String hotelId);

    /**
     * 获取对应预定，截止至business_date（含）的需要未入房费详情
     *
     * @param hotelId
     * @param res_no
     * @param business_date
     * @return
     */
    @Query(value = "from RoomsDaily where hotelId=?1 and reservationNumber=?2 and datum<=?3 and posted_time is null")
    List<RoomsDaily> getNeedToPostRoomRateDailies(String hotelId, String res_no, Date business_date);

    /**
     * 获取对应预定，所有需要未入房费详情
     *
     * @param hotelId
     * @param res_no
     * @return
     */
    @Query(value = "from RoomsDaily where hotelId=?1 and reservationNumber=?2 and posted_time is null")
    List<RoomsDaily> getNeedToPostRoomRateDailies(String hotelId, String res_no);


    @Query(value = "from RoomsDaily where   hotelId=?1 and  datum=?2 and reservationNumber=?3 and rateCode=?4 ")
    RoomsDaily findRoomsDailyByDatum(String hotelId, Date datum, String reservation_number,String rateCode);

    @Transactional
    @Modifying
    @Query(value = "delete from Rooms_daily where reservation_number = ?1 and (datum < ?2 or datum >=?3)  and hotelid=?4", nativeQuery = true)
    void deleteAllByAdjustAfter(String regno, Date start, Date end, String hotelId);

    @Transactional
    @Modifying
    @Query(value = "delete from Rooms_daily where reservation_number = ?1 and datum >=?2 and hotelid=?3", nativeQuery = true)
    void deleteAllByAdjustBefore(String regno, Date end, String hotelId);

//    @Transactional
//    @Modifying
//    @Query(value = "update RoomsDaily set posted_time=CURRENT_TIMESTAMP where hotelId=?1 and reservationNumber=?2 and datum=?3")
//    void updatePostedDate(String hotelId, String res_no, Date date);

}

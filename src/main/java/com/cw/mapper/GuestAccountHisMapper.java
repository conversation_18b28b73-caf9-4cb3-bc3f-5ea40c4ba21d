package com.cw.mapper;

import com.cw.entity.GuestAccountsHis;
import com.cw.pojo.dto.pms.res.statistic.DepartmentStat;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDate;
import java.util.List;

/**
 * @Description: 客人账目数据表映射
 * @Author: michael.pan
 * @Date: 2024/3/27 21:55
 */
public interface GuestAccountHisMapper extends JpaRepository<GuestAccountsHis, Long>, JpaSpecificationExecutor<GuestAccountsHis> {

    /**
     * 查询营业日期内的账项
     *  先直接用amount 后面改成quantity*price
     * @param hotelId   酒店ID
     * @param businessDate 开始时间
     * @return
     */
    @Query(value = "select new com.cw.pojo.dto.pms.res.statistic.DepartmentStat(g.business_date,g.departmentCode,sum(g.amount)) from GuestAccountsHis g " +
            "where g.hotelId=?1 and g.business_date =?2   GROUP BY g.departmentCode,g.business_date  ORDER BY g.business_date")
    List<DepartmentStat> statisticGuestAccountByBusinessDate(String hotelId, LocalDate businessDate);


	@Query(value = "select new com.cw.pojo.dto.pms.res.statistic.DepartmentStat(g.business_date,g.departmentCode,sum(g.amount)) from GuestAccountsHis g " +
			"where g.hotelId=?1 and g.business_date between ?2 and ?3   GROUP BY g.departmentCode,g.business_date  ORDER BY g.business_date")
	List<DepartmentStat> statGAByBusinessDate(String hotelId, LocalDate starDate, LocalDate endDate);




	





}

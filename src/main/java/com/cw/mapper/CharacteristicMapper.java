package com.cw.mapper;

import com.cw.entity.Characteristic;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * @Description: 特性设置数据映射
 * @Author: michael.pan
 * @Date: 2024/3/28 22:33
 */
public interface CharacteristicMapper extends JpaRepository<Characteristic, Long>, JpaSpecificationExecutor<Characteristic> {
    int countByHotelIdAndCharacterName(String hotelId, String characterName);
}

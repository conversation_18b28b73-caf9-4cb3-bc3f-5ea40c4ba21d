package com.cw.mapper;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import com.cw.entity.DailyStat_Res;

public interface DailyStatResMapper extends JpaRepository<DailyStat_Res, Long>, JpaSpecificationExecutor<DailyStat_Res> {
	@Transactional
	@Modifying
	@Query(value = "delete from DailyStat_Res where hotelId=:hotelId and business_date=:date")
	int remove(String hotelId, LocalDate date);

	@Query(value = "select COALESCE(sum(revenues->'$.total.debit'), 0), COALESCE(sum(revenues->'$.total.credit'), 0) from DailyStat_Res where hotelId=:hotelId and business_date=:date", nativeQuery = true)
	Object[] sumDateRevenue(String hotelId, LocalDate date);

	@Query(value = "select sum(revenues->'$.total.debit'+revenues->'$.total.credit') from DailyStat_Res where hotelId=:hotelId and business_date=:date", nativeQuery = true)
	BigDecimal sumDateAmount(String hotelId, LocalDate date);

	@Query(value = "select sum(balances->'$.total.debit'+balances->'$.total.credit') from DailyStat_Res where hotelId=:hotelId and business_date=:date", nativeQuery = true)
	BigDecimal sumBalance(String hotelId, LocalDate date);
}

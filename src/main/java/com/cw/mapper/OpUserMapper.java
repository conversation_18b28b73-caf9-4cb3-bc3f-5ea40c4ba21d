package com.cw.mapper;

import com.cw.entity.OpUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/3/15 09:15
 **/
public interface OpUserMapper extends JpaRepository<OpUser, Long>, JpaSpecificationExecutor<OpUser> {

    OpUser findByHotelIdAndUserid(String hotelId, String userid);

    @Query(value = "select count(id) from Op_user where userid=?1 and id<>?2 and hotelid=?3", nativeQuery = true)
    long countByUseridAndIdNot(String userid, Long id, String hotelId);

    @Query(value = "select * from Op_user where userid =?1 and hotelid=?2 limit 1 ", nativeQuery = true)
    OpUser findOp_userByUserid(String userid, String hotelId);

    @Transactional
    @Modifying
    @Query(value = "update Op_user set roleid=?1 where roleid=?2 and hotelid=?3", nativeQuery = true)
    void updateOp_userByRoleId(String newRoleId, String oldRoleId, String hotelId);

    @Query(value = "select count(id) from Op_user where roleid=?1 and hotelid=?2", nativeQuery = true)
    long countByRoleId(String roleId, String hotelId);
}

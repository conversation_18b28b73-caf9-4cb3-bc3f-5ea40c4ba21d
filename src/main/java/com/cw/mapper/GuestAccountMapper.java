package com.cw.mapper;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import com.cw.entity.GuestAccounts;
import com.cw.pojo.dto.pms.res.statistic.DepartmentStat;
import com.cw.utils.enums.AccountItemEnum;

/**
 * @Description: 客人账目数据表映射
 * @Author: michael.pan
 * @Date: 2024/3/27 21:55
 */
public interface GuestAccountMapper extends JpaRepository<GuestAccounts, Long>, JpaSpecificationExecutor<GuestAccounts> {

	/**
	 * 查询营业日期内的账项 先直接用amount 后面改成quantity*price
	 * 
	 * @param hotelId      酒店ID
	 * @param businessDate 开始时间
	 * @return
	 */
	@Query(value = "select new com.cw.pojo.dto.pms.res.statistic.DepartmentStat(g.business_date,g.departmentCode,sum(g.amount)) from GuestAccounts g "
			+ "where g.hotelId=?1 and g.business_date =?2   GROUP BY g.departmentCode,g.business_date  ORDER BY g.business_date")
	List<DepartmentStat> statisticGuestAccountByBusinessDate(String hotelId, LocalDate businessDate);

	@Query(value = "select new com.cw.pojo.dto.pms.res.statistic.DepartmentStat(g.business_date,g.departmentCode,sum(g.amount)) from GuestAccounts g "
			+ "where g.hotelId=?1 and g.business_date between ?2 and ?3   GROUP BY g.departmentCode,g.business_date  ORDER BY g.business_date")
	List<DepartmentStat> statGAByBusinessDate(String hotelId, LocalDate starDate, LocalDate endDate);

	/**
	 *
	 * @param hotelId
	 * @param reservationNumber
	 * @param businessDate
	 * @return 返回订单的房费收入
	 */
	@Query(value = "select sum(price*quantity) from GuestAccounts where departmentCode=?1 and hotelId=?2 and reservationNumber=?3 and business_date=?4")
	BigDecimal countRsRoomConsume(String roomDeptCode, String hotelId, String reservationNumber, LocalDate businessDate);

	/**
	 * 统计营业时间内的销售金额，计算方式：单价*数量
	 *
	 * @param salesCode    小于8000为消费；8000-8999属于代付，大于9000为付款；等于9000现金
	 * @param hotelId      酒店id
	 * @param businessDate 营业时间
	 * @return 营业时间内的销售金额
	 */
	@Query(value = "select sum(price*quantity) from GuestAccounts where departmentCode <?1 and hotelId=?2 and business_date=?3")
	BigDecimal countTodaySales(String salesCode, String hotelId, LocalDate businessDate);

	@Query(value = "from GuestAccounts where  hotelId=?1 and reservationNumber=?2 and departmentCode=?3 and remark=?4 and incomeUser=?5 ")
	List<GuestAccounts> queryByDepartmentCodeAndResNo(String hotelId, String reservationNumber, String departmentCode, String remark, String incomeUser);

	@Query(value = "select count(1) from GuestAccounts where  hotelId=:hotelId and reservationNumber=:resNo and departmentCode=:deptcode and paymethod=:pmth and tradeno=:tradeno")
	int counts(String hotelId, String resNo, String deptcode, Integer pmth, String tradeno);

	/**
	 * 获取关联的账目
	 *
	 * @param hotelId
	 * @param acc_id
	 * @return
	 */
	@Query(value = "from GuestAccounts where hotelId=:hotelId and (master_id in (:master_ids) or accountsId in (:master_ids))")
	List<GuestAccounts> getRelationAccounts(@Param("hotelId") String hotelId, @Param("master_ids") Collection<String> master_ids);


	/**
	 * 获取账目
	 *
	 * @param hotelId
	 * @param ids
	 * @return
	 */
	@Query(value = "from GuestAccounts where hotelId=:hotelId and  accountsId in (:ids)")
	List<GuestAccounts> getAccounts(@Param("hotelId") String hotelId, @Param("ids") Collection<String> ids);

	/**
	 * 根据帐ID定位涉及的预定号
	 * 
	 * @param hotelId
	 * @param acc_ids
	 * @return
	 */
	@Query(value = "select reservationNumber from GuestAccounts where hotelId=:hotelId and accountsId in (:acc_ids) group by reservationNumber")
	List<String> getResNosByAccIds(@Param("hotelId") String hotelId, @Param("acc_ids") Collection<String> acc_ids);

	/**
	 * 获取账目
	 *
	 * @param hotelId
	 * @param acc_id
	 * @return
	 */
	@Query(value = "from GuestAccounts where hotelId=:hotelId and accountsId=:acc_id")
	GuestAccounts getGuestAccounts(@Param("hotelId") String hotelId, @Param("acc_id") String acc_id);

	/**
	 * 获取账目及其绑定的总金额
	 *
	 * @param hotelId
	 * @param acc_id
	 * @return
	 */
	@Query(value = "select sum(price*quantity+credit) from GuestAccounts where hotelId=:hotelId and master_id=:master_id")
	BigDecimal getAccBalance(String hotelId, String master_id);

	/**
	 * 获取应收核销付款总额（排除折扣）
	 *
	 * @param hotelId
	 * @param acc_id
	 * @return
	 */
	@Query(value = "select sum(credit) from GuestAccounts where hotelId=:hotelId and ar_payfor_id in (:master_id)")
	BigDecimal getArPayforCreditAmount(String hotelId, String master_id);

	/**
	 * 获取应收账目及其绑定的总金额（ 主要用于检验是否已被核销，因为目前挂账没有部分核销功能，因此只需检测是否为0即可）
	 *
	 * @param hotelId
	 * @param acc_id
	 * @return
	 */
	@Query(value = "select sum(price*quantity+credit) from GuestAccounts where hotelId=:hotelId and (master_id=:master_id or ar_payfor_id=:master_id)")
	BigDecimal getArAccBalance(String hotelId, String master_id);

	/**
	 * 获取应收账户可付款ID
	 *
	 * @param hotelId
	 * @param acc_id
	 * @return
	 */
	@Query(value = "SELECT DISTINCT accountsId from GuestAccounts where hotelId=:hotelId and ar_no=:ar_no"//
			+ " and (ar_payfor_id is null or ar_payfor_id='')"// 未为他人支付
			+ " and (price*quantity+credit)<0"// 负数记录
			+ " and amount_write_off=0"// 未被他人支付
			+ " order by business_date, price*quantity+credit desc")
	List<String> getArPayAbleIDs(String hotelId, String ar_no);

	/**
	 * 获取应收账户可付款余额
	 *
	 * @param hotelId
	 * @param acc_id
	 * @return
	 */
	@Query(value = "SELECT SUM(price*quantity+credit) from GuestAccounts where hotelId=:hotelId and ar_no=:ar_no and (ar_payfor_id='' or ar_payfor_id is null) and (price*quantity+credit)<0")
	BigDecimal getArPayAbleBalance(@Param("hotelId") String hotelId, @Param("ar_no") String ar_no);

	/**
	 * 获取预定帐是否由对应应收帐
	 *
	 * @param hotelId
	 * @param acc_id
	 * @return
	 */
	@Query(value = "from GuestAccounts where hotelId=:hotelId and split_from_id=:master_id and departmentCode=:deptcode and ar_no<>''")
	List<GuestAccounts> getArDebitDetails(String hotelId, String master_id, String deptcode);

//	/**
//	 * 应收核销
//	 * 
//	 * @param hotelId
//	 * @param write_off_id
//	 * @param pament_id
//	 */
//	@Transactional
//	@Modifying
//	@Query(value = "update GuestAccounts set ar_payfor_id=:write_off_id, modified_time=now() where hotelId=:hotelId and master_id=:pament_id")
//	void arWriteOff(String hotelId, String write_off_id, String pament_id);

	/**
	 * 应收核销付款明细
	 * 
	 * @param hotelId
	 * @param acc_id
	 */
	@Query(value = "select ar_no, ar_payfor_id, sum(price*quantity+credit) from GuestAccounts"//
			+ " where hotelId=:hotelId and ar_no<>'' and ar_payfor_id<>'' and date(create_time)=:date"//
			+ " group by ar_no, ar_payfor_id")
	List<Object[]> getArPayforAmounts(String hotelId, Date date);

	@Query(value = "select accountsId from GuestAccounts where hotelId=:hotelId and ar_no<>'' and accountsId in (:ids) and res_no_org<>''")
	List<String> getFromResIDs(String hotelId, Collection<String> ids);

	/**
	 * 应收核销付款明细
	 * 
	 * @param hotelId
	 * @param acc_id
	 */
	@Query(value = "from GuestAccounts where hotelId=:hotelId and ar_payfor_id=:acc_id")
	List<GuestAccounts> getArWriteOffDetails(String hotelId, String acc_id);
	
	/**
	 * 来自预定未结账总额
	 * 
	 * @param hotelId
	 * @param acc_id
	 */
	@Query(value = "select COALESCE(sum(price*quantity+amount_write_off), 0) from GuestAccounts where hotelId=:hotelId and ar_no=:ar_no and res_no_org<>'' and business_date>=:date_from and business_date<=:date_to")
	BigDecimal getArOpenBalance(String hotelId, String ar_no, LocalDate date_from, LocalDate date_to);

	/**
	 * 来自预定未结账总额
	 * 
	 * @param hotelId
	 * @param acc_id
	 */
	@Query(value = "select COALESCE(sum(price*quantity+amount_write_off), 0) from GuestAccounts where hotelId=:hotelId and ar_no=:ar_no and res_no_org<>'' and business_date<=:date_to")
	BigDecimal getArOpenBalance(String hotelId, String ar_no, LocalDate date_to);

	@Query(value = "select COALESCE(sum(price*quantity+credit), 0) from GuestAccounts where hotelId=:hotelId and reservationNumber<>'' and business_date<=:date")
	BigDecimal sumResBalance(String hotelId, LocalDate date);

	@Query(value = "select COALESCE(sum(price*quantity+credit), 0) from GuestAccounts where hotelId=:hotelId and reservationNumber<>'' and business_date=:date")
	BigDecimal sumResDateAmount(String hotelId, LocalDate date);

	@Query(value = "select  accountsId from GuestAccounts where hotelId=:hotelId and reservationNumber in (:rsNos)")
	List<String> getRsGuestAccounts(String hotelId, List<String> rsNos);
}

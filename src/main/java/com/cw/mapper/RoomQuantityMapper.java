package com.cw.mapper;

import com.cw.entity.Rrooms;
import com.cw.pojo.sqlresult.ProductMaxDatePo;
import com.cw.pojo.sqlresult.RroomsAvlPo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.jpa.repository.Query;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description: 可卖房量数据映射
 * @Author: michael.pan
 * @Date: 2024/5/25 18:01
 */
public interface RoomQuantityMapper extends JpaRepository<Rrooms, Long>, JpaSpecificationExecutor<Rrooms> {

    List<Rrooms> findAllByHotelIdAndDatum(String hotelId,Date datum);

    Rrooms findByHotelIdAndRoomType(String hotelId, String roomType);

    Rrooms findByHotelIdAndRoomTypeAndDatum(String hotelId, String roomType, Date datum);

    @Query(value = "select new com.cw.pojo.sqlresult.ProductMaxDatePo(roomType,MAX(datum)) from Rrooms" +
            "  where hotelId=?1 group by roomType order by MAX(datum)")
    List<ProductMaxDatePo> getRroomsMaxDate(String hotelId);

    @Query(value = "select new com.cw.pojo.sqlresult.RroomsAvlPo(roomType,datum,total-ooo,pickup,total-ooo-pickup,ooo) from Rrooms" +
            "  where hotelId=?1  and datum between  ?2 and ?3 order by  roomType asc, datum asc")
    List<RroomsAvlPo> queryRroomsSchedule(String hotelId, Date startDate, Date endDate);

    @Query(value = "select new com.cw.pojo.sqlresult.RroomsAvlPo(roomType,datum,total-ooo,pickup,total-ooo-pickup,ooo) from Rrooms" +
            "  where hotelId=?1  and datum between  ?2 and ?3 and roomType in (?4) order by  roomType asc, datum asc")
    List<RroomsAvlPo> queryRroomsScheduleList(String hotelId, Date startDate, Date endDate, List<String> roomTypes);


    @Query(value = "select MAX(datum) from Rrooms  where hotelId=?1", nativeQuery = true)
    Date findRroomsMaxDate(String hotelId);


    @Transactional
    @Modifying
    @Query(value = "update Rrooms set pickup=pickup+?1 where hotelId=?2 and roomType=?3 and (datum between ?4 and   ?5)")
    void updateRroomsPickup(Integer pickup, String hotelId, String roomtype, Date start, Date end);


    @Transactional
    @Modifying
    @Query(value = "update Rrooms set ooo=ooo+?1 where hotelId=?2 and roomType = ?3 and (datum between ?4 and ?5)")
    void updateRroomsOoo(Integer ooo, String hotelId, String roomtype, Date start, Date end);

    @Transactional
    @Modifying
    @Query("update Rrooms set total = ?1 where hotelId = ?2 and roomType = ?3")
    void updateRroomsTotal(Integer total, String hotelId, String roomType);


    @Transactional
    @Modifying
    @Query("delete  from Rrooms where hotelId = ?1 and roomType = ?2")
    void deleteByRoomTypeAndHotelId();


    @Query(value = "select new map(roomType as sku,min(total-ooo-pickup) as avl) from Rrooms " +
            "where roomType in (?1) and datum between ?2 and ?3 and hotelId=?5 group by roomType having min(total-ooo-pickup)>=?4")
    public List<Map<String, Object>> getResourceLeft(List<String> roomtypes, Date start, Date end, Integer requireNum, String hotelId);
}

package com.cw.mapper;

import com.cw.entity.Kititem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface KitItemMapper extends JpaRepository<Kititem, Long>, JpaSpecificationExecutor<Kititem> {

    @Modifying
    @Query("delete from Kititem where kitcode = :kitcode and ptype=:ptype")
    void deleteByKitCode(@Param("kitcode") String kitCode, @Param("ptype") String ptype);

    @Query("from Kititem where kitcode = :kitcode")
    List<Kititem> findByKitCode(@Param("kitcode") String kitCode);
}
package com.cw.mapper;

import com.cw.entity.Kitblacklist;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface KitBlackoutMapper extends JpaRepository<Kitblacklist, Long>, JpaSpecificationExecutor<Kitblacklist> {

    @Modifying
    @Query("delete from Kitblacklist where kitcode = :kitcode")
    void deleteByKitCode(@Param("kitcode") String kitcode);

    @Query("from Kitblacklist where kitcode = :kitcode")
    List<Kitblacklist> findByKitCode(@Param("kitcode") String kitCode);

} 
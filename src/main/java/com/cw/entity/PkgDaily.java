package com.cw.entity;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单每日价格 存储为订单历史价格
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/6/11 16:55
 **/
@Entity
@Table(
        name = "Pkg_daily"
)
@Data
public class PkgDaily {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(length = 10, nullable = false, name = "[id]")
    private Long id = 0L;

    /**
     * 民宿代码
     */
    @Column(name = "hotelid", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '酒店ID' ")
    private String hotelId;


    @Column(name = "reservation_number", unique = true, length = 30, nullable = false, columnDefinition = " varchar(30)  default '' comment '预定号' ")
    private String reservationNumber;


    /**
     * 账项代码
     */
    @Column(name = "department_code", columnDefinition = " varchar(255)  default '' comment '账项代码' ")
    private String departmentCode;

    /**
     * 日期
     */
    @Column(name = "datum", nullable = false, columnDefinition = "datetime comment '日期' ")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date datum;

    /**
     * 使用日期
     */
    @Column(name = "usedate", nullable = false, columnDefinition = "datetime comment '使用日期' ")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date usedate;

    /**
     * 价格、保留两位小数
     */
    @Column(name = "price", nullable = false, columnDefinition = " decimal(10, 2) default 0  comment '房价、保留2位小数' ")
    private BigDecimal price = BigDecimal.ZERO;


    @Column(name = "quantity", columnDefinition = " int(5)  comment '数量' ")
    private Integer quantity = 0;

    /**
     * 包价费用代码
     */
    @Column(name = "pkgcode", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '包价费用代码' ")
    private String pkgcode;


    /**
     * 已入账时间
     */
    @Column(name = "posted_time", columnDefinition = "datetime comment '已入账时间' ")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date posted_time;


    @Column(name = "nextday", nullable = false, columnDefinition = " int(1) comment '是否包含下一天:默认false 当天出费用' ")
    private boolean nextday = false;


    @Column(name = "printsep", nullable = false, columnDefinition = " int(1) comment '是否与房费分开打印:默认false不分开' ")
    private boolean printsep = false;

}

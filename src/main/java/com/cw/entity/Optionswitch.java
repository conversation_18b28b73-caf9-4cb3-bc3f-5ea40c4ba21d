package com.cw.entity;


import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@SuppressWarnings("serial")
@Entity
@Data
@Table(name = "Optionswitch")
public class Optionswitch {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "Op_role")
    @Column(length = 10, nullable = false, name = "[id]")
    private Long id = 0L;

    @Column(length = 20, nullable = true, name = "[hotelid]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '项目id' ")
    private String hotelId = "";

    @Column(length = 60, nullable = true, name = "[group]", columnDefinition = " varchar(60)  DEFAULT '' COMMENT '开关组' ")
    private String group = "";/* 开关组 */

    @Column(length = 60, nullable = true, name = "[option]", columnDefinition = " varchar(60)  DEFAULT '' COMMENT '选项代码' ")
    private String option = "";/* 选项代码 */

    @Column(length = 60, nullable = true, name = "[desc]", columnDefinition = " varchar(60)  DEFAULT '' COMMENT '选项描述(中文)' ")
    private String desc = "";/* 选项描述(中文) */

    @Column(name = "[switchstatus]", columnDefinition = " bit(1)  DEFAULT b'0' COMMENT '开关状态'")
    private Boolean switchstatus = false;/* 开关状态 */

    @Column(length = 60, name = "[val]", columnDefinition = " varchar(60)  default '' comment '参数值' ")
    private String val = "";

    @Column(name = "sortIndex", columnDefinition = " int(5) comment '同组序号' ")
    private int sortIndex = 0;
}

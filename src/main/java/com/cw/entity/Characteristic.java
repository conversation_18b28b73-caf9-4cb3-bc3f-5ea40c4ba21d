package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * @Description: 特性设置
 * @Author: michael.pan
 * @Date: 2024/3/28 22:13
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "characteristic")
public class Characteristic extends BaseEntity implements Serializable {
    @Column(name = "hotelid", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '酒店ID' ")
    private String hotelId;

    @Column(name = "character_code", nullable = false, columnDefinition = " varchar(12)  default '' comment '特性代码' ")
    private String characterCode;

    @Column(name = "character_name", nullable = false, columnDefinition = " varchar(24)  default '' comment '特性名称' ")
    private String characterName;
}

package com.cw.entity;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "dailystat_accitem")
@Data
public class DailyStat_AccItem extends NaStatBaseEntity {

	/**
	 * 代码
	 */
	@Column(name = "code", length = 64, nullable = false, columnDefinition = " varchar(64)  default '' comment '代码' ")
	private String code;

	/**
	 * 发生金额
	 */
	@Column(name = "amount", columnDefinition = " decimal(10, 2)  comment '发生金额' ")
	private BigDecimal amount;

	/**
	 * 收入组（001客房，002餐厅，003商场，004宴会，005其他）
	 */
	@Column(name = "income_group", columnDefinition = " varchar(4)  default '' comment '收入组（001客房，002餐厅，003商场，004宴会，005其他）' ")
	private String incomeGroup;

}

package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.util.Date;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2024-03-25
 */
@EqualsAndHashCode(callSuper = false)
@Data
@Entity
@Table(name = "naruninfo")
public class NaRunInfo {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(length = 10, nullable = false, name = "[id]")
    private Long id = 0L;

    @Column(length = 20, nullable = true, name = "[hotelid]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '项目id' ")
    private String hotelId = "";

    @Column(length = 10, nullable = true, name = "[running]", columnDefinition = " varchar(10)  DEFAULT '' COMMENT '运行状态:0等待,1运行中' ")
    private String runNing = "";

    @Column(length = 3, nullable = true, name = "[runprogress]", columnDefinition = " varchar(3)  DEFAULT '' COMMENT '当前运行步奏ID' ")
    private String runProgress = "";

    @Column(length = 20, nullable = true, name = "[laststart]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '上次开始时间' ")
    private String lastStart = "";

    @Column(length = 20, nullable = true, name = "[lastend]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '上次结束时间' ")
    private String lastEnd = "";

    @Column(length = 20, nullable = true, name = "[start]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '本次任务开始时间' ")
    private String start = "";

    @Column(length = 3, nullable = true, name = "[nextprogress]", columnDefinition = " varchar(3)  DEFAULT '' COMMENT '下一个步奏ID' ")
    private String nextProgress = "";

    @Column(nullable = true, name = "[nadate]", columnDefinition = " datetime  DEFAULT '1900-01-01 00:00:00' COMMENT '夜审任务统计处理的日期,一般是当前日期-1.比如现在是10号.这个字段是9号.做9号的统计' ")
    private Date naDate;

    @Column(nullable = true, name = "[rundate]", columnDefinition = " datetime  DEFAULT '1900-01-01 00:00:00' COMMENT '夜审任务执行日期' ")
    private Date runDate;

}

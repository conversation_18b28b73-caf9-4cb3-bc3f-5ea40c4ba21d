package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;

/**
 * 楼层
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/12/24 14:25
 **/
@EqualsAndHashCode(callSuper = false)
@Data
@Entity
@Table(name = "floor")
public class Floor {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(length = 10, nullable = false, name = "[id]")
    private Long id = 0L;


    @Column(length = 20, nullable = true, name = "[hotelid]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '酒店Id' ")
    private String hotelId = "";


    @Column(name = "code", unique = true, length = 20, nullable = false, columnDefinition = " varchar(10)  default '' comment '楼层代码' ")
    private String code;

    @Column(name = "floor", nullable = false, columnDefinition = " int(3)  comment '层数' ")
    private int floor = 0;


    @Column(length = 30, nullable = true, name = "[description]", columnDefinition = " varchar(30)  DEFAULT '' COMMENT '描述' ")
    private String description = "";

    @Column(nullable = false, name = "[seq]", columnDefinition = " int(5)  COMMENT '楼层序号' ")
    private Integer seq = 0;
}

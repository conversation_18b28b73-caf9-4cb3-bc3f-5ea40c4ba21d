package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * @Description: 渠道实体
 * @Author: michael.pan
 * @Date: 2024/3/23 12:54
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "channel")
public class Channel extends BaseEntity implements Serializable {
    @Column(name = "hotelid", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '酒店ID' ")
    private String hotelId;

    @Column(name = "code", nullable = false, columnDefinition = " varchar(24)  default '' comment '代码' ")
    private String code;

    @Column(name = "description", columnDefinition = " varchar(24)  default '' comment '描述' ")
    private String description;


    @Column(name = "[lsys]", columnDefinition = " bit(1)  DEFAULT b'0' COMMENT '是否为系统内置保留记录'")
    private Boolean lsys;
}

package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Classname IncludePriceEntity
 * @Description 包价表实体
 * @Date 2024-03-17 12:02
 * <AUTHOR> sancho.shen
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "include_rate")
public class IncludeRate extends BaseEntity implements Serializable {

    /**
     * 包价代码
     */
    @Column(name = "code", unique = true, length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '包价代码' ")
    private String code;

    /**
     * 酒店id
     */
    @Column(name = "hotelid", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '酒店ID' ")
    private String hotelId;

    /**
     * 描述
     */
    @Column(name = "description", columnDefinition = " varchar(255)  default '' comment '描述' ")
    private String description;

    /**
     * 价格、保留两位小数
     */
    @Column(name = "price", columnDefinition = " decimal(10, 2)  comment '价格、保留2位小数' ")
    private BigDecimal price;

    /**
     * 账项代码
     */
    @Column(name = "department_code", columnDefinition = " varchar(255)  default '' comment '账项代码' ")
    private String departmentCode;

    /**
     * 是否内含0否,1是
     */
    @Column(name = "contains", length = 1, columnDefinition = " int(1) comment '是否内含:0否,1是' ")
    private boolean contains;

    ///**
    // * 配额、保留两位小数
    // */
    //@Column(name = "quota", columnDefinition = " decimal(10, 2)  comment '配额、保留2位小数' ")
    //private BigDecimal quota;

    /**
     * 频率
     */
    @Column(name = "frequency", nullable = false, columnDefinition = " int(1) comment '频率:0一次性,1入住每日都抛' ")
    private Integer frequency = 0;


    @Column(name = "nextday", nullable = false, columnDefinition = " int(1) comment '是否输出为第二天费用:默认false 当天出费用' ")
    private boolean nextday = false;


    @Column(name = "printsep", nullable = false, columnDefinition = " int(1) comment '是否与房费分开打印:默认false不分开' ")
    private boolean printsep = false;

}

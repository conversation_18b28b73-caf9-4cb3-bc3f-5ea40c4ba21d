package com.cw.entity;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/5/14
 **/
@Data
@Table(name = "shift")
@Entity
public class Shift implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "hotelid", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '酒店ID' ")
    private String hotelId;

    @Column(name = "shift_id", nullable = false, columnDefinition = " varchar(20)  default '' comment '班次id'")
    private String shiftId;

    @Column(name = "shift_name", length = 20, nullable = false, columnDefinition = " varchar(20)  default '' comment '班次名称'")
    private String shiftName;

    @Column(name = "time_range",  length = 20, nullable = false, columnDefinition = " varchar(20)  default '' comment '时段范围' ")
    private String timeRange;
}

package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 应收账户表
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/10/23 10:18
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "ar_account")
@Entity
public class ArAccount extends BaseEntity implements Serializable {
    @Column(name = "hotelid", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '酒店ID' ")
    private String hotelId = "";

    @Column(name = "code", length = 48, nullable = false, columnDefinition = " varchar(48)  default '' comment '账户代码' ")
    private String code;

    @Column(name = "description", columnDefinition = " varchar(96)  default '' comment '描述(账户名称)' ")
    private String description;

    @Column(name = "channel", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '关联渠道代码' ")
    private String channel;

    @Column(name = "contactor", columnDefinition = " varchar(24)  default '' comment '描述(联系人)' ")
    private String contactor;

    @Column(name = "phone", columnDefinition = " varchar(24)  default '' comment '描述(联系电话)' ")
    private String phone;

    @Column(name = "creditlimit", columnDefinition = " decimal(10, 2)  comment '挂账额度 -1表示无限额' ")
    private BigDecimal creditlimit;

    @Column(name = "settlecycle", columnDefinition = " int(1) default 0 comment '0>不限制,1>月结'")
    private Integer settlecycle = 1;

    @Column(name = "settleday", columnDefinition = " int(2)  comment '结算日' ")
    private Integer settleday;

    @Column(name = "[active]", columnDefinition = " bit(1)  DEFAULT b'0' COMMENT '是否激活'")
    private Boolean active = false;


    @Column(name = "noacc", columnDefinition = " decimal(10, 2)  comment '未收账款金额（待核销）' ")
    private BigDecimal noacc = BigDecimal.ZERO;

	@Column(name = "credit", columnDefinition = " decimal(10, 2)  comment '累计充值金额' ")
	private BigDecimal credit = BigDecimal.ZERO;
	
	@Column(name = "adjust", columnDefinition = " decimal(10, 2)  comment '累计调整金额（坏账，折扣，抹零等金额调整）' ")
	private BigDecimal adjust = BigDecimal.ZERO;

	@Column(name = "writeoff", columnDefinition = " decimal(10, 2)  comment '累计核销金额' ")
    private BigDecimal writeoff = BigDecimal.ZERO;

    @Column(name = "balance", columnDefinition = " decimal(10, 2)  comment '可用于核销的企业余额' ")
    private BigDecimal balance = BigDecimal.ZERO;

    @Column(name = "[lsys]", columnDefinition = " bit(1)  DEFAULT b'0' COMMENT '是否为系统内置保留记录'")
    private Boolean lsys;

    /**
     * 备注
     */
    @Column(name = "remark", columnDefinition = " varchar(255)  default '' comment '备注' ")
    private String remark;

    @Lob
    @Basic(fetch = FetchType.LAZY)
    @Column(name = "signature", columnDefinition = "LONGTEXT  comment '签名'")
    private String signature;

}

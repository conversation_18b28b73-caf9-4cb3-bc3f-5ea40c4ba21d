package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Classname RoomRateEntity
 * @Description 房价表实体
 * @Date 2024-03-17 1:12
 * <AUTHOR> sancho.shen
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "room_rate")
public class RoomRate extends BaseEntity implements Serializable {

    /**
     * 房价代码
     */
    @Column(name = "code", unique = true, nullable = false, columnDefinition = " varchar(255)  default '' comment '房价代码' ")
    private String code;

    /**
     * 酒店ID
     */
    @Column(name = "hotelid", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '酒店ID' ")
    private String hotelId;

    /**
     * 描述
     */
    @Column(name = "description", nullable = false, columnDefinition = " varchar(255)  default '' comment '描述' ")
    private String description;

    /**
     * 开始时间
     */
    @Column(name = "start_time", updatable = false, nullable = false, columnDefinition = "datetime comment '结束时间'")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /**
     * 结束时间
     */
    @Column(name = "end_time", updatable = false, nullable = false, columnDefinition = "datetime comment '结束时间'")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 账项代码
     */
    @Column(name = "department_code", nullable = false, columnDefinition = " varchar(10)  default '' comment '账项代码' ")
    private String departmentCode;

    /**
     * 包价代码
     */
    @Column(name = "include_code", nullable = false, columnDefinition = " varchar(500)  default '' comment '包价代码,多个以逗号分隔' ")
    private String includeCode; //3|BRF,1|WC,2|TK

    /**
     * 频率
     */
    @Column(name = "frequency", nullable = false, columnDefinition = " int(1) comment '频率:1->每分钟,2->每小时，3->每日,4->每周，5->每月' ")
    private Integer frequency;

    /**
     * 间隔小时数(算钟点房)
     */
    @Column(name = "interval_hour", columnDefinition = " int(2) comment '间隔小时数(算钟点房)' ")
    private Integer intervalHour;

    @Column(precision = 5, nullable = true, name = "[seq]", columnDefinition = "int(5)  DEFAULT '999' COMMENT '排序' ")
    private Integer seq = 999;


    @Column(name = "[lsys]", columnDefinition = " bit(1)  DEFAULT b'0' COMMENT '是否为系统内置保留记录'")
    private Boolean lsys;

}

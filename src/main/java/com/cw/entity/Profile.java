package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Classname Profile
 * @Description 档案表实体
 * @Date 2024-03-22 20:18
 * <AUTHOR> sancho.shen
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "profile")
public class Profile extends BaseEntity implements Serializable {
    /**
     * 酒店id
     */
    @Column(name = "hotelid", length = 10, nullable = false, columnDefinition = " varchar(10) default '' comment '酒店ID' ")
    private String hotelId;

    @Column(name = "profile_number", length = 15, nullable = false, columnDefinition = " varchar(15) default '' comment '档案号' ")
    private String profileNumber;

    @Column(name = "guest_name", length = 50, nullable = false, columnDefinition = " varchar(50) default '' comment '客人名' ")
    private String guestName;

    @Column(name = "gender", length = 1, nullable = false, columnDefinition = " int(1) comment '性别:0->女;1->男' ")
    private Integer gender = 1;

    @Column(name = "telephone", length = 15, columnDefinition = " varchar(15) default '' comment '联系方式' ")
    private String telephone;

    @Column(name = "address", columnDefinition = " varchar(255) default '' comment '地址' ")
    private String address;

    @Column(name = "country", columnDefinition = " varchar(50) default '' comment '国家' ")
    private String country = "CN";

    @Column(name = "province", columnDefinition = " varchar(50) default '' comment '省份' ")
    private String province;

    @Column(name = "city", columnDefinition = " varchar(50) default '' comment '城市' ")
    private String city;

    @Column(name = "nation", columnDefinition = " varchar(50) default '' comment '城市' ")
    private String nation;

    @Column(name = "id_card", unique = true, nullable = false, columnDefinition = " varchar(30)  default '' comment '证件号码' ")
    private String idCard;

    @Column(name = "id_type", columnDefinition = " int(2) default 1 comment '证件类型:0-未知,1-身份证,2-护照,3-驾驶证,4-居住证,5-港澳证,99-其他证件' ")
    private int idType = 1;

    @Column(name = "preference", columnDefinition = " varchar(255) default '' comment '喜好' ")
    private String preference;

    @Column(name = "remark", columnDefinition = " varchar(255) default '' comment '备注' ")
    private String remark;

    @Column(name = "profile_type", length = 1, columnDefinition = " int(1) default 0 comment '档案类型:0->个人;1->公司' ")
    private int profileType = 0;

    @Column(name = "language", length = 10, columnDefinition = " varchar(10) default '' comment '语言' ")
    private String language;

    @Column(name = "last_checkin", columnDefinition = "datetime DEFAULT '1900-01-01 00:00:00' comment '最后入住时间'")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date lastCheckinDate;

    @Column(name = "checkin_num", columnDefinition = " int(11) default 0 comment '入住次数' ")
    private int checkinNum;

    @Column(name = "birthday", columnDefinition = " datetime comment '生日' ")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date birthday;

    @Column(name = "night_total", columnDefinition = " int(11) default 0 comment '入住间夜量' ")
    private int nightTotal = 0;

    @Column(name = "avatar", columnDefinition = " varchar(255) default '' comment '身份证头像地址' ")
    private String avatar;

    @Column(precision = 8, scale = 2, nullable = true, name = "[consume_total]", columnDefinition = " decimal(8,2)  DEFAULT '0.00' COMMENT '消费总金额' ")
    private BigDecimal consumeTotal;

    @Column(name = "front_picture", columnDefinition = " varchar(255) default '' comment '证件正面图片地址' ")
    private String frontPicture;

    @Column(name = "back_picture", columnDefinition = " varchar(255) default '' comment '证件背面图片地址' ")
    private String backPicture;


}

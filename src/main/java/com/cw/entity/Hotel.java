package com.cw.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.cw.utils.annotion.PropertyMsg;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Classname HotelEntity
 * @Description 酒店表实体
 * @Date 2024-03-15 22:54
 * <AUTHOR> sancho.shen
 */
@EqualsAndHashCode(callSuper = true)
@Table(name = "hotel")
@Entity
@Data
@DynamicUpdate
public class Hotel extends BaseEntity implements Serializable {

    /**
     * 酒店id
     */
    @Column(name = "hotelid", unique = true, length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '酒店ID' ")
    private String hotelId;

    @Column(name = "hotel_name", length = 30, nullable = false, columnDefinition = " varchar(30)  default '' comment '酒店名称' ")
    private String hotelName;

    @Column(name = "address", length = 300, nullable = true, columnDefinition = " varchar(8)  default '' comment '地址' ")
    private String address;

    @Column(name = "description", columnDefinition = " varchar(255)  default '' comment '描述' ")
    private String description;


    @Column(name = "operator", length = 15, nullable = true, columnDefinition = " varchar(15)  default '' comment '经营人' ")
    private String operator;

    @Column(name = "id_card", length = 18, nullable = true, columnDefinition = " varchar(18)  default '' comment '身份证' ")
    private String idCard;


    @Column(name = "ckouttime", length = 15, nullable = true, columnDefinition = " varchar(15)  default '' comment '离店时间' ")
    private String ckouttime;

    @ApiModelProperty(value = "创建日期")
    @JSONField(format = "yyyy-MM-dd")
    @Column(
            nullable = true,
            name = "[endsell]",
            columnDefinition = " datetime  DEFAULT '1900-01-01 00:00:00' COMMENT '售卖结束日期' "
    )
    Date createDate;

    @Column(name = "room_total", nullable = false, columnDefinition = " int(10)  comment '房间总数' ")
    private int roomTotal;

    @Column(name = "telephone", length = 15, nullable = true, columnDefinition = " varchar(15)  default '' comment '联系电话' ")
    private String telephone;

    
    @Column(name = "remark", nullable = false, columnDefinition = " varchar(255)  default '' comment '备注' ")
    private String remark;

    @Column(name = "brand_id", length = 10, nullable = true, columnDefinition = " varchar(10)  comment '品牌ID' ")
    private String brandId;

    @Lob
    @Basic(fetch = FetchType.LAZY)
    @Column(name = "agreement", length = 50, nullable = true)
    private String agreement;

    @Lob
    @Basic(fetch = FetchType.LAZY)
    @Column(name = "instructions", length = 50, nullable = true)
    private String instructions;

    @Column(name = "weekend_Definition", length = 500, nullable = true, columnDefinition = " varchar(500)  comment '周末定义' ")
    private String weekendDefinition;

    @Lob
    @Basic(fetch = FetchType.LAZY)
    @Column(name = "qr_infos", length = 1000, columnDefinition = " LONGTEXT  comment '收款码图片合集' ")
    private String qrInfos;

    @Lob
    @Basic(fetch = FetchType.LAZY)
    @Column(name = "self_modes", length = 1000, columnDefinition = " LONGTEXT  comment '自助模式' ")
    private String selfModes;
    @Column(name = "business_license", length = 500, nullable = true, columnDefinition = " varchar(50)  default '' comment '营业执照' ")
    private String businessLicense;

    public void setBusinessLicense(String businessLicense) {
        this.businessLicense = businessLicense;
    }
}

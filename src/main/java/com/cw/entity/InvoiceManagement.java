package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * @Description: 发票管理实体
 * @Author: michael.pan
 * @Date: 2024/3/23 15:51
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "Invoice_Management")
@Entity
public class InvoiceManagement extends BaseEntity implements Serializable {
    @Column(name = "hotelid", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '酒店ID' ")
    private String hotelId;

    /**
     * 公司名
     */
    @Column(name = "company_name", columnDefinition = " varchar(64)  default '' comment '公司名' ")
    private String companyName;
    /**
     * 税号
     */
    @Column(name = "taxid", columnDefinition = " varchar(64)  default '' comment '税号' ")
    private String taxId;

    /**
     * 地址
     */
    @Column(name = "address",columnDefinition = " varchar(255)  default '' comment '地址' ")
    private String address;

    /**
     * 电话
     */
    @Column(name = "telephone", length = 15,columnDefinition = " varchar(15)  default '' comment '电话' ")
    private String telephone;

    /**
     * 开户银行
     */
    @Column(name = "open_blank",  columnDefinition = " varchar(32)  default '' comment '开户银行' ")
    private String openBlank;
    /**
     * 银行账户
     */
    @Column(name = "blank_account", columnDefinition = " varchar(24)  default '' comment '银行账户' ")
    private String blankAccount;

    /**
     * 开票人
     */
    @Column(name = "drawer",columnDefinition = " varchar(32)  default '' comment '开票人' ")
    private String drawer;

    /**
     * 开票人邮箱
     */
    @Column(name = "drawer_email", columnDefinition = " varchar(32)  default '' comment '开票人邮箱' ")
    private String drawerEmail;

    /**
     * 开票人电话
     */
    @Column(name = "drawer_phone",  columnDefinition = " varchar(15)  default '' comment '开票人电话' ")
    private String drawerPhone;

    /**
     * 客人手机
     */
    @Column(name = "guest_phone",columnDefinition = " varchar(15)  default '' comment '客人手机' ")
    private String guestPhone;

    /**
     * 客人邮箱
     */
    @Column(name = "guest_email",  columnDefinition = " varchar(32)  default '' comment '客人邮箱' ")
    private String guestEmail;

    /**
     * 酒店邮箱
     */
    @Column(name = "hotel_email", columnDefinition = " varchar(32)  default '' comment '酒店邮箱' ")
    private String hotelEmail;

    /**
     * 单据类型,0-其他，1-账单、2-押金单
     */
    @Column(name = "invoices_type", columnDefinition = " int(2) comment '单据类型,0-其他，1-账单、2-押金单' ")
    private int invoicesType;

}

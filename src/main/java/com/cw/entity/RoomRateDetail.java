package com.cw.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Classname RoomRateDetailEntity
 * @Description 房价详情表实体
 * @Date 2024-03-18 21:05
 * <AUTHOR> sancho.shen
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "room_rate_detail")
@Entity
public class RoomRateDetail extends BaseEntity implements Serializable {

    /**
     * 房价代码
     */
    @Column(name = "rate_code", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '房价代码' ")
    private String rateCode;

    /**
     * 酒店ID
     */
    @Column(name = "hotelid", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '酒店ID' ")
    private String hotelId;

    /**
     * 1人价
     */
    @Column(name = "rate1", columnDefinition = " decimal(10, 2)  comment '1人价' ")
    private BigDecimal rate1;

    /**
     * 2人价
     */
    @Column(name = "rate2", columnDefinition = " decimal(10, 2)  comment '2人价' ")
    private BigDecimal rate2;

    /**
     * 3人价
     */
    @Column(name = "rate3", columnDefinition = " decimal(10, 2)  comment '3人价' ")
    private BigDecimal rate3;

    /**
     * 4人价
     */
    @Column(name = "rate4", columnDefinition = " decimal(10, 2)  comment '4人价' ")
    private BigDecimal rate4;

    /**
     * 5人价
     */
    @Column(name = "rate5", columnDefinition = " decimal(10, 2)  comment '5人价' ")
    private BigDecimal rate5;

    /**
     * 1人周末价
     */
    @Column(name = "week_rate1", columnDefinition = " decimal(10, 2)  comment '1人周末价' ")
    private BigDecimal weekRate1;

    /**
     * 2人周末价
     */
    @Column(name = "week_rate2", columnDefinition = " decimal(10, 2)  comment '2人周末价' ")
    private BigDecimal weekRate2;

    /**
     * 3人周末价
     */
    @Column(name = "week_rate3", columnDefinition = " decimal(10, 2)  comment '3人周末价' ")
    private BigDecimal weekRate3;

    /**
     * 4人周末价
     */
    @Column(name = "week_rate4", columnDefinition = " decimal(10, 2)  comment '4人周末价' ")
    private BigDecimal weekRate4;

    /**
     * 5人周末价
     */
    @Column(name = "week_rate5", columnDefinition = " decimal(10, 2)  comment '5人周末价' ")
    private BigDecimal weekRate5;

    /**
     * 开始时间
     */
    @Column(name = "start_time", nullable = false, columnDefinition = "datetime comment '开始时间'")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /**
     * 结束时间
     */
    @Column(name = "end_time", nullable = false, columnDefinition = "datetime comment '结束时间'")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 房型代码
     */
    @Column(name = "room_type", columnDefinition = " varchar(255) default '' comment '房型代码' ")
    private String roomType;

}

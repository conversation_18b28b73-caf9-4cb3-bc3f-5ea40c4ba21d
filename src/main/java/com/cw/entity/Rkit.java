package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@SuppressWarnings("serial")
@EqualsAndHashCode(callSuper = false)
@Data
@Entity
@Table(name = "rkit")
public class Rkit implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "[id]", nullable = false)
    private Long id;


    @Column(length = 10, nullable = false, name = "[hotelid]", columnDefinition = "varchar(10) NOT NULL COMMENT '酒店ID'")
    private String hotelId;

    @Column(length = 20, nullable = true, name = "[channel_id]", columnDefinition = "varchar(20) NOT NULL COMMENT '渠道代码'")
    private String channelId;

    @Column(length = 32, nullable = false, name = "[kitcode]", columnDefinition = "varchar(32) NOT NULL COMMENT '关联productkit.kit_code'")
    private String kitcode;

    @Column(nullable = false, name = "[datum]", columnDefinition = "datetime NOT NULL COMMENT '日期'")
    private Date datum;

    @Column(nullable = false, name = "[pickup]", columnDefinition = "int(4) NOT NULL DEFAULT 0.00 COMMENT '已用数'")
    private int pickup;

    @Column(nullable = false, name = "[avl]", columnDefinition = "int(4) DEFAULT 0.00 COMMENT '预留数'")
    private int avl;
} 
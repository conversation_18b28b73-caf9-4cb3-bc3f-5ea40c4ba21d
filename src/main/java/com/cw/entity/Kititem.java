package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.io.Serializable;

@SuppressWarnings("serial")
@EqualsAndHashCode(callSuper = false)
@Data
@Entity
@Table(name = "kititem")
public class Kititem implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "[id]", nullable = false)
    private Long id;

    @Column(length = 10, nullable = false, name = "[hotelid]", columnDefinition = "varchar(10) NOT NULL COMMENT '酒店ID'")
    private String hotelId;

    @Column(length = 32, nullable = false, name = "[kitcode]", columnDefinition = "varchar(32) NOT NULL COMMENT '关联productkit.kit_code'")
    private String kitcode;

    @Column(length = 1, nullable = false, name = "[ptype]", columnDefinition = "varchar(1) NOT NULL COMMENT '项目类型 (R,T)'")
    private String ptype;

    @Column(length = 32, nullable = false, name = "[sku]", columnDefinition = "varchar(32) NOT NULL COMMENT '关联的产品SKU ID,可以是房型 ID'")
    private String sku;

    @Column(length = 100, nullable = false, name = "[item_name]", columnDefinition = "varchar(100) NOT NULL COMMENT '产品名称 (冗余)'")
    private String itemName;

    @Column(nullable = false, name = "[quantity]", columnDefinition = "int NOT NULL COMMENT '数量'")
    private Integer quantity = 1;

}
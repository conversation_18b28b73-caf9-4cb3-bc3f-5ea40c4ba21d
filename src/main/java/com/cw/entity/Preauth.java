package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 预授权表
 * @Author: Just
 * @Date: 2025-06-20
 */
@Data
@Table(name = "preauth")
@Entity
public class Preauth implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "Preauth")
    @Column(length = 10, nullable = false, name = "[id]")
    private Long id = 0L;

    /**
     * 酒店ID, 关联 Reservation.hotelId
     */
    @Column(name = "hotelid", length = 10, nullable = false, columnDefinition = "varchar(10) comment '酒店ID'")
    private String hotelId;

    /**
     * 关联的预定号 (冗余字段, 方便查询, 关联 Reservation.reservationNumber)
     */
    @Column(name = "rsno", length = 30, nullable = false, columnDefinition = "varchar(30) comment '关联的预定号 (冗余, 关联 Reservation.reservationNumber)'")
    private String rsno;

    /**
     * 系统内部生成的唯一交易号
     */
    @Column(name = "transactionid", length = 64, nullable = false, unique = true, columnDefinition = "varchar(64) comment '系统内部生成的唯一交易号'")
    private String transactionid;

    /**
     * 支付网关返回的交易流水号
     */
    @Column(name = "gatewayid", length = 128, columnDefinition = "varchar(128) comment '支付网关返回的交易流水号'")
    private String gatewayid;

    /**
     * 银行或支付机构返回的授权码
     */
    @Column(name = "authcode", length = 64, columnDefinition = "varchar(64) comment '银行或支付机构返回的授权码'")
    private String authcode;

    /**
     * 交易类型 (1:发起, 2:追加, 3:完成, 4:撤销)
     */
    @Column(name = "type", nullable = false, columnDefinition = "tinyint comment '交易类型 (1:发起, 2:追加, 3:完成, 4:撤销)'")
    private Integer type;

    /**
     * 状态 (1:处理中, 2:成功, 3:失败, 4:已过期)
     */
    @Column(name = "status", nullable = false, columnDefinition = "tinyint comment '状态 (1:处理中, 2:成功, 3:失败, 4:已过期)'")
    private Integer status;

    /**
     * 操作金额
     */
    @Column(name = "amount", precision = 10, scale = 2, nullable = false, columnDefinition = "decimal(10,2) comment '操作金额'")
    private BigDecimal amount;

    /**
     * (预授权完成时)实际扣款金额
     */
    @Column(name = "completedamount", precision = 10, scale = 2, columnDefinition = "decimal(10,2) comment '(预授权完成时)实际扣款金额'")
    private BigDecimal completedamount;

    /**
     * 货币代码, e.g., "CNY"
     */
    @Column(name = "currency", length = 8, nullable = false, columnDefinition = "varchar(8) default 'CNY' comment '货币代码'")
    private String currency;

    /**
     * 卡号末四位
     */
    @Column(name = "cardlastfour", length = 4, columnDefinition = "varchar(4) comment '卡号末四位'")
    private String cardlastfour;

    /**
     * 卡类型 (VISA, MasterCard, etc.)
     */
    @Column(name = "cardtype", length = 20, columnDefinition = "varchar(20) comment '卡类型 (VISA, MasterCard, etc.)'")
    private String cardtype;

    /**
     * 支付方式 (e.g., "CreditCard", "Alipay")
     */
    @Column(name = "paymentmethod", length = 32, nullable = false, columnDefinition = "varchar(32) comment '支付方式'")
    private String paymentmethod;

    /**
     * 关联的原始预授权ID（用于完成/撤销/追加操作）
     */
    @Column(name = "relatedauthid", columnDefinition = "bigint comment '关联的原始预授权ID（用于完成/撤销/追加操作）'")
    private Long relatedauthid;

    /**
     * 错误码
     */
    @Column(name = "errorcode", length = 32, columnDefinition = "varchar(32) comment '错误码'")
    private String errorcode;

    /**
     * 错误信息
     */
    @Column(name = "errormessage", length = 255, columnDefinition = "varchar(255) comment '错误信息'")
    private String errormessage;

    /**
     * 备注
     */
    @Column(name = "remark", length = 255, columnDefinition = "varchar(255) comment '备注'")
    private String remark;

    /**
     * 授权成功时间
     */
    @Column(name = "authorizedat", columnDefinition = "datetime comment '授权成功时间'")
    private Date authorizedat;

    /**
     * 预授权预计过期时间
     */
    @Column(name = "expytime", columnDefinition = "datetime comment '预授权预计过期时间'")
    private Date expytime;

    /**
     * 完成/撤销时间
     */
    @Column(name = "completetime", columnDefinition = "datetime comment '完成/撤销时间'")
    private Date completetime;

}
package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description: 客人账目实体
 * @Author: michael.pan
 * @Date: 2024/3/23 15:24
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "guest_accounts_his")
@Entity
public class GuestAccountsHis extends BaseEntity implements Serializable {
    @Column(name = "hotelid", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '酒店ID' ")
    private String hotelId;
    /**
     * 账目ID
     */
    @Column(name = "accountsid", unique = true, length = 10, nullable = false, columnDefinition = " varchar(24)  default '' comment '账目ID' ")
    private String accountsId;

    /**
     * 预定号
     */
    @Column(name = "reservation_number", length = 30, columnDefinition = " varchar(10)  default '' comment '预定号' ")
    private String reservationNumber;

    /**
     * 账项代码
     */
    @Column(name = "department_code", columnDefinition = " varchar(255)  default '' comment '账项代码' ")
    private String departmentCode;

    /**
     * 金额
     */
    @Column(name = "amount", columnDefinition = " decimal(10, 2)  comment '金额' ")
    private BigDecimal amount;
    /**
     * 入账描述
     */
    @Column(name = "description", columnDefinition = " varchar(255)  default '' comment '描述' ")
    private String description;

    /**
     * 入账用户
     */
    @Column(name = "income_user", nullable = false, columnDefinition = " varchar(30)  default '' comment '入账用户' ")
    private String incomeUser;

    /**
     * 入账日期
     */
    @Column(nullable = true, name = "[createdate]", columnDefinition = " datetime  DEFAULT '1900-01-01 00:00:00' COMMENT '创建日期' ")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime createdate;


    /**
     * 备注
     */
    @Column(name = "remark", columnDefinition = " varchar(255)  default '' comment '备注' ")
    private String remark;


    @Column(name = "tradeno", length = 60, columnDefinition = " varchar(60)  default '' comment '本地支付交易流水号' ")
    private String tradeno;

    //接口交易账
    @Column(name = "serialno", length = 60, columnDefinition = " varchar(60)  default '' comment '微信支付宝交易流水号' ")
    private String serialno;

    @Column(name = "paymethod", columnDefinition = " int(3) default 0 comment '支付方式-对应支付宝,微信的原始支付方式' ")
    private int paymethod = 0;

    /**
     * 单价
     */
    @Column(name = "price", columnDefinition = " decimal(10, 2)  comment '单价' ")
    private BigDecimal price = BigDecimal.ZERO;

    /**
     * 数量
     */
    @Column(name = "quantity", columnDefinition = " decimal(10,0)  comment '数量' ")
    private BigDecimal quantity = BigDecimal.ZERO;

    /**
     * 付款金额
     */
    @Column(name = "credit", columnDefinition = " decimal(10, 2)  comment '单价' ")
    private BigDecimal credit = BigDecimal.ZERO;

//    /**
//     * 父节点ID， 用于关联上一级节点（复杂帐关联树级结构预留 eg:税，手续费，捆绑费用等）
//     */
//    @Column(name = "parent_id", length = 24, columnDefinition = " varchar(24)  default '' comment '父节点ID' ")
//    private String parent_id;

    /**
     * 主节点ID， 用于关联所有子节点（树级结构预留 eg:税，手续费，捆绑费用等）
     */
    @Column(name = "master_id", length = 24, columnDefinition = " varchar(24)  default '' comment '主节点ID' ")
    private String master_id;

    /**
     * 营业日期（帐应该累计到的日期）
     */
    @Column(name = "business_date", columnDefinition = " datetime  DEFAULT '1900-01-01 00:00:00' COMMENT '营业日期' ")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate business_date;

    /**
     * 是否为内部调整帐
     */
    @Column(name = "internal", columnDefinition = " bit(1)  default 0 comment '是否为内部调整帐' ")
    private boolean internal = false;

    /**
     * 是否为历史记录，应收帐未结清需保留，但不应参与计算的记录
     */
    @Column(name = "history", columnDefinition = " bit(1)  default 0 comment '是否为历史记录' ")
    private boolean history = false;

    /**
     * 分离原始主帐ID
     */
    @Column(name = "split_from_id", length = 24, columnDefinition = " varchar(24)  default '' comment '记录从分离原始主帐ID' ")
    private String split_from_id;

    /**
     * 应收账户号
     */
    @Column(name = "ar_no", length = 48, columnDefinition = " varchar(48)  default '' comment '应收账户号' ")
    private String ar_no;

    /**
     * 客账结账号（一次结账一次）
     */
    @Column(name = "settlement_no", length = 30, columnDefinition = " varchar(30)  default '' comment '客账结帐号' ")
    private String settlement_no;

    /**
     * 应收付款记录关联的代付帐ID
     */
    @Column(name = "ar_payfor_id", length = 24, columnDefinition = " varchar(24)  default '' comment '应收付款记录关联的代付帐ID' ")
    private String ar_payfor_id;
    
    /**
     * 移入历史的营业日期
     */
    @Column(name = "mov_date", columnDefinition = " datetime  DEFAULT '1900-01-01 00:00:00' COMMENT '移入历史的日期' ")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate mov_date;

    /**
     * 原始预定号（记录最初预定）
     */
    @Column(name = "res_no_org", length = 30, columnDefinition = " varchar(10)  default '' comment '原始预定号' ")
    private String res_no_org;

	/**
	 * 已核销金额
	 */
	@Column(name = "amount_write_off", columnDefinition = " decimal(10,2)  comment '已核销金额' ")
	private BigDecimal amount_write_off = BigDecimal.ZERO;

}

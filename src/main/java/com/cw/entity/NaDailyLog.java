package com.cw.entity;

import com.cw.utils.tool.EntityUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.util.Date;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2024-03-20
 */
@EqualsAndHashCode(callSuper = false)
@Data
@Entity
@Table(name = "nadailylog")
public class NaDailyLog {
    Date sdf = EntityUtil.stringToDate("1900-01-01");
    public NaDailyLog() {
        this.naDate = this.sdf;
    }
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(length = 10, nullable = false, name = "[id]")
    private Long id = 0L;

    @Column(length = 20, nullable = true, name = "[hotelid]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '项目id' ")
    private String hotelId = "";
    @Column(length = 10, nullable = true, name = "[runtime]", columnDefinition = " varchar(10)  DEFAULT '' COMMENT '步奏' ")
    private String runtime = "";
    @Column(length = 20, nullable = true, name = "[progressid]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '进程ID' ")
    private String progressId = "";
    @Column(length = 20, nullable = true, name = "[starttime]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '开始时间' ")
    private String startTime = "";
    @Column(length = 20, nullable = true, name = "[endtime]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '结束时间' ")
    private String endTime = "";
    @Column(length = 20, nullable = true, name = "[totaltime]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '步奏运行的总时间' ")
    private String totalTime = "";
    @Column(length = 20, nullable = true, name = "[status]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '状态：成功、失败' ")
    private String status = "";
    @Column(length = 255, nullable = true, name = "[msg]", columnDefinition = " varchar(255)  DEFAULT '' COMMENT '消息' ")
    private String msg = "";
    @Column(length = 64, nullable = true, name = "[runip]", columnDefinition = " varchar(64)  DEFAULT '' COMMENT '地址' ")
    private String runIp = "";
    @Column(nullable = true, name = "[nadate]", columnDefinition = " datetime  DEFAULT '1900-01-01 00:00:00' COMMENT '夜审日期' ")
    private Date naDate;
}

package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * @Classname ReservationHis
 * @Description 预定表历史实体
 * @Date 2024-03-22 20:25
 * <AUTHOR> sancho.shen
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "reservation_his")
public class ReservationHis extends BaseEntity implements Serializable {
    @Column(name = "hotelid", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '酒店ID' ")
    private String hotelId;

    @Column(name = "reservation_number", unique = true, length = 30, nullable = false, columnDefinition = " varchar(10)  default '' comment '预定号' ")
    private String reservationNumber;

    @Column(name = "relation_number", length = 20, nullable = false, columnDefinition = " varchar(20)  default '' comment '预定关联号' ")
    private String relationNumber;

    @Column(name = "profile_number", length = 15, columnDefinition = " varchar(15)  default '' comment '档案号' ")
    private String profileNumber;

    @Column(name = "guest_name", length = 50, nullable = false, columnDefinition = " varchar(50)  default '' comment '客人名' ")
    private String guestName;

    @Column(name = "arrival_date", nullable = false, columnDefinition = "datetime comment '到店日期' ")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date arrivalDate;

    @Column(name = "departure_date", nullable = false, columnDefinition = "datetime comment '离店日期' ")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date departureDate;

    @Column(nullable = true, name = "[cancel_date]", columnDefinition = " datetime  DEFAULT '1900-01-01 00:00:00' COMMENT '取消时间' ")
    LocalDateTime cancelDate;
    @Column(nullable = true, name = "[checkin_date]", columnDefinition = " datetime  DEFAULT '1900-01-01 00:00:00' COMMENT '入住时间' ")
    LocalDateTime checkinDate;
    @Column(nullable = true, name = "[checkout_date]", columnDefinition = " datetime  DEFAULT '1900-01-01 00:00:00' COMMENT '离店时间' ")
    LocalDateTime checkoutDate;


    /**
     * 价格、保留两位小数
     */
    @Column(name = "total_price", nullable = false, columnDefinition = " decimal(10, 2) default 0  comment '总房价、保留2位小数' ")
    private BigDecimal totalPrice = BigDecimal.ZERO;


    /**
     * 价格、保留两位小数
     */
    @Column(name = "price", nullable = false, columnDefinition = " decimal(10, 2) default 0  comment '当晚房费' ")
    private BigDecimal price = BigDecimal.ZERO;

    /**
     * 订金、保留两位小数
     */
    @Column(name = "deposit", nullable = false, columnDefinition = " decimal(10, 2) default 0  comment '订金、保留2位小数' ")
    private BigDecimal deposit = BigDecimal.ZERO;

    /**
     * 消费金额、保留两位小数
     */
    @Column(name = "consume", nullable = false, columnDefinition = " decimal(10, 2) default 0   comment '消费金额、保留2位小数' ")
    private BigDecimal consume = BigDecimal.ZERO;

    /**
     * 余额、保留两位小数
     */
    @Column(name = "balance", nullable = false, columnDefinition = " decimal(10, 2) default 0  comment '余额、保留2位小数' ")
    private BigDecimal balance = BigDecimal.ZERO;

    /**
     * 房价代码
     */
    @Column(name = "rate_code", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '房价代码' ")
    private String rateCode;

    /**
     * 房型代码
     */
    @Column(name = "room_type", nullable = false, columnDefinition = " varchar(100) default '' comment '房型代码' ")
    private String roomType;

    @Column(name = "room_number", columnDefinition = " varchar(10) default '' comment '房间号' ")
    private String roomNumber;

    @Column(name = "person_total", columnDefinition = " int(5) comment '总人数' ")
    private int personTotal;

    @Column(name = "channel", columnDefinition = " varchar(50) default '' comment '渠道来源' ")
    private String channel;
    @Column(name = "room_night", length = 3, nullable = false, columnDefinition = " int(3) default 1 comment '房晚数' ")
    private int roomNight = 1;

    @Column(name = "payment_type", columnDefinition = " varchar(15) default '' comment '付款方式' ")
    private String paymentType;

    @Column(name = "share", columnDefinition = " int(1) comment '是否合住:0->否;1->是' ")
    private int share;

    @Column(name = "remark", columnDefinition = " varchar(255) default '' comment '备注' ")
    private String remark;

    @Column(name = "reason", columnDefinition = " varchar(150) default '' comment '换房原因' ")
    private String reason;
    @Column(name = "rooms", length = 3, nullable = false, columnDefinition = " int(3) default 1 comment '预订房间数' ")
    private int rooms = 1;
    @Column(name = "[fixrate]", columnDefinition = " bit(1)  DEFAULT b'0' COMMENT '是否固定价格'")
    private boolean fixrate = false;

    @Column(name = "telephone", columnDefinition = " varchar(11) default '' comment '联系电话' ")
    private String telephone;
    @Column(name = "reservation_type", columnDefinition = " varchar(30) default '' comment '预定类型' ")
    private String reservationType;
    @Lob
    @Basic(fetch = FetchType.LAZY)
    @Column(name = "accompany", columnDefinition = " LONGTEXT  comment '陪同人员' ")
    private String accompany;
    @Column(name = "reservation_status", columnDefinition = " int(1) default 0 comment '预订状态:0->预订;1->在住;2->离店;3->应到未到;4->应离未离;-1->取消'")
    private int reservationStatus = 0;


    @Column(length = 20, name = "[salerid]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '销售员编号' ")
    private String salerid = "";
}

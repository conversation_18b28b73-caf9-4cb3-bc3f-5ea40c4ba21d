package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * @Description: 付款方式实体表
 * @Author: michael.pan
 * @Date: 2024/3/22 21:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "payment")
public class Payment extends BaseEntity implements Serializable {
    @Column(name = "hotelid", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '酒店ID' ")
    private String hotelId;

    @Column(name = "payment_code", nullable = false, columnDefinition = " varchar(64)  default '' comment '付款代码' ")
    private String payCode;

    @Column(name = "description", columnDefinition = " varchar(255)  default '' comment '描述' ")
    private String description;

    @Column(name = "department_code", columnDefinition = " varchar(255)  default '' comment '账项代码' ")
    private String departmentCode;


    @Column(name = "ifctype", columnDefinition = " varchar(20)  default '' comment '接口类型-POS机/支付API接口类型 不为空就是线上支付' ")
    private String ifctype;

    @Column(precision = 5, nullable = true, name = "[seq]", columnDefinition = "int(5)  DEFAULT '999' COMMENT '排序' ")
    private Integer seq = 999;

    @Column(name = "[lsys]", columnDefinition = " bit(1)  DEFAULT b'0' COMMENT '是否为系统内置保留记录'")
    private Boolean lsys;

}

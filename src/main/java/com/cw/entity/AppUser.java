package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.io.Serializable;

@SuppressWarnings("serial")
@EqualsAndHashCode(callSuper = false)
@Data
@Entity
@Table(name = "app_user")
public class AppUser implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(length = 10, nullable = false, name = "[id]")
    private Long id = 0L;




    @Column(length = 20, nullable = true, name = "[groupid]", columnDefinition = " varchar(20) DEFAULT '' COMMENT '集团代码' ")
    private String groupId = "";


    @Column(length = 30, nullable = true, name = "[userid]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '用户' ")
    private String userid = "";



    @Column(length = 30, nullable = true, name = "[username]", columnDefinition = " varchar(30)  DEFAULT '' COMMENT '用户姓名' ")
    private String username = "";

    @Column(length = 100, nullable = true, name = "[pwd]", columnDefinition = " varchar(100)  DEFAULT '' COMMENT '加密存储密码' ")
    private String pwd = "";

    @Column(length = 10, nullable = true, name = "[mobileno]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '手机号码' ")
    private String mobileNo = "";




    @Column(name = "[ostatus]", columnDefinition = " bit(1)  DEFAULT b'0' COMMENT '冻结状态'")
    private Boolean oStatus = false;

}

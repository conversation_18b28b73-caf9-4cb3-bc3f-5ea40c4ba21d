package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2024-03-20
 */
@EqualsAndHashCode(callSuper = false)
@Data
@Entity
@Table(name = "naparam")
public class NaParam {



    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(length = 10, nullable = false, name = "[id]")
    private Long id = 0L;

    @Column(length = 20, nullable = true, name = "[hotelid]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '项目id' ")
    private String hotelId = "";
    @Column(length = 10, nullable = true, name = "[progressid]", columnDefinition = " varchar(10)  DEFAULT '' COMMENT '进程ID' ")
    private String progressId = "";

    @Column(length = 20, nullable = true, name = "[paramname]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '参数名' ")
    private String paramName = "";

    @Column(length = 200, nullable = true, name = "[paramvalue]", columnDefinition = " varchar(200)  DEFAULT '' COMMENT '参数值' ")
    private String paramValue = "";
}

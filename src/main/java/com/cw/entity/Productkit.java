package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@SuppressWarnings("serial")
@EqualsAndHashCode(callSuper = false)
@Data
@Entity
@javax.persistence.Table(name = "productkit")
public class Productkit implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "[id]", nullable = false)
    private Long id;

    @Column(length = 10, nullable = false, name = "[hotelid]", columnDefinition = "varchar(10) NOT NULL COMMENT '酒店ID'")
    private String hotelId;

    @Column(length = 32, nullable = false, name = "[kitcode]", columnDefinition = "varchar(32) NOT NULL UNIQUE COMMENT '套餐业务代码,唯一'")
    private String kitcode;

    @Column(length = 100, nullable = false, name = "[prodesc]", columnDefinition = "varchar(100) NOT NULL COMMENT '套餐名称'")
    private String prodesc;

    @Column(nullable = false, name = "[status]", columnDefinition = "tinyint NOT NULL COMMENT '状态 (1:启用, 0:禁用)'")
    private Integer status;

    @Column(nullable = false, name = "[totalprice]", columnDefinition = "decimal(10,2) NOT NULL COMMENT '套餐明细售价'")
    private BigDecimal totalprice;

    @Column(nullable = false, name = "[price]", columnDefinition = "decimal(10,2) NOT NULL COMMENT '套餐基础售价'")
    private BigDecimal price;

    @Column(nullable = false, name = "[kit_details]", columnDefinition = "varchar(255) NOT NULL UNIQUE COMMENT '套餐明细'")
    private String kitDetails;

    @Column(nullable = false, name = "[nights]", columnDefinition = "int NOT NULL COMMENT '可兑换的间夜数'")
    private Integer nights;

    @Column(nullable = false, name = "[dailylimit]", columnDefinition = "int NOT NULL COMMENT '每日库存 (-1表示不限制)'")
    private Integer dailylimit;

    @Column(nullable = false, name = "[sale_start_date]", columnDefinition = "date NOT NULL COMMENT '售卖开始日期'")
    private Date saleStartDate;

    @Column(nullable = false, name = "[sale_end_date]", columnDefinition = "date NOT NULL COMMENT '售卖结束日期'")
    private Date saleEndDate;

    @Column(name = "[validity_start_date]", columnDefinition = "date COMMENT '预约有效期开始'")
    private Date validityStartDate;

    @Column(name = "[validity_end_date]", columnDefinition = "date COMMENT '预约有效期结束'")
    private Date validityEndDate;

    @Column(nullable = false, name = "[checkin_start_date]", columnDefinition = "date NOT NULL COMMENT '可入住开始日期'")
    private Date checkinStartDate;

    @Column(nullable = false, name = "[checkin_end_date]", columnDefinition = "date NOT NULL COMMENT '可入住结束日期'")
    private Date checkinEndDate;

    @Column(name = "[adv_days]", columnDefinition = "int COMMENT '需提前几天预约'")
    private Integer adv_days;

    @Column(name = "[proddetail]", columnDefinition = "text COMMENT '产品内容描述'")
    private String proddetail;


}
package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Describe 每日统计表
 * <AUTHOR> <PERSON>
 * @Create on 2024-06-21
 */
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "Dailystat")
@Data
public class DailyStat {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "Dailystat")
    @Column(length = 10, nullable = false, name = "[id]")
    private Long id = 0L;/* SQL主键 */

    @Column(length = 20, nullable = true, name = "[hotelid]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '项目id' ")
    private String hotelId = "";

    @Column(nullable = true, name = "[datum]", columnDefinition = " datetime  DEFAULT '1900-01-01 00:00:00' COMMENT '日期' ")
    private Date datum;

    @Column(precision = 20, nullable = true, name = "[unarrival]", columnDefinition = " decimal(20,0)  DEFAULT '0' COMMENT '应到未到订单数' ")
    private Integer unArrival = 0;

    @Lob
    @Basic(fetch = FetchType.LAZY)
    @Column(length = 150, nullable = true, name = "[unarrival_json]")
    private String unArrivalJson;

    @Column(precision = 20, nullable = true, name = "[undept]", columnDefinition = " decimal(20,0)  DEFAULT '0' COMMENT '应离未离订单数' ")
    private Integer unDept = 0;

    @Lob
    @Basic(fetch = FetchType.LAZY)
    @Column(length = 150, nullable = true, name = "[undept_json]")
    private String unDeptJson;

    @Column(precision = 8, nullable = true, name = "[orders]", columnDefinition = " decimal(8,0)  DEFAULT '0' COMMENT '今日总订单数' ")
    private Integer orders = 0;

    @Column(precision = 8, scale = 2, nullable = true, name = "[sales]", columnDefinition = " decimal(8,2)  DEFAULT '0.00' COMMENT '今日销售总金额' ")
    private BigDecimal sales = BigDecimal.ZERO;


    @Column(precision = 8, scale = 2, nullable = true, name = "[incomes]", columnDefinition = " decimal(8,2)  DEFAULT '0.00' COMMENT '今日收入总金额' ")
    private BigDecimal incomes = BigDecimal.ZERO;


    @Lob
    @Basic(fetch = FetchType.LAZY)
    @Column(length = 150, nullable = true, name = "[income_json]")
    private String incomeJson;

    @Lob
    @Basic(fetch = FetchType.LAZY)
    @Column(length = 150, nullable = true, name = "[department_json]")
    private String departmentJson;


    @Lob
    @Basic(fetch = FetchType.LAZY)
    @Column(length = 150, nullable = true, name = "[roomtype_json]")
    private String roomTypeJson;
    
	@Lob
    @Basic(fetch = FetchType.LAZY)
    @Column(length = 150, nullable = true, name = "[trial_balance_json]", columnDefinition = "json")
    private String  trialBalanceJson;
}

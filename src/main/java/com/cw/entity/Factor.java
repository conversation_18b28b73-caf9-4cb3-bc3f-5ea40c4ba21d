package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2024-04-02
 */
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "factor")
@Data
public class Factor {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "Factor")
    @Column(length = 10, nullable = false, name = "[id]")
    private Long id = 0L;/* SQL主键 */
    @Column(length = 20, nullable = true, name = "[hotelid]",columnDefinition=" varchar(20)  DEFAULT '' COMMENT '项目id' ")
    private String hotelId = "";/* 项目id */
    @Column(length = 20, nullable = true, name = "[code]",columnDefinition=" varchar(20)  DEFAULT '' COMMENT '代码' ")
    private String code = "";/* 代码 */
    @Column(length = 60, nullable = true, name = "[description]",columnDefinition=" varchar(60)  DEFAULT '' COMMENT '描述' ")
    private String description = "";/* 描述 */
    @Column(length = 15, nullable = true, name = "[type]",columnDefinition=" varchar(15)  DEFAULT '' COMMENT '代码中定义的组类型' ")
    private String type = "";/* 代码中定义的组类型 */
    @Column(length = 20, nullable = true, name = "[header]",columnDefinition=" varchar(20)  DEFAULT '' COMMENT '所属的factor组' ")
    private String header = "";/* 所属的factor组 */
    @Column(length = 20, nullable = true, name = "[root]",columnDefinition=" varchar(20)  DEFAULT '' COMMENT '所属的factor根节点分组' ")
    private String root = "";/* 所属的factor根节点分组 */

    @Column(precision = 4, nullable = true, name = "[seq]",columnDefinition=" decimal(4,0)  DEFAULT '0' COMMENT '排序字段' ")
    private Integer seq = 0;/* 排序字段 */
    @Column(precision = 1, nullable = true, name = "[status]",columnDefinition=" decimal(1,0)  DEFAULT '0' COMMENT '状态，0-未启动，1-启动' ")
    private Integer status = 0;/* 状态，0-未启动，1-启动 */
    @Column(length = 1, nullable = true, name = "[option]",columnDefinition=" varchar(1)  DEFAULT '' COMMENT '是否多选，1-单选，2-多选' ")
    private String option = "";/* 是否多选，1-单选，2-多选 */


}

package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * @Description: 集团
 * @Author: <PERSON>
 * @Date: 2024/6/23 22:18
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "group_band")
@Entity
public class GroupBand extends BaseEntity implements Serializable {

    /**
     * 代码
     */
    @Column(name = "code", length = 64, nullable = false, columnDefinition = " varchar(64)  default '' comment '集团代码' ")
    private String code;


    /**
     * 描述
     */
    @Column(name = "description", columnDefinition = " varchar(255)  default '' comment '集团描述' ")
    private String description;


    @Column(length = 20, nullable = true, name = "[hotelids]", columnDefinition = " LONGTEXT  COMMENT '关联hotelid' ")
    private String hotelIds = "";



    @Column(name = "[ostatus]", columnDefinition = " bit(1)  DEFAULT b'0' COMMENT '冻结状态'")
    private Boolean oStatus = false;


    @Column(precision = 5, nullable = true, name = "[seq]", columnDefinition = "int(5)  DEFAULT '999' COMMENT '排序' ")
    private Integer seq = 999;



}

package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.io.Serializable;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2024-03-19
 */
@EqualsAndHashCode(callSuper = false)
@Data
@Entity
@Table(name = "Op_role")
public class OpRole implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY,generator = "Op_role")
    @Column(length = 10,nullable = false,name = "[id]")
    private Long id = 0L;

    @Column(length = 20,nullable = true,name = "[hotelid]",columnDefinition = " varchar(20)  DEFAULT '' COMMENT '项目id' ")
    private String hotelId = "";

    @Column(length = 20,nullable = true,name = "[roleid]",columnDefinition = " varchar(20)  DEFAULT '' COMMENT '角色ID' ")
    private String roleId = "";

    @Column(length = 20,nullable = true,name = "[name]",columnDefinition = " varchar(20)  DEFAULT '' COMMENT '角色描述' ")
    private String name = "";

    @Column(length = 4,nullable = true, name = "[sysid]",columnDefinition = " varchar(4)  DEFAULT '' COMMENT '系统编号' ")
    private String sysId = "";


}

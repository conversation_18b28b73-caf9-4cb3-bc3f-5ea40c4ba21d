package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: 账项代码
 * @Author: michael.pan
 * @Date: 2024/3/21 22:18
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "account_item")
@Entity
public class AccountItem extends BaseEntity implements Serializable {

    /**
     * 代码
     */
    @Column(name = "code", length = 64, nullable = false, columnDefinition = " varchar(64)  default '' comment '代码' ")
    private String code;

    /**
     * 酒店ID
     */
    @Column(name = "hotelid", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '酒店ID' ")
    private String hotelId;
    /**
     * 描述
     */
    @Column(name = "description", columnDefinition = " varchar(255)  default '' comment '描述' ")
    private String description;

    /**
     * 收入组（001客房，002餐厅，003商场，004宴会，005其他）
     */
    @Column(name = "income_group", columnDefinition = " varchar(4)  default '' comment '收入组（001客房，002餐厅，003商场，004宴会，005其他）' ")
    private String incomeGroup;

    /**
     * 今日收入
     */
    @Column(name = "income_today", columnDefinition = " decimal(10, 2)  comment '今日收入' ")
    private BigDecimal incomeToday = BigDecimal.ZERO;

    /**
     * 月累计
     */
    @Column(name = "monthly_total", columnDefinition = " decimal(10, 2)  comment '月累计' ")
    private BigDecimal monthlyTotal = BigDecimal.ZERO;
    /**
     * 年累计（9000以上为收入.9000以下为消费）
     */
    @Column(name = "yearly_total", columnDefinition = " decimal(10, 2)  comment '年累计（9000以上为收入.9000以下为消费）' ")
    private BigDecimal yearlyTotal = BigDecimal.ZERO;

    @Column(precision = 5, nullable = true, name = "[seq]", columnDefinition = "int(5)  DEFAULT '999' COMMENT '排序' ")
    private Integer seq = 999;


    @Column(name = "[lsys]", columnDefinition = " bit(1)  DEFAULT b'0' COMMENT '是否为系统内置保留记录'")
    private Boolean lsys;

}

package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Classname StandardGroup
 * @Description 标团表实体
 * @Date 2024-03-22 20:26
 * <AUTHOR> sancho.shen
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "standard_group")
public class StandardGroup extends BaseEntity implements Serializable {
    /**
     * 酒店id
     */
    @Column(name = "hotelid", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '酒店ID' ")
    private String hotelId;

    @Column(name = "group_number", unique = true, length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '团队号' ")
    private String groupNumber;

    @Column(name = "group_name", length = 50, nullable = false, columnDefinition = " varchar(50)  default '' comment '团队名称' ")
    private String groupName;

    @Column(name = "arrival_date", nullable = false, columnDefinition = "datetime comment '到店日期' ")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date arrivalDate;

    @Column(name = "departure_date", nullable = false, columnDefinition = "datetime comment '离店日期' ")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date departureDate;

    /**
     * 房间信息列表
     */
    @Lob
    @Basic(fetch = FetchType.LAZY)
    @Column(name = "room_infos")
    private String roomInfos;

    /**
     * 房型代码
     */
    @Column(name = "room_type", columnDefinition = " varchar(15) default '' comment '房型代码' ")
    private String roomType;

    @Column(name = "room_night", length = 3, nullable = false, columnDefinition = " int(3) comment '房晚数' ")
    private int roomNight;

    @Column(name = "country", columnDefinition = " varchar(50)  default '' comment '国家' ")
    private String country;

    @Column(name = "province", columnDefinition = " varchar(50)  default '' comment '省份' ")
    private String province;

    @Column(name = "city", columnDefinition = " varchar(50)  default '' comment '城市' ")
    private String city;

    @Column(name = "contacter", length = 30, columnDefinition = " varchar(30)  default '' comment '联系人' ")
    private String contacter;

    @Column(name = "telephone", length = 15, columnDefinition = " varchar(15)  default '' comment '联系方式' ")
    private String telephone;

    @Column(name = "remark", columnDefinition = " varchar(255)  default '' comment '备注' ")
    private String remark;
}

package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.io.Serializable;

/**
 * @Description: 房型表实体
 * @Author: michael.pan
 * @Date: 2024/3/17 17:05
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "room_type")
@Data
public class RoomType extends BaseEntity implements Serializable {
    @Column(name = "hotelid", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '酒店ID' ")
    private String hotelId = "";
    @Column(name = "room_type", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '房型代码' ")
    private String roomType = "";
    @Column(name = "description", columnDefinition = " varchar(24)  default '' comment '描述(房型名称)' ")
    private String description = "";
    @Column(name = "room_number", columnDefinition = " int(10)  comment '房间数' ")
    private int roomNumber = 0;
    @Column(name = "people_number", nullable = false, columnDefinition = " int(10)  comment '人数' ")
    private int peopleNumber = 2;
    @Column(name = "building_no", columnDefinition = " varchar(10)  comment '楼号' ")
    private String buildingNo = "";
    @Column(name = "bed_type", nullable = false, columnDefinition = " varchar(20) default '' comment '床型(房间类型)' ")
    private String bedType = "";
    @Lob
    @Basic(fetch = FetchType.LAZY)
    @Column(name = "picture")
    private String picture = "";

    @Column(precision = 5, nullable = true, name = "[seq]", columnDefinition = "int(5)  DEFAULT '999' COMMENT '排序' ")
    private Integer seq = 999;

    @Column(name = "[generic]", columnDefinition = " bit(1)  DEFAULT b'0' COMMENT '是否套房'")
    private Boolean generic = false;

}

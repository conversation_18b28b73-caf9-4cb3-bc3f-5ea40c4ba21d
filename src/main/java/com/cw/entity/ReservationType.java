package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * @Description: 预定类型实体
 * @Author: michael.pan
 * @Date: 2024/3/23 12:49
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "reservation_type")
public class ReservationType extends BaseEntity implements Serializable {
    @Column(name = "hotelid", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '酒店ID' ")
    private String hotelId;

    @Column(name = "code", nullable = false, columnDefinition = " varchar(10)  default '' comment '预定类型代码' ")
    private String code;

    @Column(name = "description", columnDefinition = " varchar(24)  default '' comment '描述（预定类型名称）' ")
    private String description;
}

package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.io.Serializable;

/**
 * @Classname BrandEntity
 * @Description 品牌表实体
 * @Date 2024-03-16 22:11
 * <AUTHOR> sancho.shen
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "brand")
@Data
public class Brand extends BaseEntity implements Serializable {

    @Column(name = "brand_name", unique = true, nullable = false, columnDefinition = " varchar(30)  default '' comment '品牌名称' ")
    private String brandName;

    @Column(name = "brand_id",length = 10,unique = true, nullable = false, columnDefinition = " varchar(10)  default '' comment '品牌ID' ")
    private String brandId;

    @Column(name = "description", columnDefinition = " varchar(255)  default '' comment '描述' ")
    private String description;

}

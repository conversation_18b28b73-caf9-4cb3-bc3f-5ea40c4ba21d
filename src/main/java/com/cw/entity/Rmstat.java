package com.cw.entity;

import com.cw.utils.annotion.PropertyMsg;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 房间状态关联表
 * <AUTHOR>
 * @date 2025/6/16
 **/
@Entity
@Data
@Accessors(chain = true)
@Table(name = "rmstat")
public class Rmstat  implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "hotelid", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '酒店ID' ")
    private String hotelId;
    @Column(name = "reason", length = 10, columnDefinition = " varchar(10)  default '' comment '原因代码' ")
    private String reason;
    @Column(name = "operator", columnDefinition = " varchar(10) default '' comment '操作人' ")
    private String operator;
    @Column(name = "status", columnDefinition = " varchar(4) default '' comment '房间状态（OO）' ")
    private String status;
    @Column(name = "from_date", columnDefinition = " datetime  DEFAULT '1900-01-01 00:00:00' COMMENT '开始日期' ")
    @PropertyMsg(value = "开始日期")
    private Date fromDate;
    @Column(name = "to_date", columnDefinition = " datetime  DEFAULT '1900-01-01 00:00:00' COMMENT '结束日期' ")
    @PropertyMsg(value = "结束日期")
    private Date toDate;
    @Column(name = "returnstat", columnDefinition = " varchar(4)  comment '完成后目标状态' ")
    private String returnstat;
    @Column(name = "room_no", nullable = false, columnDefinition = " varchar(10) default '' comment '房间号' ")
    private String roomNo;
}

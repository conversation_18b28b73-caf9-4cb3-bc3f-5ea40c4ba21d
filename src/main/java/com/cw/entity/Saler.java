package com.cw.entity;

import lombok.Data;

import javax.persistence.*;

/**
 * 销售员ID
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/12/24 14:25
 **/
@Data
@Entity
@Table(name = "saler")
public class Saler {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(length = 10, nullable = false, name = "[id]")
    private Long id = 0L;


    @Column(length = 20, nullable = true, name = "[hotelid]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '酒店Id' ")
    private String hotelId = "";


    @Column(length = 30, nullable = true, name = "[salerid]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '销售员编号' ")
    private String salerid = "";


    @Column(length = 30, nullable = true, name = "[description]", columnDefinition = " varchar(30)  DEFAULT '' COMMENT '描述或姓名' ")
    private String description = "";


    @Column(length = 20, nullable = true, name = "[telephone]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '手机号' ")
    private String telephone = "";

    @Column(name = "[ostatus]", columnDefinition = " bit(1)  DEFAULT b'0' COMMENT '是否启用'")
    private Boolean ostatus = true;
}

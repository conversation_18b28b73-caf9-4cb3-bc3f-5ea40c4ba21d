package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.io.Serializable;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2024-03-21
 */
@EqualsAndHashCode(callSuper = false)
@Data
@Entity
@Table(name = "op_role_right")
public class OpRoleRight implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(length = 10, nullable = false, name = "[id]")
    private Long id = 0L;

    @Column(length = 20, nullable = true, name = "[hotelid]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '项目id' ")
    private String hotelId = "";

    @Column(length = 20, nullable = true, name = "[roleid]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '角色 ID' ")
    private String roleId = "";

    @Column(length = 20, nullable = true, name = "[rightid]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '权限代码' ")
    private String rightId = "";


}

package com.cw.entity;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Describe 房型历史统计
 * <AUTHOR> <PERSON>
 * @Create on 2024-06-26
 */
@Entity
@Table(name = "room_type_stat")
@Data
public class RoomTypeStat {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(length = 10, nullable = false, name = "[id]")
    private Long id = 0L;

    /**
     * 民宿代码
     */
    @Column(name = "hotelid", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '酒店ID' ")
    private String hotelId;

    /**
     * 日期
     */
    @Column(name = "datum", nullable = false, columnDefinition = "datetime comment '日期' ")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date datum;
    /**
     * 房型
     */
    @Column(name = "room_type", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '房型代码' ")
    private String roomType;
    /**
     * 物理房间总数
     */
    @Column(name = "total", columnDefinition = " int(4)  comment  ' 总数' ")
    private int total;

    /**
     * 当日入住占用数
     */
    @Column(name = "checkin", columnDefinition = " int(4)  comment  '办理入住占用数' ")
    private int checkIn;
    /**
     * 当日预订占用数
     */
    @Column(name = "pickup", columnDefinition = " int(4)  comment  '预订未入住占用数' ")
    private int pickup;

    /**
     * 当日维修的房间数
     */
    @Column(name = "ooo", columnDefinition = " int(4)  comment  '维修房间数' ")
    private int ooo;

    /**
     * 价格、保留两位小数
     */
    @Column(name = "total_price", nullable = false, columnDefinition = " decimal(10, 2) default 0  comment '房型总收入、保留2位小数' ")
    private BigDecimal totalPrice = BigDecimal.ZERO;

    /**
     * 价格、保留两位小数
     */
    @Column(name = "avg_price", nullable = false, columnDefinition = " decimal(10, 2) default 0  comment '平均房价、保留2位小数' ")
    private BigDecimal avgPrice = BigDecimal.ZERO;

    /**
     * 价格、保留两位小数
     */
    @Column(name = "letting_rate", nullable = false, columnDefinition = " decimal(10, 2) default 0  comment '出租率、保留2位小数' ")
    private BigDecimal lettingRate = BigDecimal.ZERO;


}

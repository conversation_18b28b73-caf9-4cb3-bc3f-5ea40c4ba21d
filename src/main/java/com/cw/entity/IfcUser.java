package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;

@Data
@Entity
@Table(name = "ifc_user")
public class IfcUser {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(length = 10, nullable = false, name = "[id]")
    private Long id = 0L;

    /**
     * 应用ID
     */
    @Column(name = "appid", length = 64, nullable = false, columnDefinition = "varchar(64) comment '应用租户ID'")
    private String appid;

    /**
     * 应用密钥
     */
    @Column(name = "secret", length = 128, nullable = false, columnDefinition = "varchar(128) comment '应用密钥'")
    private String secret;

    /**
     * 酒店ID
     */
    @Column(name = "hotelid", length = 64, nullable = false, columnDefinition = "varchar(64) comment '酒店ID'")
    private String hotelId;

    /**
     * 配置YAML
     */
    @Column(name = "confyaml", columnDefinition = "text comment '配置YAML'")
    private String confyaml;
} 
package com.cw.entity;

import com.cw.utils.annotion.PropertyMsg;
import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 主单团队头部信息表
 */
@Data
@Entity
@Table(name = "colrs")
public class Colrs {

    @Column(nullable = true, name = "[cancel_date]", columnDefinition = " datetime  DEFAULT '1900-01-01 00:00:00' COMMENT '取消时间' ")
    LocalDateTime cancelDate;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Column(length = 20, nullable = true, name = "[hotelid]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '酒店id' ")
    private String hotelId = "";
    /**
     * 团队名称
     */
    @Column(name = "groupname", length = 50, columnDefinition = " varchar(50)  default '' comment '团队名' ")
    private String groupname;
    /**
     * 预订人姓名
     */
    @Column(length = 20, nullable = true, name = "[bookername]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '订房人姓名' ")
    private String bookerName;
    /**
     * 预订人联系方式
     */
    @Column(name = "telephone", columnDefinition = " varchar(11) default '' comment '联系电话' ")
    private String telephone = "";
    /**
     * 主订单号
     */
    @Column(name = "bookingid", length = 20, nullable = false, columnDefinition = " varchar(20)  default '' comment '主单号' ")
    private String bookingid;
    /**
     * 主订单号
     */
    @Column(name = "otano", length = 30, columnDefinition = " varchar(20)  default '' comment 'OTA订单号' ")
    private String otano;
    /**
     * 中台crs订单号 经过OTA 接口下单的会有个平台流水号.相当 于x  连流水号
     */
    @Column(name = "crsno", length = 30, columnDefinition = " varchar(20)  default '' comment '中台crs订单号' ")
    private String crsno;

    /**
     * 第三方与PMS交互的网络ID，由第三方提供
     */
    @Column(name = "network_id", length = 50, columnDefinition = " varchar(50)  default '' comment '网络ID' ")
    private String networkId;

    @Column(name = "channel", columnDefinition = " varchar(50) default '' comment '渠道来源' ")
    @PropertyMsg(value = "渠道", tansData = "channel")
    private String channel;
    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "create_time", updatable = false, columnDefinition = " datetime comment '创建时间'")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime = new Date();


    @Column(name = "arrival_date", nullable = true, columnDefinition = "  datetime comment '到店日期' ")
    @PropertyMsg(value = "到店日期")
    private Date arrivalDate;


    @Column(name = "departure_date", nullable = true, columnDefinition = "  datetime comment '离店日期' ")
    @PropertyMsg(value = "离店日期")
    private Date departureDate;

}

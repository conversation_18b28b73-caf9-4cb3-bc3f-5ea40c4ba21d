package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 门锁关联配置表
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/12/24 14:25
 **/
@Entity
@Table(name = "doorlock")
@Data
public class Doorlock implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(length = 10, nullable = false, name = "[id]")
    private Long id = 0L;
    @Column(name = "hotelid", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '酒店ID' ")
    private String hotelId;
    @Column(name = "vendor", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '厂商类型' ")
    private String vendor;
    @Column(name = "room_no", nullable = false, columnDefinition = " varchar(10) default '' comment '房间号' ")
    private String roomNo;
    @Column(name = "lockno", columnDefinition = " varchar(64)  comment '门锁编号' ")
    private String lockno;

}

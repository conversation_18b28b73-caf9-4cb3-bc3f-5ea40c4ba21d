package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import com.cw.utils.annotion.PropertyMsg;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;

@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "dailystat_res")
@Data
public class DailyStat_Res extends NaStatBaseEntity {

	/**
	 * 预定号
	 */
	@Column(name = "reservation_number", length = 30, columnDefinition = " varchar(30)  default '' comment '预定号' ")
	private String reservationNumber;

	@Column(name = "profile_number", length = 15, columnDefinition = " varchar(15)  default '' comment '档案号' ")
	private String profileNumber;

	@PropertyMsg(value = "预定状态", tansData = "RsStatus")
	@Column(name = "reservation_status", columnDefinition = " int(1) default 0 comment '预订状态:0->预订;1->在住;2->离店;3->应到未到;4->应离未离;-1->取消'")
	private Integer reservationStatus = 0;

	/**
	 * 收入汇总明细
	 */
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(length = 255, name = "[revenues]", columnDefinition = "json")
	private String revenues;

	/**
	 * 当前余额
	 */
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(length = 255, name = "[balances]", columnDefinition = "json")
	private String balances;

	/**
	 * 预定信息
	 */
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(length = 255, name = "[res_info]", columnDefinition = "json")
	private String resInfo;
}

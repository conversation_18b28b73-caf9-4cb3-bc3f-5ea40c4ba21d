package com.cw.entity;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 可卖房量
 * @Author: michael.pan
 * @Date: 2024/5/23 23:01
 */
@Entity
@Table(name = "rrooms")
@Data
public class Rrooms implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(length = 10, nullable = false, name = "[id]")
    private Long id = 0L;

    /**
     * 民宿代码
     */
    @Column(name = "hotelid", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '酒店ID' ")
    private String hotelId;
    /**
     * 房型
     */
    @Column(name = "room_type", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '房型代码' ")
    private String roomType;
    /**
     * 物理房间总数
     */
    @Column(name = "total", columnDefinition = " int(4)  comment  '总数' ")
    private int total;
    /**
     * 当日预订占用数
     */
    @Column(name = "pickup", columnDefinition = " int(4)  comment  '预订占用数' ")
    private int pickup;

    /**
     * 当日维修的房间数
     */
    @Column(name = "ooo", columnDefinition = " int(4)  comment  '维修房间数' ")
    private int ooo;

    /**
     * 日期
     */
    @Column(name = "datum", nullable = false, columnDefinition = "datetime comment '日期' ")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date datum;

}

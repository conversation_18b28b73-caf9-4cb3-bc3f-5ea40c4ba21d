package com.cw.entity;


import com.cw.utils.tool.EntityUtil;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@SuppressWarnings("serial")
@Data
@Entity
@Table(name = "Vendorconfig")
public class Vendorconfig implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "Vendorconfig")
    @Column(length = 10, nullable = false, name = "[sqlid]")
    private Long id = 0L;/* SQL主键 */

    @Column(length = 20, nullable = true, name = "[hotelid]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '项目id' ")
    private String hotelId = "";/* 项目id */

    @Column(length = 50, nullable = true, name = "[vtype]", columnDefinition = " varchar(50)  DEFAULT '' COMMENT '厂商类型.对应 vendortype 枚举' ")
    private String vtype = "";/* 厂商类型.对应 vendortype 枚举 */

    @Column(length = 100, nullable = true, name = "[appid]", columnDefinition = " varchar(100)  DEFAULT '' COMMENT '厂商提供的app帐号id/公众号/小程序appid' ")
    private String appid = "";/* 厂商提供的app帐号id/公众号/小程序appid */

    @Column(length = 100, nullable = true, name = "[appsecrect]", columnDefinition = " varchar(100)  DEFAULT '' COMMENT '厂商提供的密钥' ")
    private String appsecrect = "";/* 厂商提供的密钥 */

    @Lob
    @Basic(fetch = FetchType.LAZY)
    @Column(length = 150, nullable = true, name = "[url]")
    private String url = "";/* 厂商接口地址 */

    @Column(length = 100, nullable = true, name = "[userid]", columnDefinition = " varchar(100)  DEFAULT '' COMMENT '第三方系统用户名' ")
    private String userid = "";/* 第三方系统用户名 */

    @Column(length = 100, nullable = true, name = "[userpwd]", columnDefinition = " varchar(100)  DEFAULT '' COMMENT '第三方系统用户密码' ")
    private String userpwd = "";/* 第三方系统用户密码 */

    @Column(length = 100, nullable = true, name = "[outid]", columnDefinition = " varchar(100)  DEFAULT '' COMMENT '外部系统projectid' ")
    private String outid = "";/* 外部系统projectid */

    @Lob
    @Basic(fetch = FetchType.LAZY)
    @Column(length = 150, nullable = true, name = "[certkey]")
    private String certkey = "";/* 公钥证书内容 CERTIFICATE 部分内容  */

    @Lob
    @Basic(fetch = FetchType.LAZY)
    @Column(length = 150, nullable = true, name = "[privatekey]")
    private String privatekey = "";/* 私钥证书内容 PRIVATE KEY 部分内容 */

    @Lob
    @Basic(fetch = FetchType.LAZY)
    @Column(length = 150, nullable = true, name = "[notifyurl]")
    private String notifyurl = "";/* 接收回调通知地址 */

    @Column(length = 100, nullable = true, name = "[mchid]", columnDefinition = " varchar(100)  DEFAULT '' COMMENT '支付服务主商户号' ")
    private String mchid = "";/* 支付服务主商户号 */

    @Column(length = 100, nullable = true, name = "[mchkey]", columnDefinition = " varchar(100)  DEFAULT '' COMMENT '支付服务主密钥' ")
    private String mchkey = "";/* 支付服务主密钥 */

    @Column(length = 100, nullable = true, name = "[subappid]", columnDefinition = " varchar(100)  DEFAULT '' COMMENT '服务商模式子商户公众号ID' ")
    private String subappid = "";/* 服务商模式子商户公众号ID */

    @Column(length = 100, nullable = true, name = "[submchid]", columnDefinition = " varchar(100)  DEFAULT '' COMMENT ' 服务商模式子商户号' ")
    private String submchid = "";/*  服务商模式子商户号 */

    @Column(length = 100, nullable = true, name = "[wxpayv3key]", columnDefinition = " varchar(100)  DEFAULT '' COMMENT '  微信支付v3密钥' ")
    private String wxpayv3key = "";/*   微信支付v3密钥 */

    @Column(length = 60, nullable = true, name = "[description]", columnDefinition = " varchar(60)  DEFAULT '' COMMENT '配置文件描述' ")
    private String description = "";/* 配置文件描述 */

    @Column(name = "[active]", columnDefinition = " bit(1)  DEFAULT b'0' COMMENT '是否激活'")
    private Boolean active = false;/* 是否激活 */



}

package com.cw.entity;

import lombok.Data;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Classname BaseEntity
 * @Description 公共数据实体
 * @Date 2024-03-16 1:14
 * <AUTHOR> sancho.shen
 */
@Data
@MappedSuperclass
@EntityListeners(value = AuditingEntityListener.class)
public class BaseEntity implements Serializable {

    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "create_time", updatable = false, columnDefinition = " datetime comment '创建时间'")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime = new Date();

    /**
     * 最后修改时间
     */
    @LastModifiedDate
    @Column(name = "modified_time", columnDefinition = " datetime comment '修改时间'")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifiedTime = new Date();

    /**
     * 创建人
     */
    @CreatedBy
    @Column(name = "create_by", length = 30)
    private String createBy;

    /**
     * 最后修改人
     */
    @LastModifiedBy
    @Column(name = "modified_by", length = 30)
    private String modifiedBy;
}

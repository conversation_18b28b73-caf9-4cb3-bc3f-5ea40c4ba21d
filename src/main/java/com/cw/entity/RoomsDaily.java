package com.cw.entity;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单每日价格 存储为订单历史价格
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/6/11 16:55
 **/
@Entity
@Table(
        name = "Rooms_daily"
)
@Data
public class RoomsDaily {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(length = 10, nullable = false, name = "[id]")
    private Long id = 0L;

    /**
     * 民宿代码
     */
    @Column(name = "hotelid", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '酒店ID' ")
    private String hotelId;


    @Column(name = "reservation_number", unique = true, length = 30, nullable = false, columnDefinition = " varchar(10)  default '' comment '预定号' ")
    private String reservationNumber;


    /**
     * 日期
     */
    @Column(name = "datum", nullable = false, columnDefinition = "datetime comment '日期' ")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date datum;

    /**
     * 价格、保留两位小数
     */
    @Column(name = "price", nullable = false, columnDefinition = " decimal(10, 2) default 0  comment '房价、保留2位小数' ")
    private BigDecimal price = BigDecimal.ZERO;


    /**
     * 房价代码
     */
    @Column(name = "rate_code", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '房价代码' ")
    private String rateCode;

    /**
     * 是否固定价格
     */
    @Column(name = "[fixrate]", columnDefinition = " bit(1)  DEFAULT b'0' COMMENT '是否固定价格'")
    private Boolean fixrate = false;

    /**
     * 已入账时间
     */
    @Column(name = "posted_time", columnDefinition = "datetime comment '已入账时间' ")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date posted_time;

    //@Column(name = "include_code", nullable = false, columnDefinition = " varchar(200)  default '' comment '包价代码,多个以逗号分隔' ")
    //private String includeCode;

}

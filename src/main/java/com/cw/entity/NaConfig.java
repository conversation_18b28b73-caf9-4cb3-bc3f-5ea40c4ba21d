package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2024-03-20
 */
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "naconfig")
@Data
public class NaConfig {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(length = 10, nullable = false, name = "[id]")
    private Long id = 0L;

    @Column(length = 20, nullable = true, name = "[hotelid]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '项目id' ")
    private String hotelId = "";

    @Column(length = 20, nullable = true, name = "[runtime]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '每日运行时间' ")
    private String runtime = "";

    @Column(length = 20, nullable = true, name = "[crontime]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '每日运行时间转quartz时间表达式.转换后进行存储' ")
    private String cronTime = "";

    @Column(length = 500, nullable = true, name = "[progress]", columnDefinition = " varchar(500)  DEFAULT '' COMMENT '配置每日运行的步奏.任务列表.' ")
    private String progress = "";

    @Column(length = 20,nullable = true,name = "[serverip]",columnDefinition = " varchar(20)  DEFAULT '' COMMENT '运行的服务器IP.可以指定符号IP 要求的机器才能运行.' ")
    private String serverip = "";

}

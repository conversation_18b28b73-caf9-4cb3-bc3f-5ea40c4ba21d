package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.io.Serializable;

@SuppressWarnings("serial")
@EqualsAndHashCode(callSuper = false)
@Data
@Entity
@Table(name = "op_user")
public class OpUser implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(length = 10, nullable = false, name = "[id]")
    private Long id = 0L;


    @Column(length = 20, nullable = true, name = "[hotelid]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '酒店Id' ")
    private String hotelId = "";


    @Column(length = 30, nullable = true, name = "[userid]", columnDefinition = " varchar(30)  DEFAULT '' COMMENT '用户ID' ")
    private String userid = "";



    @Column(length = 30, nullable = true, name = "[username]", columnDefinition = " varchar(30)  DEFAULT '' COMMENT '用户姓名' ")
    private String username = "";

    @Column(length = 100, nullable = true, name = "[pwd]", columnDefinition = " varchar(100)  DEFAULT '' COMMENT '加密存储密码' ")
    private String pwd = "";

    @Column(length = 100, nullable = true, name = "[roleid]", columnDefinition = " varchar(100)  DEFAULT '' COMMENT '角色 ID,可关联多个,逗号分割' ")
    private String roleId = "";


    @Column(name = "[ostatus]", columnDefinition = " bit(1)  DEFAULT b'0' COMMENT '冻结状态'")
    private Boolean oStatus = false;

    @Column(name = "building_no", columnDefinition = " varchar(10)  comment '楼号' ")
    private String buildingNo = "";
}

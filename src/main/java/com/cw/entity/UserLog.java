package com.cw.entity;

import com.cw.utils.tool.EntityUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Describe 用户操作日期
 * <AUTHOR> <PERSON>
 * @Create on 2024-04-07
 */
@EqualsAndHashCode(callSuper = false)
@Data
@Entity
@Table(name = "User_log")
public class UserLog implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "User_log")
    @Column(length = 10, nullable = false, name = "[id]")
    private Long id = 0L;

    @Column(length = 20, nullable = true, name = "[hotelid]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '项目id' ")
    private String hotelId = "";

    @Column(length = 20, nullable = true, name = "[type]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '组' ")
    private String type = "";
    @Column(length = 10, nullable = true, name = "[optype]", columnDefinition = " varchar(10)  DEFAULT '' COMMENT '操作类型' ")
    private String opType = "";
    @Column(length = 60, nullable = true, name = "[regno]", columnDefinition = " varchar(60)  DEFAULT '' COMMENT '关联号,订单类型关联订单号,配置类关联对应代码' ")
    private String regNo = "";
    @Lob
    @Basic(fetch = FetchType.LAZY)
    @Column(length = 150, nullable = true, name = "[content]")
    private String content = "";
    @Column(nullable = true, name = "[date]", columnDefinition = " datetime  DEFAULT '1900-01-01 00:00:00' COMMENT '日期' ")
    private Date date = EntityUtil.stringToDate("1900-01-01");
    @Column(length = 20, nullable = true, name = "[userid]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '用户ID' ")
    private String userid = "";
}

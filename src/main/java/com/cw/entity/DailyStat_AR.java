package com.cw.entity;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Lob;
import javax.persistence.Table;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "dailystat_ar")
@Data
public class DailyStat_AR extends NaStatBaseEntity {

	/**
	 * 应收账户号
	 */
    @Column(name = "ar_no", length = 48, columnDefinition = " varchar(48)  default '' comment '应收账户号' ")
	private String ar_no;

	/**
	 * 当日收入汇总
	 */
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(length = 255, name = "[amounts_daily]", columnDefinition = "json comment '当日收入汇总'")
	private String amounts_daily;

	/**
	 * 累计收入汇总
	 */
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(length = 255, name = "[amounts_total]", columnDefinition = "json comment '累计收入汇总'")
	private String amounts_total;

	/**
	 * 消息
	 */
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(length = 255, name = "[messages]", columnDefinition = "json comment '消息'")
	private String messages;

}

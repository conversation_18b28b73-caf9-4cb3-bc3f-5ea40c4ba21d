package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * @Description: 市场实体
 * @Author: michael.pan
 * @Date: 2024/3/23 12:53
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "market")
public class Market extends BaseEntity implements Serializable {
    @Column(name = "hotelid", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '酒店ID' ")
    private String hotelId;

    @Column(name = "code", nullable = false, columnDefinition = " varchar(10)  default '' comment '代码' ")
    private String code;

    @Column(name = "description", columnDefinition = " varchar(24)  default '' comment '描述' ")
    private String description;
}

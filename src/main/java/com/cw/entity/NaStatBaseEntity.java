package com.cw.entity;

import lombok.Data;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

@Data
@MappedSuperclass
public class NaStatBaseEntity implements Serializable {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(length = 20, nullable = true, name = "[hotelid]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '项目id' ")
    private String hotelId = "";

    /**
     * 营业日期（帐应该累计到的日期）
     */
    @Column(name = "business_date", columnDefinition = " datetime  DEFAULT '1900-01-01 00:00:00' COMMENT '营业日期' ")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate business_date;

    /**
     * 统计年份
     */
    @Column(name = "business_year", length = 4, columnDefinition = "decimal(4,0)  DEFAULT '0' COMMENT '统计年份'")
    private int business_year;

    /**
     * 统计月份
     */
    @Column(name = "business_month", length = 2, columnDefinition = "decimal(2,0)  DEFAULT '0' COMMENT '统计月份'")
    private int business_month;

}

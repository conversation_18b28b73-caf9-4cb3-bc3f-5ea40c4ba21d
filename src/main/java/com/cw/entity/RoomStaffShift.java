package com.cw.entity;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/14
 **/
@Data
@Entity
@Table(name = "room_staff_shift")
public class RoomStaffShift {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "hotelid", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '酒店ID' ")
    private String hotelId;

    /**
     * 关联的客房员工 ID
     */
    @Column(name = "staff_id", nullable = false, columnDefinition = " varchar(20)  default '' comment '企微员工id'")
    private String staffId;

    /**
     * 关联的排班信息 ID
     */
    @Column(name = "shift_id", nullable = false, columnDefinition = " varchar(20)  default '' comment '班次id'")
    private String shiftId;

    @Column(name = "assignment_start_date",nullable = false, columnDefinition = "datetime comment '排班开始日期'")
    private Date assignmentStartDate;

    @Column(name = "assignment_end_date",nullable = false, columnDefinition = "datetime comment '排班结束日期'")
    private Date assignmentEndDate;

    @Column(name = "status", length = 20, columnDefinition = "varchar(20) default 'ACTIVE' comment '排班状态'")
    private String status = "ACTIVE";
}

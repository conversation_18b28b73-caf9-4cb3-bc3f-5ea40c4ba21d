package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;


/**
 *
 */
@EqualsAndHashCode(callSuper = false)
@Data
@Entity
@Table(name = "kitblacklist")
public class Kitblacklist implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "[id]", nullable = false)
    private Long id;

    @Column(length = 10, nullable = false, name = "[hotelid]", columnDefinition = "varchar(10) NOT NULL COMMENT '酒店ID'")
    private String hotelId;

    @Column(length = 32, nullable = false, name = "[kitcode]", columnDefinition = "varchar(32) NOT NULL COMMENT '关联productkit.kit_code'")
    private String kitcode;

    @Column(nullable = false, name = "[start_date]", columnDefinition = "date NOT NULL COMMENT '范围开始日期'")
    private Date startDate;

    @Column(nullable = false, name = "[end_date]", columnDefinition = "date NOT NULL COMMENT '范围结束日期'")
    private Date endDate;

    @Column(name = "[flag]", columnDefinition = "smallint COMMENT '星期标志 (1234567)'")
    private String flag;

} 
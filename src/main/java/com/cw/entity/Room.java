package com.cw.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * @Description: 房间表实体
 * @Author: michael.pan
 * @Date: 2024/3/20 21:26
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "room")
@Data
public class Room extends BaseEntity implements Serializable {
    @Column(name = "hotelid", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '酒店ID' ")
    private String hotelId;
    @Column(name = "room_type", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '房型代码' ")
    private String roomType;
    //@Column(name = "description", columnDefinition = " varchar(24)  default '' comment '房型描述' ")
    //private String description;
    @Column(name = "room_no", nullable = false, columnDefinition = " varchar(10) default '' comment '房间号' ")
    private String roomNo;
    @Column(name = "area", columnDefinition = " int(4)  comment  '面积' ")
    private int area;
    @Column(name = "characteristic", columnDefinition = " varchar(64)  comment '特性' ")
    private String characteristic;
    @Column(name = "room_status", columnDefinition = " varchar(4) default '' comment '房间状态（DI,CL,OO）' ")
    private String roomStatus;
    @Column(name = "start_time", columnDefinition = "varchar(12) comment '开始时间' ")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String startTime;
    @Column(name = "end_time", columnDefinition = "varchar(12) comment '结束时间' ")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String endTime;
    @Column(name = "locc", columnDefinition = "int(1) comment '占用标识(1占用，0空闲)' ")
    private int locc;


    @Column(name = "floorcode", length = 20, columnDefinition = " varchar(20)  default '' comment '楼层代码' ")
    private String floorcode;


    @Column(name = "suirooms", length = 100, columnDefinition = " varchar(100)  default '' comment '关联的物理房间号' ")
    private String suirooms;


}

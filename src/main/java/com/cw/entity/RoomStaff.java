package com.cw.entity;

import com.cw.utils.SystemUtil;
import io.swagger.annotations.ApiModelProperty;
import jodd.util.StringUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/5/12
 **/
@Data
@Table(name = "room_staff")
@Entity
public class RoomStaff implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "hotelid", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '酒店ID' ")
    private String hotelId;

    @Column(name = "name", length = 20, nullable = false, columnDefinition = " varchar(20)  default '' ")
    private String name;

    @Column(name = "gender", length = 1, nullable = false, columnDefinition = " int(1) comment '性别:0->女;1->男' ")
    private Integer gender = 1;

    @Column(name = "hire_date", nullable = false, columnDefinition = "datetime comment '入职日期' ")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date hireDate;

    @Column(name = "wx_user_id", nullable = false, columnDefinition = " varchar(20)  default '' comment '企微员工id'")
    private String wxUserId;

    @Column(name = "building", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '楼栋' ")
    private String building;

    @Column(name = "position_type", length = 10, nullable = false, columnDefinition = " varchar(10)  default '' comment '岗位类型' ")
    private String positionType;

    @Column(name = "telephone", length = 11, nullable = false, columnDefinition = " varchar(11)  default '' comment '手机号码' ")
    private String telephone;

    @Column(name = "status", length = 20, columnDefinition = "varchar(20) default 'ENABLE' comment '状态(禁用/开启)'")
    private String status = "ENABLE";


    public RoomStaff.Status toggle() {

        return StringUtil.equals(status,Status.ENABLE.name()) ? Status.DISABLE : Status.ENABLE;
    }

    public enum Status {
        ENABLE("启用"),
        DISABLE("禁用");

        private final String value;

        Status(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }
}

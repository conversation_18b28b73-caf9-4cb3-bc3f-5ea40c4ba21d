package com.cw.pojo.dto.pms.req.guest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/7/8
 **/
@Data
@Accessors(chain = true)
@ApiModel(value = "应收账户签名照请求对象")
public class ArSignatureReq {

    @NotBlank
    @ApiModelProperty(value = "原应收账号编号", example = "1", required = true)
    private String arNo;

    @NotBlank
    @ApiModelProperty(value = "签名照url，多个签名照url用,隔开")
    private String ossFileUrl;

}

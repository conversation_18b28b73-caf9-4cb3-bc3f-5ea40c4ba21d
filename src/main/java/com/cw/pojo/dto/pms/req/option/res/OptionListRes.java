package com.cw.pojo.dto.pms.req.option.res;

import com.cw.entity.Optionswitch;
import com.cw.entity.Saler;
import com.cw.pojo.dto.common.res.PageResponse;
import com.cw.pojo.dto.pms.req.others.res.SalesListRes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/7/11
 **/
public class OptionListRes extends PageResponse<OptionListRes.OptionListData, Optionswitch> {

    @Data
    @ApiModel(description = "酒店参数数据")
    public class OptionListData {
        @ApiModelProperty(value = "主键ID")
        private Long id;

        @ApiModelProperty(value = "开关组")
        private String group = "";

        @ApiModelProperty(value = "选项代码")
        private String option = "";

        @ApiModelProperty(value = "选项描述(中文)")
        private String desc = "";

        @ApiModelProperty(value = "是否启用")
        private Boolean switchstatus = true;

        @ApiModelProperty(value = "参数值")
        private String val = "";

        @ApiModelProperty(value = "排序序号")
        private Integer sortIndex;
    }
}

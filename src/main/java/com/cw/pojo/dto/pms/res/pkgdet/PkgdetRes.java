package com.cw.pojo.dto.pms.res.pkgdet;

import com.cw.pojo.dto.pms.res.BaseRes;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 包价详情响应实体
 * <AUTHOR>
 * @date 2024-07-30
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "包价详情对象")
public class PkgdetRes extends BaseRes implements Serializable {

    /**
     * 房价代码
     */
    @ApiModelProperty(value = "房价代码", example = "PKG001")
    private String ratecode;

    /**
     * 酒店ID
     */
    @ApiModelProperty(value = "酒店ID", example = "1001")
    private String hotelId;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endtime;

    /**
     * 房型代码
     */
    @ApiModelProperty(value = "房型代码", example = "STD")
    private String roomtype;

    /**
     * 包价代码
     */
    @ApiModelProperty(value = "包价代码，多个以逗号分隔", example = "3|BRF,1|WC,2|TK")
    private String includeCode;
}

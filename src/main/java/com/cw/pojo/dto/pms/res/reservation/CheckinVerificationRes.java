package com.cw.pojo.dto.pms.res.reservation;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/23
 **/
@Data
@Accessors(chain = true)
public class CheckinVerificationRes {

    @ApiModelProperty(value = "校验结果")
    List<CheckinVerificationsResult> checkinVerificationsResults = new ArrayList<>();

    @Data
    @Accessors(chain = true)
    public static class CheckinVerificationsResult {
        @ApiModelProperty(value = "预订号")
        private String reservationNumber = "";
        @ApiModelProperty(value = "入住校验是否成功")
        private Boolean result = false;
        @ApiModelProperty(value = "校验失败原因")
        private String failReason = "";
        @ApiModelProperty(value = "REMIND - 提醒，NOT_ALLOWED - 禁止操作")
        private VerificationStatus status;
        @ApiModelProperty(value = "校验状态描述")
        private String statusDesc;
    }

    public enum VerificationStatus {
        REMIND("提醒"), // 提醒
        NOT_ALLOWED("禁止操作"); // 不允许

        String desc;

        VerificationStatus(String desc) {
            this.desc = desc;
        }

        public String getDesc() {
            return desc;
        }
    }
}

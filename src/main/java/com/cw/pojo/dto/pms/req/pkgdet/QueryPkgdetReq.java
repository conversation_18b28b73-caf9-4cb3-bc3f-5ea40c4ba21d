package com.cw.pojo.dto.pms.req.pkgdet;

import com.cw.pojo.dto.common.req.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 查询包价详情信息实体
 * <AUTHOR>
 * @date 2024-07-30
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "查询包价详情信息请求参数")
public class QueryPkgdetReq extends PageReq implements Serializable {

    /**
     * 房价代码
     */
    @ApiModelProperty(value = "房价代码", example = "PKG001")
    private String ratecode;

    /**
     * 房型代码
     */
    @ApiModelProperty(value = "房型代码", example = "STD")
    private String roomtype;

    /**
     * 有效时间
     */
    @ApiModelProperty(value = "有效时间yyyy-MM-dd HH:mm:ss")
    private Date validTime;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间 yyyy-MM-dd", example = "2024-08-01")
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间 yyyy-MM-dd", example = "2024-08-31")
    private Date endTime;

    /**
     * 包价代码（模糊查询）
     */
    @ApiModelProperty(value = "包价代码", example = "BRF")
    private String includeCode;
}

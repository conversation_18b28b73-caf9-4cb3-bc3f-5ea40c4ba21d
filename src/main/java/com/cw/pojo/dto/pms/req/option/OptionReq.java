package com.cw.pojo.dto.pms.req.option;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/7/11
 **/
@Data
@Accessors(chain = true)
@ApiModel(value = "查询账项代码列表请求实体")
public class OptionReq {

    /**
     * 代码
     */
    @ApiModelProperty(value = "代码",  example = "66")
    private String option;

    /**
     * 收入组（001客房，002餐厅，003商场，004宴会，005其他）
     */
    @ApiModelProperty(value = "收入组（ORDER-预订相关，SERVICE-客房服务，RULES-规则参数，NOTIFY消息通知，005其他）", example = "ORDER")
    private String group;
}

package com.cw.pojo.dto.app.req.statistics;

import com.cw.pojo.dto.common.req.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/6/25 23:23
 * @Description 查询当日接待团队信息请求参数
 **/
@Data
@ApiModel("查询当日接待团队信息请求参数")
public class QueryStatStandardGroupReq extends PageReq implements Serializable {
    @ApiModelProperty(value = "团队名称", example = "团队名称")
    private String groupName;
    @ApiModelProperty(value = "联系人")
    private String contacter;
    @ApiModelProperty(value = "手机号码")
    private String telephone;
}

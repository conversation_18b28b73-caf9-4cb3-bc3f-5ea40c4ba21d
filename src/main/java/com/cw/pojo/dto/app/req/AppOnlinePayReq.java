package com.cw.pojo.dto.app.req;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("在线支付请求")
public class AppOnlinePayReq {

    @Size(min = 1)
    @ApiModelProperty(value = "支付订单号数组", required = true, example = "001")
    List<String> orderid;


    @ApiModelProperty(value = "交易金额")
    BigDecimal amount = BigDecimal.ZERO;

    @ApiModelProperty(value = "下单用户的openid,可选.一般用在微信环境", required = false, example = "001")
    String openid;


    @ApiModelProperty(value = "支付成功跳转路径", required = false, example = "xxx.com/orderid=123")
    String return_url;

    @ApiModelProperty(value = "支付场景值", required = true, example = "默认为0.其他值为针对具体业务的支付")
    Integer payscene = 0;


    @ApiModelProperty(value = "付款码支付时的数字", required = false, example = "AAAAAAXXXXXXXB")
    String qrAuthCode;//付款码支付时的数字

    @ApiModelProperty(value = "入账账项-为空不指定默认为房费")
    String deptCode;// 账项代码


    @ApiModelProperty(value = "细分的支付方式,微信付款码传8,支付宝付款码传9", example = "8")
    Integer queryPayMethod = 8;

//    @NotBlank
//    @ApiModelProperty(value = "操作指令",notes = "0:取消,1:查看订单详情,2:取消预检查(是否含扣款)" ,
//            required = true, example = "1")
//    String op;

    public String getBillingReg() {
        if (CollectionUtil.isNotEmpty(orderid)) {
            return orderid.get(0);
        } else {
            return StrUtil.EMPTY;
        }
    }


}

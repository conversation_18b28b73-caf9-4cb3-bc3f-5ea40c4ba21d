package com.cw.pojo.dto.app.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("H5应用端订单支付查询操作请求")
public class AppOrderPayQueryReq {

    @NotBlank
    @ApiModelProperty(value = "支付单号", required = true, example = "001")
    String outTradeNo;

    @ApiModelProperty(value = "付款方式/环境 微信内:1 微信H5:7.PC端支付宝:3 支付宝H5 :5", required = true, example = "")
    Integer payMode = 1;

    @ApiModelProperty(value = "校验时间戳")
    private String timestamp = "";

    @ApiModelProperty(value = "校验签名")
    private String sign = "";

    @ApiModelProperty(value = "订单支付场景 0普通订单 1预约订单 2线下订单 ", example = "0")
    private Integer payscene = 0;


}

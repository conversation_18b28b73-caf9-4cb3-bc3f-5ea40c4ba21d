package com.cw.pojo.dto.app.req.statistics;

import com.cw.pojo.dto.common.req.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/6/25 20:34
 * @Description 查询当日房间管理信息请求参数
 **/
@Data
@ApiModel("查询当日脏房请求参数")
public class QueryStatRoomReq extends PageReq implements Serializable {
    @ApiModelProperty(value = "房态")
    private String roomStatus;

    @ApiModelProperty(value = "房型", example = "高级大床房")
    private String description = "";

    @ApiModelProperty(value = "房间号")
    private String roomNo = "";
}

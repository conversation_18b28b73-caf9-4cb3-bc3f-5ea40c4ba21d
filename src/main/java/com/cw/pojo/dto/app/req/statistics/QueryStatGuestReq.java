package com.cw.pojo.dto.app.req.statistics;

import com.cw.pojo.dto.common.req.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/6/19 15:23
 * @Description
 **/
@Data
@ApiModel(description = "查询当日接待客人信息请求参数")
public class QueryStatGuestReq extends PageReq implements Serializable {
    @ApiModelProperty(value = "客户姓名")
    private String guestName;

    @ApiModelProperty(value = "房型", example = "高级大床房")
    private String description = "";

    @ApiModelProperty(value = "房间号")
    private String roomNumber = "";

    @ApiModelProperty(value = "来源")
    private String channel;
}

package com.cw.pojo.dto.ota.common;

import lombok.Data;

import java.math.BigDecimal;

/**
 * OTA入住订单信息
 *
 * <AUTHOR>
 */
@Data
public class OtaCheckinOrderInfo {

    /**
     * 订单编号
     */
    private String otano;

    /**
     * 入住日期
     */
    private String checkInDate;

    /**
     * 离店日期
     */
    private String checkOutDate;

    /**
     * 客人姓名
     */
    private String guestName;

    /**
     * 手机号码
     */
    private String phoneNumber;

    /**
     * 房间数量
     */
    private Integer rooms;

    /**
     * 房间号码
     */
    private String roomNo;

    /**
     * 房型描述
     */
    private String roomTypeDesc;

    /**
     * 订单价格
     */
    private BigDecimal orderPrice;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 是否可以入住
     */
    private boolean lcancheckin = false;

    /**
     * 预订类型（携程/预抵等）
     */
    private String channel;
}

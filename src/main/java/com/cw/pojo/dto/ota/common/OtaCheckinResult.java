package com.cw.pojo.dto.ota.common;

import lombok.Data;

import java.util.List;

/**
 * OTA入住结果
 *
 * <AUTHOR>
 */
@Data
public class OtaCheckinResult {

    /**
     * 预订房间数量
     */
    private Integer roomCount;

    /**
     * 联系方式
     */
    private String contactPhone;

    /**
     * 房间信息列表
     */
    private List<OtaRoomInfo> rooms;

    /**
     * 入住人员信息列表
     */
    private List<OtaGuestResult> guests;

    /**
     * 房间信息
     */
    @Data
    public static class OtaRoomInfo {
        /**
         * 房间号
         */
        private String roomNo;

        /**
         * 房锁密码
         */
        private String roomPassword;

        /**
         * 房型描述
         */
        private String roomTypeDesc;
    }

    /**
     * 入住人员信息
     */
    @Data
    public static class OtaGuestResult {
        /**
         * 入住人姓名
         */
        private String guestName;

        /**
         * 身份证号
         */
        private String idNumber;

        /**
         * 入住状态
         */
        private String checkInStatus;
    }
}

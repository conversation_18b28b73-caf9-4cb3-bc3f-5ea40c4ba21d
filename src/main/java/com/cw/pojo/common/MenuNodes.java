package com.cw.pojo.common;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 *
 * 前端菜单分页数据
 *
 */
@Data
@Schema(description = "菜单节点", example = "报表")
public class MenuNodes {
    @Schema(description = "菜单名称", example = "客房预定")
    private String label; //菜单名称.权限管理时显示的名称
    @Schema(description = "子菜单列表", example = "成功")
    private List<MenuNodes> childs;// = new ArrayList<>();
    @Schema(description = "权限代码", example = "1001")
    private String rightcode;//对应的显示这菜单的权限


    public MenuNodes(String label, String rightcode) {
        this.setLabel(label);
        this.setRightcode(rightcode);
    }


    public MenuNodes addMenu(MenuNodes newMenu) {
        if (this.childs == null) {
            this.childs = new java.util.ArrayList<>();
        }
        this.childs.add(newMenu);
        return this;
    }


    public MenuNodes addChildren(List<MenuNodes> nodes) {
        if (this.childs == null) {
            this.childs = new java.util.ArrayList<>();
        }
        this.childs.addAll(nodes);
        return this;
    }

}

package com.cw.pojo.common;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 *   前端表格分页数据
 */
@Data
public class PageData {
    private List data;
    private int pagesize = 0;
    private int totalpage = 0;
    private int total = 0;
    private int currentpage = 0;
    String countSql = "";
    Map<String,Object> countParam;
    String sql = "";
    Map<String,Object> param;
}

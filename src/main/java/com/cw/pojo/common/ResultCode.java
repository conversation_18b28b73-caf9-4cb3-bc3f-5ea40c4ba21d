package com.cw.pojo.common;


/**
 * 定义返回给前端的响应代码
 */
public enum ResultCode {
    /*
    请求返回状态码和说明信息
     */
    SUCCESS(200, "成功"),

    //后端定义的业务处理异常..通常这些错误需要做机器人报警:
    BAD_REQUEST(400, "提交参数不对"),
    UNAUTHORIZED(401, "认证失败"),
    LOGIN_ERROR(402, "登陆失败，用户名或密码无效"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "请求的资源不存在"),
    TIME_OUT(408, "请求超时"),
    IP_FAIL(409, "IP受限登录"),
    SERVER_ERROR(500, "服务器内部错误"),


    SYSERR(999, "系统错误"),
    PMSERR(998, "子系统错误"),
    PARAMERR(900, "提交参数错误"),
    RETRY_ORDERERR(800, "下单错误.可重试"),
    OVERBOOK_ERR(120, "库存不足,超预订"),
    UPDATEORDER_ERR(121, "订单状态不可修改"),
    AUDITING_ERR(103, "订单审核中"),
    CANCELOVERTIME_ERR(104, "超过最晚取消时间"),
    PAYFAIL(105, "支付失败"),
    FORMERR(115, "用户表单提交错误");

    //其他业务异常错误:
    ;
    private int code;
    private String msg;

    ResultCode(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int code() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

}

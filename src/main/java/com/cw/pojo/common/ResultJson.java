package com.cw.pojo.common;


import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
public class ResultJson<T> implements Serializable {

    private static final long serialVersionUID = 783015033603078674L;
    @Schema(description = "响应代码 200-成功 其它-失败", example = "200")
    private int code;
    @Schema(description = "响应描述", example = "成功")
    private String msg; //结果描述字符串.如果不指定的话.就是 resultcode 的描述
    @Schema(description = "返回数据")
    private T data;  //一般是返回的表单对象


    public ResultJson(ResultCode resultCode) {
        setResultCode(resultCode);
    }

//    public static <T> ResultJson ok(T o) {
//        return new ResultJson(ResultCode.SUCCESS,"",null);
//    }

    public ResultJson(ResultCode resultCode, String msg) {
        this(resultCode, msg, null);
    }

//    public static <T> ResultJson failure(ResultCode code, T o) {
//        return new ResultJson(code,"",null);
//    }

    public ResultJson(ResultCode resultCode, String msg, T data) {
        setResultCode(resultCode);
        if (StrUtil.isNotEmpty(msg)) {
            this.msg = msg;
        }
        this.data = data;
    }

    public static ResultJson ok() {
        return builder().resultCode(ResultCode.SUCCESS);
    }

    public static ResultJson failure(ResultCode code) {
        return builder().resultCode(code);// failure(code, null);
    }

    public static ResultJson builder() {
        return new ResultJson(ResultCode.SUCCESS);//默认创建的是成功对象
    }

    private void setResultCode(ResultCode resultCode) {
        this.code = resultCode.code();
        this.msg = resultCode.getMsg();
    }

    public ResultJson resultCode(ResultCode resultCode) {
        setResultCode(resultCode);
        return this;
    }

    public ResultJson msg(String msg) {
        this.msg = msg;
        return this;
    }

    public ResultJson data(T t) {
        this.data = t;
        return this;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this, SerializerFeature.SortField);
    }

}

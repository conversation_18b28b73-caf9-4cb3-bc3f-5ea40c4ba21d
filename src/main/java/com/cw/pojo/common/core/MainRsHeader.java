package com.cw.pojo.common.core;

import com.cw.entity.Colrs;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Descripstion 团队头.后续拿来做综合预订的头也没问题
 * @Create 2024/6/20 11:55
 **/
@Data
@ApiModel(description = "主单信息")
public class MainRsHeader {

    @ApiModelProperty(value = "团队名称")
    String groupName;  //团队名称

    @ApiModelProperty(value = "电话")
    String tel;

    @ApiModelProperty(value = "预订人姓名")
    String bookerName;// 预订人姓名

    @ApiModelProperty(value = "OTA来源订单号")
    String otano;//OTA 来源订单号

    @ApiModelProperty(value = "预订平台订单号")
    String crsno; //预订平台订单号

    @ApiModelProperty(value = "预订系统订单号")
    String bookingid; //预订系统订单号

    @ApiModelProperty(value = "主档案号")
    String profileNo;  //主档案号

    public void fillColRsInfo(Colrs colrs) {
        this.groupName = colrs.getGroupname();
        this.tel = colrs.getTelephone();
        this.bookerName = colrs.getBookerName();
        this.otano = colrs.getOtano();
        this.crsno = colrs.getCrsno();
        this.bookingid = colrs.getBookingid();
    }


}

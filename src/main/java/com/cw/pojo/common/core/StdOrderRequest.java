package com.cw.pojo.common.core;

import com.cw.entity.Colrs;
import com.cw.entity.Profile;
import com.cw.entity.Reservation;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/6/17 15:22
 **/
public interface StdOrderRequest {


    Colrs getColrs();

    List<Reservation> getRoomRss();

    //方便后面拓展.除了客房预订之外.还有其他的产品需要一起预订. 给景区多产品预订留个口

    Profile getMainProfile();

    String getHotelId();
}

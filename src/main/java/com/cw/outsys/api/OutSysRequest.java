package com.cw.outsys.api;

import java.util.Map;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/9/3 13:22
 **/
public interface OutSysRequest<T extends OutSysResponse> {
    String getApiMethodName();

    String getVersion();

    Class<T> getResponseClass();


    OutSysApi getOutApiAnotion();

    Map<String, String> getGetRequestParams(); //如果是GET请求.就组装返回这个

    default String logid() {
        return "";
    }

    default String groupid() {
        return "";
    }


    default String getData() {
        return "";
    }//获取请求数据

}

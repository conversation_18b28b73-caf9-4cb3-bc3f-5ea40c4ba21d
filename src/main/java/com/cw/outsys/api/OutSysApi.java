package com.cw.outsys.api;

import com.cw.outsys.base.SysStdRequest;
import org.springframework.http.HttpMethod;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/9/6 11:25
 **/
@Target(ElementType.TYPE)  //应用于接口,类.枚举.注解
@Retention(RetentionPolicy.RUNTIME)  //反射的时候能够获取
@Documented              //允许 JAVA DOC 生成描述
@Inherited                    //允许子类继承使用
public @interface OutSysApi {
    String description() default ""; //中景定义的方法描述.比如登陆方法

    String methodName() default ""; //类似taoabao .crm的风格.地址用一个.方法名不同

    String basepath() default ""; //基础路径

    String path() default ""; //请求资源的路径  cambrige的是接口地址加方法后缀

    String version() default ""; //方法版本

    HttpMethod reqMethod() default HttpMethod.POST;

    Class<? extends SysStdRequest> stdCrsClass() default SysStdRequest.class;


}

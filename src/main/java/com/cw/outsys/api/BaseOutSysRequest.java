package com.cw.outsys.api;

import com.alibaba.fastjson.annotation.JSONField;
import com.cw.entity.Vendorconfig;
import com.cw.outsys.base.SysStdRequest;
import org.springframework.http.HttpMethod;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/9/6 11:51
 **/
public abstract class BaseOutSysRequest<T extends OutSysResponse, M extends SysStdRequest> extends Object implements OutSysRequest<T> {
    //    protected Long consumeTime;
    @JSONField(serialize = false)
    protected Map<String, String> headerMap;

    @JSONField(serialize = false)
    protected OutSysApi outApiAnotion;

    @JSONField(serialize = false)
    protected Map<String, String> getRequestParams;

    @JSONField(serialize = false)
    protected String pmsMethodUrl;

    @JSONField(serialize = false)
    protected Class<T> responseClass;

    @JSONField(serialize = false)
    protected String data;

    @JSONField(serialize = false)
    protected Vendorconfig vendorconfig;  //提供注入.给填充时使用

    public BaseOutSysRequest setVendor(Vendorconfig vendorconfig) {
        this.vendorconfig = vendorconfig;
        return this;
    }

    /**
     * 将标准参数转换成各个系统 接口的请求参数
     * 根据类实例化的时候反射调用
     *
     * @param m 内部标准请求
     * @return 一般是返回自己
     */
    public abstract OutSysRequest<T> transfer(M m);

    public Map<String, String> getHeaderMap() {
        if (this.headerMap == null) {
            this.headerMap = new HashMap();
        }
        return this.headerMap;
    }


    public void setHeaderMap(Map<String, String> headerMap) {
        this.headerMap = headerMap;
    }

    public void putHeaderParam(String key, String value) {
        getHeaderMap().put(key, value);
    }

    @Override
    @JSONField(serialize = false)
    public String getApiMethodName() {
        return this.getClass().getAnnotation(OutSysApi.class).methodName();
    }

    @JSONField(serialize = false)
    @Override
    public String getVersion() {
        return this.getClass().getAnnotation(OutSysApi.class).version();
    }

    @Override
    public OutSysApi getOutApiAnotion() {
        return this.getClass().getAnnotation(OutSysApi.class);
    }

    public String getPmsMethodUrl() {
        OutSysApi api = getOutApiAnotion();
        return api.basepath() + api.path();
    }

    /**
     * 返回GET请求的键值对.反射遍厉所有字段.不为NULL 就加入.
     *
     * @return
     */
    @Override
    public Map<String, String> getGetRequestParams() {
        Map<String, String> reqparams = new HashMap<>();
        if (getOutApiAnotion().reqMethod().equals(HttpMethod.GET)) {
            Field[] fields = this.getClass().getDeclaredFields();
            for (Field field : fields) {
                try {
                    field.setAccessible(true);
                    Object o = field.get(this);
                    if (o != null) {
                        reqparams.put(field.getName(), o.toString());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return reqparams;
    }

    @Override
    public String getData() {
        return this.data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public Vendorconfig getVendorconfig() {
        return vendorconfig;
    }

    public void setVendorconfig(Vendorconfig vendorconfig) {
        this.vendorconfig = vendorconfig;
    }
}

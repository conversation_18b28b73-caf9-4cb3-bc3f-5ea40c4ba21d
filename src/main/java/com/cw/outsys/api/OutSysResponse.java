package com.cw.outsys.api;

import com.alibaba.fastjson.JSON;
import com.cw.outsys.base.SysStdResponse;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/9/3 13:22
 **/
@Data
public abstract class OutSysResponse<T extends SysStdResponse> {
    private String body;
    private Map<String, String> header;
    private String requestUrl;
    private Map<String, String> params;

    private String code = "100";    //内部的结果代码
    private String message;//内部的结果描述
    private String request_id;  //请求 ID. 方便以后拓展做追踪
    private String sub_code;  //外部系统的错误代码
    private String sub_msg;//外部系统 的错误描述

    /**
     * 实现将自己转成标准响应对象的方法
     *
     * @param <T>
     * @return
     */
    public abstract T getSysStdResponse();

    /**
     * 定义请求的 http body 应该如何转成自己.默认行为是直接 json 串转对象
     * 但有的复杂结果直接转是不行的.比如结果是个 base64串.或者是个数组.那就要各个对象自己解析
     * 重载转换方法
     *
     * @param body
     * @return
     */
    public OutSysResponse body2Instance(String body) {
        return JSON.parseObject(body, this.getClass()); //
    }


}

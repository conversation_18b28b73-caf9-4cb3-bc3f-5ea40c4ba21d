package com.cw.outsys.client;


import com.cw.exception.DefinedException;
import com.cw.outsys.api.OutSysRequest;
import com.cw.outsys.api.OutSysResponse;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/9/6 10:12
 **/
public interface OutSysClient {
    <T extends OutSysResponse> T execute(OutSysRequest<T> paramOutSysRequest) throws DefinedException;

    default <T extends OutSysResponse> List<T> executeArray(OutSysRequest<T> paramOutSysRequest) throws DefinedException {
        return Lists.newArrayList();
    }

    default void refreshTokenAndLogin() throws DefinedException {
    }
}

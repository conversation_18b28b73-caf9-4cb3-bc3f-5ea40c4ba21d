package com.cw.outsys.client;

import cn.hutool.core.util.ReflectUtil;
import com.cw.outsys.api.OutSysResponse;

/**
 * 处理请求结果..Body 到 pmsresponse 的转换
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2020/4/12 15:02
 **/
//@Component
public class OutSysResponseConverter {
    private static final String method = "body2Instance"; //响应结果中实现的 string 转 响应对象方法.


    public static OutSysResponse convertBody(String body, Class<? extends OutSysResponse> repsonse) {
        return ReflectUtil.invoke(ReflectUtil.newInstance(repsonse), method, body);
    }

}

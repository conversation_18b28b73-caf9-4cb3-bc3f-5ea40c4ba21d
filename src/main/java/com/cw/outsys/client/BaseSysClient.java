package com.cw.outsys.client;


import com.cw.entity.Vendorconfig;
import com.cw.utils.SpringUtil;
import org.redisson.api.RedissonClient;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/9/6 10:12
 **/
public abstract class BaseSysClient implements OutSysClient {
    protected Vendorconfig config;
    protected RestTemplate restTemplate;

    public Vendorconfig getConfig() {
        return config;
    }

    public void setConfig(Vendorconfig config) {
        this.config = config;
    }

    public RestTemplate getRestTemplate() {
        return SpringUtil.getBean(RestTemplate.class);
    }

    public RedissonClient getRedissonClient() {
        return SpringUtil.getBean(RedissonClient.class);
    }


    /**
     * 注入config 之后的初始化事件
     */
    public void init() {

    }

    /**
     * 修改config 之后 .执行刷新client
     */
    public void refreshClient() {

    }
}

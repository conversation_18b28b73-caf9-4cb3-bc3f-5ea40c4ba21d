package com.cw.outsys.client.impl.sms;

import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cw.config.exception.CustomException;
import com.cw.exception.DefinedException;
import com.cw.outsys.api.OutSysRequest;
import com.cw.outsys.api.OutSysResponse;
import com.cw.outsys.client.BaseSysClient;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

/**
 * @Describe 短信发送请求
 * <AUTHOR> <PERSON>
 * @Create on 2022-03-28
 */
@Slf4j
public class SmsClient extends BaseSysClient {
    public static final String OKRESULT = "success";
    public static final String MESSAGERESULT = "message";
    private static final String APPID = "appkey";
    private static final String SIGN = "sign";
    private static final String TIMESTAMP = "timestamp";
    private static final String dateformat = "yyyy-MM-dd";
    private HttpHeaders headers;


    @Override
    public <T extends OutSysResponse> T execute(OutSysRequest<T> paramOutSysRequest) throws DefinedException {
        String url = config.getUrl() + paramOutSysRequest.getOutApiAnotion().path(); //拼接地址.得到URL
        ResponseEntity<String> responseEntity = null;
        String body = JSON.toJSONString(paramOutSysRequest);
        HttpHeaders headers = getPostHeader(body);
        //System.out.println("Body:" + body);
        //System.out.println("Header:" + headers.toString());
        HttpEntity<String> httpEntity = new HttpEntity<>(body, headers);
        try {
            responseEntity = getRestTemplate().exchange(url, paramOutSysRequest.getOutApiAnotion().reqMethod(),
                    httpEntity, String.class);
        } catch (Exception e) {
            log.error("错误短信内容：{}", httpEntity);
            throw e;
        }
        String bodyStr = responseEntity.getBody();
        //判断body 返回success
        JSONObject jsonObject = null;
        if (StringUtils.isNotBlank(bodyStr)) {
            jsonObject = JSON.parseObject(bodyStr);
        }
        if (jsonObject != null) {
            if (jsonObject.getBooleanValue(OKRESULT)) {
                return JSON.parseObject(bodyStr, paramOutSysRequest.getResponseClass());
            } else {
                throw new CustomException(ResultJson.failure(ResultCode.SERVER_ERROR).msg(jsonObject.getString(MESSAGERESULT)));
            }
        } else {
            throw new CustomException(ResultJson.failure(ResultCode.NOT_FOUND).msg("请求资源不存在"));
        }

    }

    private HttpHeaders getPostHeader(String body) {
        headers = new HttpHeaders();
        MediaType type = MediaType.parseMediaType("application/json");
        headers.setContentType(type);
        headers.add(APPID, config.getAppid());
        long timestamp = System.currentTimeMillis();
        headers.add(TIMESTAMP, timestamp + "");
        String sign = body + timestamp + config.getAppsecrect();
        headers.add(SIGN, SecureUtil.md5(sign));
        return headers;
    }
}

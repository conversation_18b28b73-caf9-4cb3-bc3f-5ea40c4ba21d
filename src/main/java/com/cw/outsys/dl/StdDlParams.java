package com.cw.outsys.dl;

import lombok.Data;

/**
 * 标准门锁操作参数
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2025/1/3 15:30
 **/
@Data
public class StdDlParams {

    /**
     * 项目ID（酒店ID）
     */
    String projectId;

    /**
     * 房间号
     */
    String roomNo;

    /**
     * 门锁编号
     */
    String lockId;

    /**
     * 密码类型
     * 1-单次密码，2-永久密码，3-限期密码，4-删除密码
     */
    Integer passwordType = 3;

    /**
     * 密码名称
     */
    String passwordName;

    /**
     * 有效期开始时间（时间戳，单位毫秒）
     */
    Long startDate;

    /**
     * 有效期结束时间（时间戳，单位毫秒）
     */
    Long endDate;

    /**
     * 密码ID（用于删除密码）
     */
    String passwordId;

    /**
     * 搜索关键字（用于查询密码列表）
     */
    String searchStr;

    /**
     * 页码（用于查询密码列表）
     */
    Integer pageNo = 1;

    /**
     * 页大小（用于查询密码列表）
     */
    Integer pageSize = 100;

    /**
     * 排序方式（用于查询密码列表）
     */
    Integer orderBy = 1;
}

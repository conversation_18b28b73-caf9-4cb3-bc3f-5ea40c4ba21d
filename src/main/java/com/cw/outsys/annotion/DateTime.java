package com.cw.outsys.annotion;


import com.cw.utils.DateTimeValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * String类型日期格式校验
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = DateTimeValidator.class)
public @interface DateTime {

    String message() default "日期格式错误";

    String format() default "hh:ss ~ hh:ss";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}

package com.cw.outsys.base;

import cn.hutool.core.util.StrUtil;
import lombok.Data;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/9/30 10:34
 **/
@Data
public class SysStdResponse {
    //加了个std .是为了防止跟实际对象有冲突.
    final String successcode = "100";
    final String failcode = "";
    private String std_code = "100";    //内部的结果代码 100表示成功
    private String std_message;//内部的结果描述
    private Boolean std_flag = true;  //处理标志.是否成功??
    private String std_request_id;  //请求 ID. 方便以后拓展做追踪
    private String std_sub_code;  //外部系统的错误代码
    private String std_sub_msg;//外部系统 的错误描述
    private String std_data; //请求结果BODY
//    private Boolean stdresult;

    public static String getError(SysStdResponse response) {
        if (response == null) {
            return "no repsonse";
        }
        return StrUtil.format("subcode:{} suberror:{} ",
                response.getStd_sub_code(), response.getStd_sub_msg());
    }

    public boolean lOk() {
        return std_flag != null && std_flag;
    }


}

package com.cw.outsys.pay;

import lombok.Data;

/**
 * @Description: 标准预授权撤销参数
 * @Author: Gemini AI
 * @Date: 2024-05-21
 */
@Data
public class StdPreAuthCancelParams {

    /**
     * 系统商户号 (酒店ID)
     */
    private String hotelId;

    /**
     * 原始预授权的交易号 (我方系统的 transactionId)
     */
    private String transactionId;

    /**
     * 原始预授权在支付网关的交易号 (银行流水号)
     */
    private String gatewayTransactionId;

    /**
     * 附加信息，用于在回调时透传
     */
    private String attach;
} 
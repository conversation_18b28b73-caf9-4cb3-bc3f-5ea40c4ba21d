package com.cw.outsys.pay;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 统一退款参数
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2022/2/15 11:21
 **/
@Data
public class StdRefundParams {
    BigDecimal orgAmount;//原支付总金额
    BigDecimal refund;//退款金额

    String attachBookingId;
    String attachUid;
    String attachProjectId;
    BigDecimal attachRefundAmount;

    String transactionId;//原支付单号
    String outTradeNo;//原商户订单号
    String outRefundNo; //本地退款流水号
    String notifyDomain;
    Integer payscene = 0;//支付场景值.0为普通订单 .对应PAYUTIL中的常量


    public StdRefundParams() {

    }

    public StdRefundParams(String bookingId, String uid,
                           String projectId, String localPayId, String outTradeNo,
                           BigDecimal orgAmount, BigDecimal refund,
                           String notifyDomain, String outRefundNo) {
        attachBookingId = bookingId;
        attachUid = uid;
        attachProjectId = projectId;
        attachRefundAmount = refund;

        orgAmount = orgAmount;
        refund = refund;

        transactionId = localPayId;
        outTradeNo = outTradeNo;
        this.notifyDomain = notifyDomain;
        this.outRefundNo = outRefundNo;


    }


}

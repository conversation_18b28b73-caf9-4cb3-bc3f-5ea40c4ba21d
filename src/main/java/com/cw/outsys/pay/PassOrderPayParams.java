package com.cw.outsys.pay;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2022/4/13 14:46
 **/
@Data
public class PassOrderPayParams {
    int paymode;

    String projectId;
    String outTradeNo;  //本地生成支付单号 单个订单支付时等幂
    String orderDesc; //商品描述.注意.不同厂商的要求长度会不一样.各自截取

    String payerId;//支付环境中的支付用户 id  微信小程序内则是 openid

    String bookingid;//订单号
    String outid;//外部订单号
    String memo;//客人订单备注
    String ptype;//购买产品类型.暂时为空
    String tel;//预订电话
    String guestname;//客人姓名
    LocalDateTime createdate;
    BigDecimal amount;//订单支付金额

    Long expireTime; //支付关闭交易时间
    String notifyDomain; //支付回调地址
    String return_url;//支付返回地址
    Integer onlinePayMethod;//对应 OnlinePaymethod


}

package com.cw.outsys.pay.config;

import lombok.Data;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2022/2/15 22:42
 **/
@Data
public class AliPayConfig {
    public static final String signType = "RSA2";//常量
    public static final String charSet = "UTF-8";//常量
    public static String serverUrl = "https://openapi.alipay.com/gateway.do";//常量
    private String appId;
    private String privateKey;
    private String publicKey;
    private String appCertPath;
    private String aliPayRootCertPath;
    private String aliPayPublicCertPath;
    private String aliPayPublicCertContent;
    private String domain;
}

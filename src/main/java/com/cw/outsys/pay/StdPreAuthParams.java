package com.cw.outsys.pay;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description: 标准预授权发起参数
 * @Author: Just
 * @Date: 2025-06-27
 */
@Data
public class StdPreAuthParams {

    /**
     * 系统商户号 (酒店ID)
     */
    private String projectId;

    /**
     * 关联的预定号
     */
    private String reservationNumber;

    /**
     * 本次预授权的唯一交易号 (由我方系统生成)
     */
    private String transactionId;

    /**
     * 预授权金额
     */
    private BigDecimal amount;

    /**
     * 支付方式 (e.g., "CreditCard", "Alipay")
     */
    private String paymentMethod;

    /**
     * 支付场景，用于区分不同业务，可为空
     */
    private String payScene;

    /**
     * 授权码 (例如，当面付时的付款码)
     */
    private String authCode;

    /**
     * 附加信息，用于在回调时透传
     */
    private String attach;
} 
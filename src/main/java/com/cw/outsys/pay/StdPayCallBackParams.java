package com.cw.outsys.pay;

import java.math.BigDecimal;

/**
 * 处理支付成功回调消息
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2022/2/15 11:21
 **/
public class StdPayCallBackParams {

    String appid;
    String projectId;
    String outTradeNo;  //本地生成支付单号 单个订单支付时等幂
    String orderDesc; //商品描述.注意.不同厂商的要求长度会不一样.各自截取
    BigDecimal totalPay;
    String payerId;//支付环境中的支付用户 id  微信小程序内则是 openid
    String expireTime; //支付关闭交易时间
    String notifyUrl; //支付回调的 URL

}

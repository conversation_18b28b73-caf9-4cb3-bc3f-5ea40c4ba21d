package com.cw.outsys.pay;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description: 标准预授权完成参数
 * @Author: Just
 * @Date: 2025-06-27
 */
@Data
public class StdPreAuthCompleteParams {

    /**
     * 系统商户号 (酒店ID)
     */
    private String projectId;

    /**
     * 原始预授权的交易号 (我方系统的 transactionId)
     */
    private String transactionId;

    /**
     * 原始预授权在支付网关的交易号 (银行流水号)
     */
    private String gatewayTransactionId;

    /**
     * 本次需要完成(扣款)的金额
     */
    private BigDecimal amount;

    /**
     * 附加信息，用于在回调时透传
     */
    private String attach;
} 
package com.cw.outsys.pay;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 统一下单参数
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2022/2/15 11:21
 **/
@Data
public class StdPayParams {

    String projectId;
    List<String> orderids; //支付回调时.需要处理的订单号
    String outTradeNo;  //本地生成支付单号 单个订单支付时等幂
    String orderDesc; //商品描述.注意.不同厂商的要求长度会不一样.各自截取
    BigDecimal totalPay;
    String payerId;//支付环境中的支付用户 id  微信小程序内则是 openid
    Long expireTime; //支付关闭交易时间
    String notifyDomain; //支付回调的
    String return_url;//支付

    String qrAuthCode;//付款码支付时的数字
    String deptCode;// 账项代码
    String hotelId;// 要支付的目标酒店ID
    String mchId;//需要二次分账的商户ID


    int paymode;//非必填.只是公众号跟微信小程序支付需要区分

    Integer payscene = 0;//支付场景值.0默认为订单支付.其他值对应payutil 里的枚举


}

package com.cw.service.config.cashier.impl;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.cw.core.CoreAvl;
import com.cw.core.CoreRs;
import com.cw.entity.ArAccount;
import com.cw.entity.GuestAccounts;
import com.cw.mapper.AraccountMapper;
import com.cw.mapper.GuestAccountMapper;
import com.cw.mapper.ReservationMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.pojo.dto.ota.req.OtaCancelChargeReq;
import com.cw.pojo.dto.ota.req.OtaOrderArPayReq;
import com.cw.pojo.dto.pms.req.cashier.ArDiscountReq;
import com.cw.pojo.dto.pms.req.cashier.ArWriteOffReq;
import com.cw.pojo.dto.pms.req.cashier.CashierAccountReq;
import com.cw.pojo.dto.pms.req.cashier.CashierPostReq;
import com.cw.pojo.dto.pms.req.guest.QueryARAccLogReq;
import com.cw.pojo.dto.pms.req.guest.QueryARDetailReq;
import com.cw.pojo.dto.pms.req.reservation.BatchAssignRoomReq;
import com.cw.pojo.dto.pms.req.room.RoomSearchReq;
import com.cw.pojo.dto.pms.res.ar.ArDetailListRes;
import com.cw.pojo.dto.pms.res.reservation.BatchAssginRoomResult;
import com.cw.pojo.dto.pms.res.room.RoomListRes;
import com.cw.pojo.dto.pms.res.room.RoomRes;
import com.cw.pojo.sqlresult.ArAmounts;
import com.cw.service.config.guest.impl.GuestAccountServiceImpl;
import com.cw.service.config.ota.Pms_OtaServiceImpl;
import com.cw.service.na.NaService;
import com.cw.utils.CalculateDate;
import com.cw.utils.SpringUtil;
import com.cw.utils.tool.DefValData;
import com.google.common.collect.Lists;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class CashierTestServiceImpl {

	@Resource
	private DaoLocal<?> dao;

	public void test() throws Exception {
		String hotelid = "001";
		// testArWriteOff(hotelid);
//		String prepay_sql = "REPLACE INTO Guest_accounts_his ("
//				+ " `accountsid`, `amount`, `department_code`, `description`, `hotelid`, `income_user`, `remark`, `reservation_number`"
//				+ ", `business_date`, `internal`, `ar_no`, `split_from_id`, `createdate`, `paymethod`, `serialno`, `tradeno`,   `create_by`"
//				+ ", `create_time`,`modified_by`, `modified_time`, `mov_date`" + ") SELECT "
//				+ " `accountsid`, `amount`, `department_code`, `description`, `hotelid`, `income_user`, `remark`, `reservation_number`"
//				+ ", `business_date`, `internal`, `ar_no`, `split_from_id`, `createdate`, `paymethod`, `serialno`, `tradeno`,   `create_by`"
//				+ ", `create_time`,`modified_by`, `modified_time`, :mov_date" + " FROM Guest_accounts WHERE hotelId=:hotelid and accountsId=:accid";
//		int cnt = dao.executeNativeUpdate(prepay_sql, Map.of("hotelid", hotelid, "accid", "5ZIKF651K1", "mov_date", LocalDate.now().plusDays(-5)));
//		System.out.println("inc " + cnt);
//		cnt = dao.executeUpdate("delete from GuestAccountsHis where hotelId=:hotelid and accountsId=:accid", Map.of("hotelid", hotelid, "accid", "5ZIKF651K1"));
//		System.out.println("del " + cnt);
//		byte[] data = SpringUtil.getBean(com.cw.service.config.print.impl.PrintGuestBillingWTImpl.class).print(hotelid, "*************");
//		Files.write(Paths.get("客账单.docx"), data);
//        SpringUtil.getBean(NaService.class).runNa(hotelid);
//		repWoffAmount(hotelid);
//		testRufNComm(hotelid);
		for (String code : Arrays.asList("MLC", "SXDD", "CSZHA", "25NXH25")) {
//			testWrittenOffDetails(hotelid, code);
//			testSynArBalance(hotelid, code);
//			System.out.println(code + ": " + SpringUtil.getBean(com.cw.service.config.cashier.impl.CashierServiceImpl.class).getArAgingAmount(hotelid, code));
		}
//		testOnlinePay(hotelid);
		com.cw.service.na.impl.NaACCProcess naACCProcess = SpringUtil.getBean(com.cw.service.na.impl.NaACCProcess.class);
//		SpringUtil.getBean(CoreAvl.class).repResRevs(hotelid);
		com.cw.service.na.impl.NaDailyProcess naDailyProcess = SpringUtil.getBean(com.cw.service.na.impl.NaDailyProcess.class);
		com.cw.service.na.impl.NaCalcProcess naCalcProcess = SpringUtil.getBean(com.cw.service.na.impl.NaCalcProcess.class);
//		GuestAccountMapper guestAccountMapper = SpringUtil.getBean(GuestAccountMapper.class);
//		GuestAccounts acc = new GuestAccounts();
//		acc.setHotelId(hotelid);
//		acc.setAccountsId("XXXXXX");
//		acc.setIncomeUser("SDDD");
//		acc.setReservationNumber("dfsdfsdf");;
//		guestAccountMapper.saveAndFlush(JpaUtil.appendEntity(acc));
//		naDailyProcess.handleHistory_run(hotelid, DateUtil.parse("2025-01-19"));
//		naACCProcess.processStatistics(hotelid, DateUtil.parse("2025-03-07"));
//		naDailyProcess.handelARCustClean(hotelid, DateUtil.parse("2025-01-19"));
		for (int i = 3; i > 0; i--) {
			Date date = DateUtil.offsetDay(new Date(), -i);
//			naACCProcess.calcTrialBalance(hotelid, date);
//			naACCProcess.processStatistics(hotelid, date);
//			naCalcProcess.dailyStaticRun(hotelid, date);
//			NaDailyLog naDailyLog = new NaDailyLog();
//			naDailyLog.setHotelId(hotelid);
//			naDailyLog.setNaDate(date);
//			naACCProcess.execute(new NaTaskInfo(naDailyLog, null));
//			naACCProcess.postRoomRate(hotelid, date);
//			naACCProcess.processStatisticsAccItem(hotelid, date);
//			naACCProcess.processStatistics(hotelid, date);
		}
//		testResPstARNWrtoff(hotelid, "*************", "CSZHA");
//		naACCProcess.processTrialBalance(hotelid, DateUtil.parse("2024-12-02"));
//		testCancel(hotelid, "7310826490");
//		naACCProcess.processStatistics(hotelid, DateUtil.date());
//		testResPstARNCancel(hotelid, "2024110464003", "SXDD");
//		testARPayNCancel(hotelid, "SXDD");
//		testARPayNRefund(hotelid, "SXDD");
//		testARBalanceRefund(hotelid, "SXDD");
//		testARTransfer(hotelid, Arrays.asList("3AIT3EBL00"), "SXDD", "MLC");
//		testResPstARNWrtoff(hotelid, "2025022044006", "MLC");
//		testSynArBalance(hotelid, "SXDD");
//		testSynArBalance(hotelid, "MLC");
//		testArWriteOff(hotelid);
//		testArCancelWriteOff(hotelid);
//		naACCProcess.processStatistics(hotelid, DateUtil.offsetDay(new Date(), 0));
//		naACCProcess.processStatistics(hotelid, DateUtil.date());
//		testARInfo(hotelid, "CSZHA");
//		testARInfo(hotelid, "MLC");
//		listArPaymentDetails(hotelid, "25NXH25");
//		testOtaArPay(hotelid);
//		testPostRoomRate(hotelid);
//		testARPost(hotelid);
	}

	private void testPostRoomRate(String hotelid) {
//		System.setProperty("debughotelid", hotelid);
		String res_no = "2025012747002";
		Date date = DateUtil.parse("2025-01-27");
//		Map<String, Object> hpars = new HashMap<>();
//		hpars.put("hotelId", hotelid);
//		hpars.put("res_no", res_no);
//		hpars.put("date", date);
//		List<PkgDaily> pkgDailies = dao.getList("from PkgDaily where hotelId=:hotelId and reservationNumber=:res_no and usedate<=:date", hpars);
//		List<RoomsDaily> rmDailies = dao.getList("from RoomsDaily where hotelId=:hotelId and reservationNumber=:res_no and datum<=:date", hpars);
//		pkgDailies.forEach(d -> d.setPosted_time(null));
//		rmDailies.forEach(d -> d.setPosted_time(null));
//		SpringUtil.getBean(Rooms_dailyMapper.class).saveAll(rmDailies);
//		SpringUtil.getBean(Pkg_dailyMapper.class).saveAll(pkgDailies);
//		SpringUtil.getBean(com.cw.service.config.cashier.impl.CashierRoomRateServiceImpl.class).postRoomRate(hotelid, res_no, date);
		SpringUtil.getBean(com.cw.service.config.cashier.impl.CashierRoomRateServiceImpl.class).postAddtionRoomRate(hotelid, res_no, date, BigDecimal.valueOf(0.5), false, "加收");
	}

	private void testOtaArPay(String hotelid) {
//		System.setProperty("debughotelid", hotelid);
		Pms_OtaServiceImpl impl = SpringUtil.getBean(Pms_OtaServiceImpl.class);
		OtaOrderArPayReq req = new OtaOrderArPayReq();
		req.setAmount(BigDecimal.valueOf(333));
		req.setAr_no("MLC");
		req.setItfNo("CY2025012100004");
		req.setDeptCode("2003");
		req.setRemark("1.21.1433应收");
		String accid = impl.arPay(req).getAccid();
		OtaCancelChargeReq cancelChargeReq = new OtaCancelChargeReq();
		cancelChargeReq.setAccid(accid);
		cancelChargeReq.setRemark("test");
		log.info("CancelCharge Req:{}", cancelChargeReq);
		log.info("CancelCharge Resp:{}", impl.cancelCharge(cancelChargeReq));
	}

	/**
	 * 应收充值日志
	 * 
	 * @param hotelid
	 * @param arNo
	 */
	private void listArPaymentDetails(String hotelid, String arNo) {
//		System.setProperty("debughotelid", "001");
		QueryARDetailReq queryARDetailReq = new QueryARDetailReq();
		queryARDetailReq.setAr_no(arNo);
		queryARDetailReq.setQueryMode(4);
		GuestAccountServiceImpl guestAccountService = SpringUtil.getBean(GuestAccountServiceImpl.class);
		ArDetailListRes res = guestAccountService.listARGuestAccountDetail(queryARDetailReq);
		log.info("{}", res);
		res.getRecords().forEach(e -> {
			log.info("{}	{} 	{} 	充值：{} 	可用金额：{} 已使用：{}", e.getAccountsId(), e.getCreateTime(), e.getDepartmentCode(), e.getAmount(), e.getAmount_curr(),
					NumberUtil.sub(e.getAmount(), e.getAmount_curr()), e.getRemark());
		});
	}

	private void testOnlinePay(String hotelid) {
		com.cw.service.config.cashier.impl.CashierServiceImpl cashierServiceImpl = SpringUtil.getBean(com.cw.service.config.cashier.impl.CashierServiceImpl.class);
		// CashierPostReq cashierPostReq = new CashierPostReq();
		// cashierPostReq.setReservationNumber("*************");
		// cashierPostReq.getAccounts().add(new CashierAccountReq()//
		// .setDepartmentCode("9100")//
		// .setQueryPayMethod(PayProcessStatus.PAYMETHOD_WX)//发起online支付类型
		// .setTradeno("131764380045478194")//扫码结果
		// .setCredit(BigDecimal.valueOf(-0.01)));
		// for (String id : cashierServiceImpl.post(hotelid,
		// cashierPostReq).getMaster_ids()) {
		// System.out.println(id);
		// cashierServiceImpl.refund(hotelid, id,null, "test remark");
		// }
		cashierServiceImpl.refund(hotelid, "5UG529V301", null, "test remark");
	}

	private void repWoffAmount(String hotelid) {
		dao.executeUpdate("update GuestAccounts set amount_write_off=:amt where hotelId=:hotelid", Map.of(//
				"hotelid", hotelid//
				, "amt", BigDecimal.ZERO//
		));
		List<Object[]> payfors = dao.getList(
				"select ar_payfor_id, sum(price*quantity+credit) from GuestAccounts where hotelId=:hotelid and ar_no<>'' and ar_payfor_id<>'' group by ar_payfor_id", Map.of(//
						"hotelid", hotelid//
				));
		payfors.forEach(objs -> {
			String id = (String) objs[0];
			BigDecimal amt = (BigDecimal) objs[1];
			dao.executeUpdate("update GuestAccounts set amount_write_off=:amt where hotelId=:hotelid and accountsid=:id", Map.of(//
					"hotelid", hotelid//
					, "id", id//
					, "amt", amt//
			));
		});
		DefValData<String, List<GuestAccounts>> arpayfors = new DefValData<String, List<GuestAccounts>>().setInitHandler(k -> Lists.newArrayList());
		Map<String, GuestAccounts> credits = new HashMap<>();
		Map<String, GuestAccounts> adjusts = new HashMap<>();
		Map<String, GuestAccounts> reswfs = new HashMap<>();
		BigDecimal t_credit = BigDecimal.ZERO, t_adj = BigDecimal.ZERO, t_res = BigDecimal.ZERO//
				, t_res_wf = BigDecimal.ZERO, t_payable = BigDecimal.ZERO, t_payfor = BigDecimal.ZERO, t_wf = BigDecimal.ZERO//
		;
		Collection<String> payforids = new HashSet<>(), pwfids = new HashSet<>(), awfids = new HashSet<>(), rwfids = new HashSet<>(), wfids = new HashSet<>()//
				, resss = new HashSet<>(), adjids = new HashSet<>(), refendids = new HashSet<>();
		List<GuestAccounts> accs = dao.getList("from GuestAccounts WHERE hotelId=:hotelid and ar_no=:ar_no", Map.of("hotelid", hotelid, "ar_no", "SXDD"));
		for (GuestAccounts acc : accs) {
			if (StrUtil.isNotBlank(acc.getRes_no_org())) {
				resss.add(acc.getAccountsId());
			} else if (acc.getPrice().multiply(acc.getQuantity()).compareTo(BigDecimal.ZERO) != 0) {
				adjids.add(acc.getAccountsId());
			}
			arpayfors.get(acc.getAr_payfor_id()).add(acc);
			if (acc.getCredit().compareTo(BigDecimal.ZERO) != 0) {
				if (acc.getCredit().compareTo(BigDecimal.ZERO) > 0) {
					refendids.add(acc.getAccountsId());
				}
				credits.put(acc.getAccountsId(), acc);
				t_credit = acc.getCredit().add(t_credit);
				if (StrUtil.isBlank(acc.getAr_payfor_id())) {
//					if (acc.getCredit().compareTo(BigDecimal.ZERO) < 0) {
					t_payable = acc.getCredit().add(acc.getAmount_write_off()).add(t_payable);
//					}
				} else {
					pwfids.add(acc.getAr_payfor_id());
				}
			} else {
				if (StrUtil.isBlank(acc.getRes_no_org())) {
					awfids.add(acc.getAr_payfor_id());
					adjusts.put(acc.getAccountsId(), acc);
					t_adj = acc.getPrice().multiply(acc.getQuantity()).add(t_adj);
				} else {
					rwfids.add(acc.getAr_payfor_id());
					reswfs.put(acc.getAccountsId(), acc);
					t_res = acc.getPrice().multiply(acc.getQuantity()).add(acc.getAmount_write_off()).add(t_res);
					t_res_wf = acc.getAmount_write_off().add(t_res_wf);
				}
			}
			if (StrUtil.isNotBlank(acc.getAr_payfor_id())) {
				t_payfor = acc.getPrice().multiply(acc.getQuantity()).add(acc.getCredit()).add(t_payfor);
				payforids.add(acc.getAr_payfor_id());
			}
			if (acc.getAmount_write_off().compareTo(BigDecimal.ZERO) != 0) {
				t_wf = acc.getAmount_write_off().add(t_wf);
				wfids.add(acc.getAccountsId());
			}
		}
		arpayfors.getDataMap().remove("");
		arpayfors.getDataMap().remove(null);
		List<GuestAccounts> aaa = Lists.newArrayList();
		List<GuestAccounts> oth = Lists.newArrayList();
		List<GuestAccounts> adjs = Lists.newArrayList();
		List<GuestAccounts> resadjs = Lists.newArrayList();
		arpayfors.getDataMap().forEach((id, amts) -> {
			testArCancelWriteOff(hotelid, id);
//			TreeSet<LocalDate> times = new TreeSet<>();
//			amts.forEach(e -> times.add(e.getBusiness_date()));
//			int cnt = dao.executeUpdate("update GuestAccounts set business_date=:time where hotelId=:hotelid and ar_payfor_id=:id",
//					Map.of("time", times.first(), "hotelid", hotelid, "id", id));
//			log.info("{} -> {} {}", id, times.first(), cnt);
			if (resss.contains(id)) {
				aaa.addAll(amts);
				amts.forEach(acc -> {
					if (acc.getPrice().multiply(acc.getQuantity()).compareTo(BigDecimal.ZERO) != 0) {
						resadjs.add(acc);
					}
				});
			} else if (adjids.contains(id)) {
				adjs.addAll(amts);
			} else if (refendids.contains(id)) {
				oth.addAll(amts);
			}
		});
		;
		System.out.println(NumberUtil.add(CollectionUtil.map(aaa, acc -> acc.getPrice().multiply(acc.getQuantity()).add(acc.getCredit()), true).toArray(new BigDecimal[0])));
		System.out.println(NumberUtil.add(CollectionUtil.map(adjs, acc -> acc.getPrice().multiply(acc.getQuantity()).add(acc.getCredit()), true).toArray(new BigDecimal[0])));
		System.out.println(NumberUtil.add(CollectionUtil.map(resadjs, acc -> acc.getPrice().multiply(acc.getQuantity()).add(acc.getCredit()), true).toArray(new BigDecimal[0])));
		System.out.println(NumberUtil.add(CollectionUtil.map(oth, acc -> acc.getPrice().multiply(acc.getQuantity()).add(acc.getCredit()), true).toArray(new BigDecimal[0])));
		resss.retainAll(adjids);
		System.out.println(resss);
		Collection<String> ids = new HashSet<String>(wfids);
		ids.retainAll(payforids);
		Collection<String> adjpayforids = new HashSet<String>(payforids);
		adjpayforids.retainAll(adjusts.keySet());
		BigDecimal t_same = BigDecimal.ZERO, t_d_p = BigDecimal.ZERO, t_d_w = BigDecimal.ZERO;
		for (GuestAccounts acc : accs) {
			if (ids.contains(acc.getAr_payfor_id())) {
				t_same = acc.getPrice().multiply(acc.getQuantity()).add(acc.getCredit()).add(t_same);
			} else if (payforids.contains(acc.getAr_payfor_id())) {
				t_d_p = acc.getPrice().multiply(acc.getQuantity()).add(acc.getCredit()).add(t_d_p);
			}
			if (!ids.contains(acc.getAccountsId()) && wfids.contains(acc.getAccountsId())) {
				t_d_w = acc.getAmount_write_off().add(t_d_w);
				testArCancelWriteOff(hotelid, acc.getAccountsId());
			}
		}
//		ids.removeAll(reswfs.keySet());
		log.info("充值:{}"//
				+ "\n调整:{}"//
				+ "\n预定未核销:{}"//
				+ "\n预定已核销{}"//
				+ "\n可用支付{}"//
				+ "\n总核销-payfor{}"//
				+ "\n总核销-woff{}"//
				+ "\n核销差{}"//
				+ "\n付款核销调整{}"//
				, t_credit, t_adj, t_res, t_res_wf, t_payable//
				, t_payfor, t_wf//
				, (wfids.size() + ":" + payforids.size() + ":" + ids.size() + ":" + t_same + ":" + t_d_p + ":" + t_d_w)//
				, adjpayforids//
		);
	}

	/**
	 * 撤销核销
	 *
	 * @param hotelid
	 * @param accid
	 */
	private void testArCancelWriteOff(String hotelid, String accid) {
		String remark = "acc-test-核销取消";
		com.cw.service.config.cashier.impl.CashierServiceImpl cashierServiceImpl = SpringUtil.getBean(com.cw.service.config.cashier.impl.CashierServiceImpl.class);
		cashierServiceImpl.arCancelWriteOff(hotelid, accid, remark);
	}

	/**
	 * 测试应收核销
	 * 
	 * @param hotelid
	 */
	private void testArWriteOff(String hotelid) {
		String remark = "acc-test";
		com.cw.service.config.cashier.impl.CashierServiceImpl cashierServiceImpl = SpringUtil.getBean(com.cw.service.config.cashier.impl.CashierServiceImpl.class);
		ArWriteOffReq req = new ArWriteOffReq().setAcc_ids(CollectionUtil.toList("60GWG27KP2")) // 待核销的acc_id
				.setRemark(remark).setUse_ar_balance(true);// 是否使用企业余额
		// req.getDiscounts().add(new
		// ArDiscountReq().setRemark(remark).setDepartmentCode("2000").setAmount(BigDecimal.valueOf(34)));
		// // 坏账金额
		// req.getDiscounts().add(new
		// ArDiscountReq().setRemark(remark).setDepartmentCode("2100").setAmount(BigDecimal.valueOf(34)));
		// // 抹零金额
		// req.getPayments().add(new
		// CashierAccountReq().setDepartmentCode("9000").setCredit(BigDecimal.valueOf(-50)));
		// // 冲账付款 组合1
		// req.getPayments().add(new
		// CashierAccountReq().setDepartmentCode("9101").setCredit(BigDecimal.valueOf(-900)));
		// // 冲账付款 组合2
		cashierServiceImpl.ar_write_off(hotelid, req);

	}

	private void testSynArBalance(String hotelid, String ar_no) {
		com.cw.service.config.cashier.impl.CashierServiceImpl cashierServiceImpl = SpringUtil.getBean(com.cw.service.config.cashier.impl.CashierServiceImpl.class);
		cashierServiceImpl.synArBalance(hotelid, ar_no, false);
	}

	private void testARInfo(String hotelid, String ar_no) {
		GuestAccountMapper guestAccountMapper = SpringUtil.getBean(GuestAccountMapper.class);
//		acc_ids.forEach(id -> log.info("{}",guestAccountMapper.getArAccBalance(hotelid, id)));
		log.info(guestAccountMapper.getArPayAbleIDs(hotelid, ar_no).toString());
		{
			GuestAccountServiceImpl guestAccountServiceImpl = SpringUtil.getBean(GuestAccountServiceImpl.class);
			QueryARAccLogReq queryARAccLogReq = new QueryARAccLogReq().setAr_no(ar_no);
			queryARAccLogReq.getPages().setPagesize(100);
			queryARAccLogReq.getPages().setCurrentpage(0);
			log.info("应收操作日志：	{}", JSONUtil.toJsonPrettyStr(guestAccountServiceImpl.listARAccLogs(hotelid, queryARAccLogReq)));
			com.cw.pojo.dto.pms.req.guest.QueryARDetailReq queryARDetailReq = new com.cw.pojo.dto.pms.req.guest.QueryARDetailReq();
			queryARDetailReq.setAr_no(ar_no);
			org.springframework.data.domain.Page<com.cw.entity.GuestAccounts> pagRst;
//			{// toJsonPrettyStr
//				queryARDetailReq.setQueryMode(1);
//				log.info("待核销	{}", JSONUtil.toJsonPrettyStr(guestAccountServiceImpl.listARGuestAccountDetail(hotelid, queryARDetailReq)));
//			}
//			{
//				queryARDetailReq.setQueryMode(2);
//				log.info("已核销 {}", JSONUtil.toJsonPrettyStr(guestAccountServiceImpl.listARGuestAccountDetail(hotelid, queryARDetailReq)));
//			}
//			{
//				queryARDetailReq.setQueryMode(3);
//				log.info("可支付余额	{}", JSONUtil.toJsonPrettyStr(guestAccountServiceImpl.listARGuestAccountDetail(hotelid, queryARDetailReq)));
//			}
			{
				queryARDetailReq.setQueryMode(4);
				log.info("金额流水	{}", JSONUtil.toJsonPrettyStr(guestAccountServiceImpl.listARGuestAccountDetail(hotelid, queryARDetailReq)));
			}
			com.cw.service.config.cashier.impl.CashierServiceImpl cashierServiceImpl = SpringUtil.getBean(com.cw.service.config.cashier.impl.CashierServiceImpl.class);
//			cashierServiceImpl.synArBalance(hotelid, ar_no);
			log.info(JSONUtil.toJsonPrettyStr(SpringUtil.getBean(AraccountMapper.class).findByHotelIdAndCode(hotelid, ar_no)));
		}
	}

	/**
	 * 测试应收核销明细查看
	 * 
	 * @param hotelid
	 * @param ar_no
	 */
	private void testWrittenOffDetails(String hotelid, String ar_no) {
//		System.setProperty("debughotelid", "001");
		GuestAccountServiceImpl guestAccountServiceImpl = SpringUtil.getBean(GuestAccountServiceImpl.class);
		QueryARDetailReq req = new QueryARDetailReq();
		req.setAr_no(ar_no);
		req.getPages().setPagesize(100);
		req.getPages().setCurrentpage(0);
		req.getPages().setSortname("modifiedTime");
		req.getPages().setSortorder("descending");
		log.info("应收{}核销日志：	{}", ar_no, JSONUtil.toJsonPrettyStr(guestAccountServiceImpl.listARWrittenOffDetails(req)));
	}

	/**
	 * 将挂账转到其他应收账户
	 * 
	 * @param hotelid
	 * @param ar_no_scr
	 * @param ar_no_tag
	 */
	private void testARTransfer(String hotelid, Collection<String> acc_ids, String ar_no_scr, String ar_no_tag) {
		String remark = "acc-test";
		com.cw.service.config.cashier.impl.CashierServiceImpl cashierServiceImpl = SpringUtil.getBean(com.cw.service.config.cashier.impl.CashierServiceImpl.class);
//		cashierServiceImpl.transferArAccBalance(hotelid,
//				new ArAccDepositTransferReq().setAmount(BigDecimal.valueOf(300)).setArNo(ar_no_scr).setTargetArNo(ar_no_tag).setRemark(remark));
		com.cw.mapper.AraccountMapper araccountMapper = SpringUtil.getBean(com.cw.mapper.AraccountMapper.class);
		ArAccount cust;
		cust = araccountMapper.findByHotelIdAndCode(hotelid, ar_no_scr);
		log.info("org from {} {}", ar_no_scr, new ArAmounts(cust.getNoacc(), cust.getBalance(), cust.getCredit(), cust.getAdjust(), cust.getWriteoff()));
		cust = araccountMapper.findByHotelIdAndCode(hotelid, ar_no_tag);
		log.info("org to {} {}", ar_no_tag, new ArAmounts(cust.getNoacc(), cust.getBalance(), cust.getCredit(), cust.getAdjust(), cust.getWriteoff()));
		cashierServiceImpl.transferArAccDetails(hotelid, acc_ids, ar_no_tag, remark);
		cust = araccountMapper.findByHotelIdAndCode(hotelid, ar_no_scr);
		log.info("curr from {} {}", ar_no_scr, new ArAmounts(cust.getNoacc(), cust.getBalance(), cust.getCredit(), cust.getAdjust(), cust.getWriteoff()));
		cust = araccountMapper.findByHotelIdAndCode(hotelid, ar_no_tag);
		log.info("curr to {} {}", ar_no_tag, new ArAmounts(cust.getNoacc(), cust.getBalance(), cust.getCredit(), cust.getAdjust(), cust.getWriteoff()));
	}

	/**
	 * 测试企业余额退款
	 *
	 * @param hotelid
	 * @param res_no
	 * @param ar_no
	 */
	private void testARBalanceRefund(String hotelid, String ar_no) {
		com.cw.service.config.cashier.impl.CashierServiceImpl cashierServiceImpl = SpringUtil.getBean(com.cw.service.config.cashier.impl.CashierServiceImpl.class);
		String remark = "acc-test";
		CashierPostReq cashierPostReq = new CashierPostReq().setAr_no(ar_no)//
				.setRemark(remark)//
				.setAccounts(CollectionUtil.toList(new CashierAccountReq()//
						.setDepartmentCode("9000")//
						.setCredit(BigDecimal.valueOf(30))//
				));
		cashierServiceImpl.arBalanceRefund(hotelid, cashierPostReq);
//		cashierServiceImpl.synArBalance(hotelid, ar_no, false);
	}

	/**
	 * 测试应收充值、部分退款
	 *
	 * @param hotelid
	 * @param res_no
	 * @param ar_no
	 */
	private void testARPayNRefund(String hotelid, String ar_no) {
		com.cw.service.config.cashier.impl.CashierServiceImpl cashierServiceImpl = SpringUtil.getBean(com.cw.service.config.cashier.impl.CashierServiceImpl.class);
		Collection<String> acc_ids = new HashSet<>();
		String remark = "acc-test";
		{// 应收充值
			CashierPostReq cashierPostReq = new CashierPostReq();
			cashierPostReq.setAr_no(ar_no);
			cashierPostReq.setRemark(remark);
			List<CashierAccountReq> lis = new ArrayList<>();
			CashierAccountReq cashierAccountReq = new CashierAccountReq();
			cashierAccountReq.setDepartmentCode("9000");
			cashierAccountReq.setCredit(BigDecimal.valueOf(-200));
			lis.add(cashierAccountReq);
			cashierPostReq.setAccounts(lis);
			acc_ids.addAll(cashierServiceImpl.post(hotelid, cashierPostReq).getMaster_ids());
		}
		acc_ids.forEach(id -> cashierServiceImpl.refund(hotelid, id, BigDecimal.valueOf(50), remark));
	}

	/**
	 * 测试应收充值、取消
	 *
	 * @param hotelid
	 * @param res_no
	 * @param ar_no
	 */
	private void testARPayNCancel(String hotelid, String ar_no) {
		com.cw.service.config.cashier.impl.CashierServiceImpl cashierServiceImpl = SpringUtil.getBean(com.cw.service.config.cashier.impl.CashierServiceImpl.class);
		Collection<String> acc_ids = new HashSet<>();
		String remark = "acc-test";
		{// 应收充值
			CashierPostReq cashierPostReq = new CashierPostReq();
			cashierPostReq.setAr_no(ar_no);
			cashierPostReq.setRemark(remark);
			List<CashierAccountReq> lis = new ArrayList<>();
			CashierAccountReq cashierAccountReq = new CashierAccountReq();
			cashierAccountReq.setDepartmentCode("9000");
			cashierAccountReq.setCredit(BigDecimal.valueOf(-200));
			lis.add(cashierAccountReq);
			cashierPostReq.setAccounts(lis);
			acc_ids.addAll(cashierServiceImpl.post(hotelid, cashierPostReq).getMaster_ids());
		}
		acc_ids.forEach(id -> cashierServiceImpl.cancel(hotelid, id, remark));
	}

	private void testCancel(String hotelid, String id) {
		com.cw.service.config.cashier.impl.CashierServiceImpl cashierServiceImpl = SpringUtil.getBean(com.cw.service.config.cashier.impl.CashierServiceImpl.class);
		cashierServiceImpl.cancel(hotelid, id, "取消");
	}

	/**
	 * 测试预定挂应收、取消
	 *
	 * @param hotelid
	 * @param res_no
	 * @param ar_no
	 */
	private void testResPstARNCancel(String hotelid, String res_no, String ar_no) {
		com.cw.service.config.cashier.impl.CashierServiceImpl cashierServiceImpl = SpringUtil.getBean(com.cw.service.config.cashier.impl.CashierServiceImpl.class);
		Collection<String> acc_ids = new HashSet<>();
		String remark = "acc-test";
		{// 挂应收
			CashierPostReq cashierPostReq = new CashierPostReq();
			cashierPostReq.setReservationNumber(res_no);
			cashierPostReq.setAr_no(ar_no);
			cashierPostReq.setRemark(remark);
			List<CashierAccountReq> lis = new ArrayList<>();
			CashierAccountReq cashierAccountReq = new CashierAccountReq();
			cashierAccountReq.setDepartmentCode("9102");
			cashierAccountReq.setCredit(BigDecimal.valueOf(-200));
			lis.add(cashierAccountReq);
			cashierPostReq.setAccounts(lis);
			acc_ids.addAll(cashierServiceImpl.post(hotelid, cashierPostReq).getMaster_ids());
		}
		acc_ids.forEach(id -> cashierServiceImpl.cancel(hotelid, id, remark));
	}

	/**
	 * 测试应收入账
	 * 
	 * @param hotelid
	 * @param res_no
	 * @param ar_no
	 * @param deptcode
	 * @param amount
	 */
	private void testARPost(String hotelid) {
		com.cw.service.config.cashier.impl.CashierServiceImpl cashierServiceImpl = SpringUtil.getBean(com.cw.service.config.cashier.impl.CashierServiceImpl.class);
		if (true) {// 接口直接挂应收
			CashierPostReq cashierPostReq = new CashierPostReq().setRemark("acc-test")//
					.setReservationNumber("ITF@776786")// 伪造预定号，规则为 渠道类 + @ + 流水号
					.setAr_no("SXDD");// 应收账号
			cashierPostReq.getAccounts().add(new CashierAccountReq()//
					.setDepartmentCode("2003")// 接口类型配置付款码
					.setPrice(BigDecimal.valueOf(0.03))//
			);
			cashierServiceImpl.post(hotelid, cashierPostReq).getAcc_ids().forEach(id -> {
				cashierServiceImpl.refund(hotelid, id, null);
			});
		}
		if (false) {// pms预定挂应收
			CashierPostReq cashierPostReq = new CashierPostReq().setRemark("acc-test")//
					.setReservationNumber("*************")// 预定号
					.setAr_no("CSZHA");// 应收账号
			cashierPostReq.getAccounts().add(new CashierAccountReq()//
					.setDepartmentCode("9000")// 支付码（pms预定账单出现）
					.setCredit(BigDecimal.valueOf(-230))// 金额，注意负数
			);
			cashierServiceImpl.post(hotelid, cashierPostReq);
		}
		if (false) {// 接口直接挂应收
			CashierPostReq cashierPostReq = new CashierPostReq().setRemark("acc-test")//
					.setReservationNumber("ITF@776786")// 伪造预定号，规则为 渠道类 + @ + 流水号
					.setAr_no("CSZHA");// 应收账号
			cashierPostReq.getAccounts().add(new CashierAccountReq()//
					.setDepartmentCode("9000")// 接口类型配置付款码
					.setCredit(BigDecimal.valueOf(-250))// 金额，注意负数
			);
			cashierServiceImpl.post(hotelid, cashierPostReq);
		}
		if (false) {// 测试应收退款
			cashierServiceImpl.refund(hotelid, "5ZIKF651K1", ListUtil.toList(//
					new CashierAccountReq()//
							.setDepartmentCode("3300")// 违约金码1
							.setPrice(BigDecimal.valueOf(30))//
					, new CashierAccountReq()//
							.setDepartmentCode("3000")// 违约金码2
							.setPrice(BigDecimal.valueOf(20))//
					, new CashierAccountReq()//
							.setCredit(BigDecimal.valueOf(200))// 剩余金额可以不填写账项码
			));
		}
	}

	/**
	 * 测试带手续费退款
	 * 
	 * @param hotelid
	 */
	private void testRufNComm(String hotelid) {
		com.cw.service.config.cashier.impl.CashierServiceImpl cashierServiceImpl = SpringUtil.getBean(com.cw.service.config.cashier.impl.CashierServiceImpl.class);
		CashierPostReq cashierPostReq = new CashierPostReq().setRemark("acc-test")//
				.setReservationNumber("*************")// 预定号
		;
		cashierPostReq.getAccounts().add(new CashierAccountReq()//
				.setDepartmentCode("9100")// 支付码（pms预定账单出现）
				.setCredit(BigDecimal.valueOf(-330))// 金额，注意负数
		);
		cashierServiceImpl.post(hotelid, cashierPostReq).getMaster_ids().forEach(id -> {
			cashierServiceImpl.cancel(hotelid, id, ListUtil.toList(//
					new CashierAccountReq()//
							.setDepartmentCode("3300")// 违约金码1
							.setPrice(BigDecimal.valueOf(30))//
							.setRemark("违约金1")//
					, new CashierAccountReq()//
							.setDepartmentCode("3000")// 违约金码2
							.setPrice(BigDecimal.valueOf(20))//
							.setRemark("违约金2")//
			));
		});
	}

	/**
	 * 测试应收充值，预定挂应收，应收冲销
	 *
	 * @param hotelid
	 * @param res_no
	 * @param ar_no
	 */
	private void testResPstARNWrtoff(String hotelid, String res_no, String ar_no) {
		com.cw.service.config.cashier.impl.CashierServiceImpl cashierServiceImpl = SpringUtil.getBean(com.cw.service.config.cashier.impl.CashierServiceImpl.class);
		com.cw.mapper.AraccountMapper araccountMapper = SpringUtil.getBean(com.cw.mapper.AraccountMapper.class);
		Collection<String> acc_ids = new HashSet<>();
		String remark = "acc-test";
//		araccountMapper.synBalance(hotelid, ar_no, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
		if (true) {// 应收充值
			CashierPostReq cashierPostReq = new CashierPostReq();
			cashierPostReq.setAr_no(ar_no);
			cashierPostReq.setRemark(remark + "-充值");
			List<CashierAccountReq> lis = new ArrayList<>();
			CashierAccountReq cashierAccountReq = new CashierAccountReq();
			cashierAccountReq.setDepartmentCode("9000");
			cashierAccountReq.setCredit(BigDecimal.valueOf(-200));
			lis.add(cashierAccountReq);
			cashierPostReq.setAccounts(lis);
			cashierServiceImpl.post(hotelid, cashierPostReq);
//			acc_ids.addAll(cashierServiceImpl.post(hotelid, cashierPostReq).getAr_ids());
			log.info("应收充值: {}", JSONUtil.toJsonPrettyStr(araccountMapper.viewAmounts(hotelid, ar_no)));
		}
		{// 挂应收
			CashierPostReq cashierPostReq = new CashierPostReq();
			cashierPostReq.setReservationNumber(res_no);
			cashierPostReq.setAr_no(ar_no);
			cashierPostReq.setRemark(remark + "-预定挂应收");
			List<CashierAccountReq> lis = new ArrayList<>();
			CashierAccountReq cashierAccountReq = new CashierAccountReq();
			cashierAccountReq.setDepartmentCode("9102");
			cashierAccountReq.setCredit(BigDecimal.valueOf(-300));
			lis.add(cashierAccountReq);
			cashierPostReq.setAccounts(lis);
			acc_ids.addAll(cashierServiceImpl.post(hotelid, cashierPostReq).getAr_ids());
			log.info("预定挂应收: {}", JSONUtil.toJsonPrettyStr(araccountMapper.viewAmounts(hotelid, ar_no)));
		}
		{
			ArWriteOffReq req = new ArWriteOffReq().setAcc_ids(acc_ids).setRemark(remark + "-核销").setUse_ar_balance(true);
			req.getDiscounts().add(new ArDiscountReq().setRemark(remark + "-核销折扣").setDepartmentCode("2000").setAmount(BigDecimal.valueOf(50)));
			req.getPayments().add(new CashierAccountReq().setDepartmentCode("9000").setCredit(BigDecimal.valueOf(-100)));
//			req.getPayments().add(new CashierAccountReq().setDepartmentCode("9101").setCredit(BigDecimal.valueOf(-900)));
			cashierServiceImpl.ar_write_off(hotelid, req);
			log.info("核销: {}", JSONUtil.toJsonPrettyStr(araccountMapper.viewAmounts(hotelid, ar_no)));
		}
//		{
//			acc_ids.forEach(id -> cashierServiceImpl.arCancelWriteOff(hotelid, id, remark + "-核销取消"));
//			log.info("审核取消: {}", JSONUtil.toJsonPrettyStr(araccountMapper.viewAmounts(hotelid, ar_no)));
//		}
//		{
//			CashierPostReq cashierPostReq = new CashierPostReq().setAr_no(ar_no)//
//					.setRemark(remark + "-余额退款")//
//					.setAccounts(CollectionUtil.toList(new CashierAccountReq()//
//							.setDepartmentCode("9000")//
//							.setCredit(BigDecimal.valueOf(30))//
//					));
//			cashierServiceImpl.arBalanceRefund(hotelid, cashierPostReq);
//			log.info("余额退款: {}", JSONUtil.toJsonPrettyStr(araccountMapper.viewAmounts(hotelid, ar_no)));
//		}
//		{
//			cashierServiceImpl.transferArAccBalance(hotelid,
//					new ArAccDepositTransferReq().setAmount(BigDecimal.valueOf(12)).setArNo(ar_no).setTargetArNo("MLC").setRemark(remark + "-余额转结"));
//			log.info("余额转结: {}", JSONUtil.toJsonPrettyStr(araccountMapper.viewAmounts(hotelid, ar_no)));
//		}
//		cashierServiceImpl.synArBalance(hotelid, ar_no, false);
		log.info("恢复: {}", JSONUtil.toJsonPrettyStr(araccountMapper.viewAmounts(hotelid, ar_no)));
	}

	public void testAssign() {// 检查校验房间是否被其他预订占用
		String roomNo = "316";
		String reservationNo = "xxxx";
		Date arrdate = CalculateDate.stringToDate("2024-11-25");
		Date deptDate = CalculateDate.stringToDate("2024-11-26");
		String hotelId = "001";

		ReservationMapper rsMapper = SpringUtil.getBean(ReservationMapper.class);
		List<String> occRss = rsMapper.fetchRoomOccupiedRs(roomNo, reservationNo, arrdate, deptDate, hotelId);
		log.info("occRss:{},{},{}", occRss.size() > 0 ? "占用咯" : "非占用", CalculateDate.dateToString(arrdate), CalculateDate.dateToString(deptDate));

		arrdate = CalculateDate.stringToDate("2024-11-26");
		deptDate = CalculateDate.stringToDate("2024-11-30");
		occRss = rsMapper.fetchRoomOccupiedRs(roomNo, reservationNo, arrdate, deptDate, hotelId);
		log.info("occRss:{},{},{}", occRss.size() > 0 ? "占用咯" : "非占用", CalculateDate.dateToString(arrdate), CalculateDate.dateToString(deptDate));

		arrdate = CalculateDate.stringToDate("2024-12-17");
		deptDate = CalculateDate.stringToDate("2024-12-25");
		occRss = rsMapper.fetchRoomOccupiedRs(roomNo, reservationNo, arrdate, deptDate, hotelId);
		log.info("occRss:{},{},{}", occRss.size() > 0 ? "占用咯" : "非占用", CalculateDate.dateToString(arrdate), CalculateDate.dateToString(deptDate));

	}

	public void testBatchAssignRoom() {
		CoreRs coreRs = SpringUtil.getBean(CoreRs.class);
		BatchAssignRoomReq batchAssignRoomReq = new BatchAssignRoomReq();
		batchAssignRoomReq.setRegNos(Arrays.asList("2024122464003"));
		List<BatchAssignRoomReq.AssginInfo> assginInfos = new ArrayList<>();
		BatchAssignRoomReq.AssginInfo assginInfo = new BatchAssignRoomReq.AssginInfo();
		assginInfo.setRmNos(Arrays.asList("317", "318"));
		assginInfo.setRoomType("YZSCF");
		assginInfos.add(assginInfo); // 模拟前台选了317,318
		batchAssignRoomReq.setAssignRoomNos(assginInfos);

		BatchAssginRoomResult assginRoomResult = coreRs.batchAssignRoomReq(batchAssignRoomReq, "001");

		log.info("assginRoomResult:{}", assginRoomResult);

		RoomSearchReq roomSearchReq = new RoomSearchReq();
		roomSearchReq.getPages().setCurrentpage(1);
		roomSearchReq.getPages().setPagesize(5);
		roomSearchReq.setArrDate(CalculateDate.stringToDate("2025-01-01"));
		roomSearchReq.setDeptDate(CalculateDate.stringToDate("2025-01-04"));
		roomSearchReq.setRoomTypes(Arrays.asList("YZSCF", "GJHHTF"));
		RoomListRes res0 = coreRs.getAvlRoomPage(roomSearchReq, "001");
		log.info("rooms:{}", res0.getRecords().stream().map(RoomRes::getRoomNo).collect(Collectors.toList()));

		RoomListRes res = coreRs.getAvlRoomPage(roomSearchReq, "001");
		log.info("res:{}", JSON.toJSONString(res));

	}
}

package com.cw.service.config.print.impl;

import java.io.FileOutputStream;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import com.cw.mapper.common.DaoLocal;
import com.cw.report.enums.ExportType;
import com.cw.service.config.print.folio.service.FolioService;
import com.cw.service.config.print.report.service.ReportServiceManager;
import com.cw.utils.SpringUtil;

import lombok.extern.slf4j.Slf4j;
import net.sf.jasperreports.engine.DefaultJasperReportsContext;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperExportManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import net.sf.jasperreports.engine.export.HtmlExporter;
import net.sf.jasperreports.export.SimpleExporterInput;
import net.sf.jasperreports.export.SimpleHtmlExporterOutput;

@Slf4j
@Service
public class ReportTestServiceImpl {

	@Resource
	private DaoLocal<?> dao;

	@Autowired
	private ReportServiceManager reportServiceManager;

	@Autowired
	private FolioService folioService;

	public ResponseEntity<byte[]> test() throws Exception {
		//System.setProperty("debughotelid", "001");

//		try (FileOutputStream outputStream = new FileOutputStream("MultiLevelGroupingReport.pdf")) {
//			folioService.print2OutputStream("2024120207005", ExportType.PDF, outputStream);
////			ReportService<?> service = reportServiceManager.getReportService("SalesmanPerformanceSummary");
////			service.generateReport(ExportType.PDF, outputStream, service.parseParameters(Map.of("fromDate", "2021-01-01", "toDate", "2026-01-01", "cashierId", "ADMIN")));
//			outputStream.close();
//		}
		ExportType exportType = ExportType.PDF;
		String fileName = "report", typeName = "";
		byte[] reportData;
//		byte[] reportData = SpringUtil.getBean(PrintGuestBillingJasperImpl.class).print("2024112553005");// generateReport(format);
//		byte[] reportData = SpringUtil.getBean(PrintGuestBillingDetailJasperImpl.class).printDetail(new GuestBillingDetailReq().setExportFormat(format).setHedgingOnly(true));
		// PringTBalanceReportImpl, PringDailyReportImpl
//		byte[] reportData = SpringUtil.getBean(PringPkgReportImpl.class).print(format, DateUtil.offsetDay(DateUtil.date(), -1));//
//		byte[] reportData = SpringUtil.getBean(PringTBalanceReportImpl.class).print(format, LocalDate.now().plusDays(-1));//
		{
			reportData = folioService.getBytesPrint("2024120207005", exportType);//
			fileName = "客人账单";
		}
//		SpringUtil.getBean(PringTBalanceReportImpl.class).test();
//		System.out.println(reportData.clone());
		HttpHeaders headers = new HttpHeaders();
		if (exportType == ExportType.PDF) {
			headers.setContentType(MediaType.APPLICATION_PDF);
			typeName = ".pdf";
		} else if (exportType == ExportType.ZIPIMG) {
			headers.setContentType(MediaType.MULTIPART_FORM_DATA);
			typeName = ".zip";
		} else if (exportType == ExportType.HTML) {
			headers.setContentType(MediaType.TEXT_HTML);
			typeName = ".html";
		} else if (exportType == ExportType.XLS) {
			headers.setContentType(MediaType.MULTIPART_FORM_DATA);
			typeName = ".xls";
		}
		headers.setContentDispositionFormData("attachment", URLEncoder.encode(fileName, "utf-8") + typeName);
		return ResponseEntity.ok().headers(headers).body(reportData);
	}
//	@GetMapping("/generate")
//	public ResponseEntity<byte[]> generateReportf(@RequestParam(defaultValue = "pdf") String format) throws JRException, FileNotFoundException {
//	}

	public byte[] generateReport(String format) throws Exception {

//		BufferedReader bufferedReader = new BufferedReader(
//				new InputStreamReader(getClass().getResourceAsStream("/com/cw/service/config/print/templates/GuestBilling.jrxml"), "UTF-8"));
//		String line;
//		while ((line = bufferedReader.readLine()) != null) {
//			System.out.println(line);
//		}
		// 1. 加载报表模板
//		File file = ResourceUtils.getFile("classpath:templates/report_template.jrxml");
//		JasperReport jasperReport = JasperCompileManager.compileReport(file.getAbsolutePath());

		JasperReport jasperReport = JasperCompileManager.compileReport(getClass().getResourceAsStream("/com/cw/service/config/print/templates/GuestBilling.jrxml"));

		// 2. 模拟数据
		List<Map<String, Object>> dataList = new ArrayList<>();
		Map<String, Object> data1 = new HashMap<>();
		data1.put("name", "张三");
		data1.put("age", 30);
		dataList.add(data1);

		Map<String, Object> data2 = new HashMap<>();
		data2.put("name", "李四");
		data2.put("age", 25);
		dataList.add(data2);

		JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(dataList);

		// 3. 填充报表数据
		JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, new HashMap<>(), dataSource);

		// 4. 导出为不同格式
		if ("pdf".equalsIgnoreCase(format)) {
			return JasperExportManager.exportReportToPdf(jasperPrint);
		} else if ("html".equalsIgnoreCase(format)) {
			StringBuilder sb = new StringBuilder();

			HtmlExporter exporter = new HtmlExporter(DefaultJasperReportsContext.getInstance());

			exporter.setExporterInput(new SimpleExporterInput(jasperPrint));
			exporter.setExporterOutput(new SimpleHtmlExporterOutput(sb));

			exporter.exportReport();

			return sb.toString().getBytes();
		} else if ("xml".equalsIgnoreCase(format)) {
			return JasperExportManager.exportReportToXml(jasperPrint).getBytes();
		} else {
			throw new IllegalArgumentException("Unsupported report format: " + format);
		}
	}
}

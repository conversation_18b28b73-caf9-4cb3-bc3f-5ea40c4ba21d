package com.cw.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.cw.pojo.common.ResultJson;
import com.cw.service.config.cashier.impl.CashierTestServiceImpl;
import com.cw.service.context.GlobalContext;
import com.cw.utils.LoginJwtForm;
import com.cw.utils.SpringUtil;
import com.cw.utils.enums.EntranceType;

import cn.dev33.satoken.stp.StpUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/3/12 16:20
 **/
@Slf4j
@RestController
@RequestMapping(value = "/test", method = RequestMethod.POST)
public class TestController {

	public String login() throws Exception {
		LoginJwtForm form = new LoginJwtForm();
		form.setUserId("sys");
		form.setHotelId("001");
		form.setEntranceType(EntranceType.CONSOLEWEB);
		String token = com.cw.utils.JwtUtils.generateToken(form);
		StpUtil.setTokenValue(token, 5);
		System.out.println(GlobalContext.getCurrentHotelId());
		return token;
	}

	@RequestMapping(value = "/testNotify")
	public ResultJson output() {

		CashierTestServiceImpl cashierTestService = SpringUtil.getBean(CashierTestServiceImpl.class);
		// cashierTestService.testAssign();
		cashierTestService.testBatchAssignRoom();

		return ResultJson.ok();
	}

	/**
	 * 测试配置缓存
	 *
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/cashier", method = RequestMethod.GET)
	public ResultJson cashier() throws Exception {
		SpringUtil.getBean(com.cw.service.config.cashier.impl.CashierTestServiceImpl.class).test();
		log.info("test-temp");
		return ResultJson.ok();
	}

	@RequestMapping(value = "/report", method = RequestMethod.GET)
	public ResponseEntity<byte[]> report() throws Exception {
		login();
		return SpringUtil.getBean(com.cw.service.config.print.impl.ReportTestServiceImpl.class).test();
	}
}
